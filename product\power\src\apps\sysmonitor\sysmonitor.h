/**
 * @file     sysmonitor.h
 * @brief    This is a brief description.
 * @details  This is the detail description.
 * <AUTHOR>
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation
 * @par History:
 *   version: author, date, descn
 */

#ifndef _SYSMONITOR_H
#define _SYSMONITOR_H

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */



/*  Include   */
#include <linux/watchdog.h>
#include <sys/ioctl.h>
#include "plat_oss.h"
#include "plat_error.h"
#include "plat_fapi_history_events.h"
#include "plat_fapi_update.h"
#include "unitest.h"

/*  defines   */
#define MAINAPP "mainapp"        ///<  主进程名
#define WEB_SERVER_PROCESS   "lighttpd"        ///<  web服务器进程
#define WEB_CGI_PROCESS      "main.fcgi"        ///<  web后端cgi进程

#define PORC_HIS_STATE_FILE  PATH_DATA_WORK"sysmonitor/proc_his_state.bin"  ///<  进程历史状态文件
#define PROC_CONF_INCLUDE_SNMP_FILE  PATH_ROOT_SYS"proc_conf_include_snmp.xml"
#define PROC_CONF_EXCLUDE_SNMP_FILE  PATH_ROOT_SYS"proc_conf_exclude_snmp.xml"
#define PKG_LIST_NAME        "ftp_pkg_info.bin"
#define FTP_PKG_FILE         PATH_DATA_WORK"sysmonitor/"PKG_LIST_NAME      ///<  ftp目录升级包信息文件
#define PROC_D_RESET_REASON_FILE    "proc_d_reset.bin"
#define PROC_SOFTWARE_RESTART_SCRIPT  PATH_ROOT_SYS"restart_all_apps.sh" 

#define UPDATE_SUCCESS      0       ///<  升级成功
#define UPDATE_FAILURE      1       ///<  升级失败
#define UPDATE_NONE         2       ///<  无升级

#define PROC_SUCCESS        0       ///<  进程全部正常
#define PROC_FAILURE        1       ///<  进程异常
#define PROC_START          2       ///<  进程启动中

#define TIMER3_OUT  5000    ///<  定时器3定时时间
#define WATCHDOG_DEV_PATH "/dev/watchdog"   ///<  看门狗进程路径
#define WATCHDOG_TIMEOUT  600
/* typedefs   */

/**
 * 进程配置信息结构
 */
typedef struct process_info {
    str32   name;           ///<  进程名
    str128   arg;          ///<  进程启动需要的参数
    str256  full_name;      ///<  进程名全称(包含路径)
    unsigned int  die_timeout;    ///<  未收到心跳超时
    unsigned int  die_time;       ///<  未收到心跳计时
    unsigned int  die_cnt;        ///<  进程死机计数
    unsigned int  pulse_cnt;      ///<  进程心跳计数
    boolean wait_msg;       ///<  等待进程启动标志
    boolean is_pulse;       ///<  支持心跳机制标识
}process_info_t;

/**
 * 软件更新事件信息
 */
typedef struct update_event_info {
    int  type;           ///<  事件类型
    str128  package;        ///<  软件包名称
    str128  script;         ///<  升级脚本名称
    boolean need_bakup;     ///<  是否需要备份
    int  reset_code;     ///<  复位原因编码
}update_event_info_t;


/**
 * 软件升级状态信息
 */
#pragma pack(1)
typedef struct update_info {
    unsigned char  rollback_times;         ///<  回滚次数
    unsigned char  bakup;              ///<  备份成功标志
    unsigned char  update_status;          ///<  升级成功状态
    event_operator_t  updater_operator;                ///<  操作者信息
}update_info_t;
#pragma pack()

#pragma pack(1)
typedef struct ftp_pkg_info {
    char     name[64];          ///<  升级包名称
    time_t   chg_time;          ///<  文件修改时间
}ftp_pkg_info_t;
#pragma pack()

/**
 * 软件更新事件类型
 */
typedef enum update_event {
    ROLLBACK = 0,                   ///<  回滚事件
    UPDATE,                         ///<  升级事件
    BAKUP,                          ///<  备份事件
}update_event_e;


typedef enum high_resource_usage_event {
    HIGH_CPU = 0,
    HIGH_MEM,
}high_resource_usage_event_e;

/*    Global declaration */


/*   function declarations  */
int sysmonitor_proc_manage_init(void);
int sysmonitor_proc_manage_main(msgid msg_id,
                                             msgsn msg_sn,
                                             const void *msg,
                                             unsigned int msg_len,
                                             const mid sender);
int sysmonitor_prog_update_main(msgid msg_id,
                                             msgsn msg_sn,
                                             const void *msg,
                                             unsigned int msg_len,
                                             const mid sender);
update_event_info_t* get_event_info(update_event_e event_type);

int sysmonitor_prog_update_init(void);

int sysmonitor_ftp_dir_manage_init(void);
int sysmonitor_ftp_dir_manage_main(msgid msg_id,
                        msgsn msg_sn,
                        const void *msg,
                        unsigned int msg_len,
                        const mid sender);
void ftp_init_for_ut(void);
boolean judge_process_alive(char *proc_name, int *pid);
void func_only_for_unitest(int num);
int handle_process_exception(unsigned int die_cnt, char *proc_name);
int del_proc_d_status_reset_file(void);

#ifdef UNITEST
STATIC int initialize_watchdog(void);
STATIC int feed_watchdog(void);
STATIC int close_watchdog(void);
STATIC void reg_sigterm(void);
STATIC int set_watchdog_timeout(int timeout);
STATIC int check_system_resource_usage(void);
STATIC int restart_all_apps(void);
STATIC int save_high_resources_event(int resource_id);
STATIC boolean is_start_system_resource_check(void);
STATIC void start_process(process_info_t *proc);
STATIC int kill_process(process_info_t *proc);
STATIC void get_reinforce_log(char* version, char* details);
#endif

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif  //  _SYSMONITOR_H
