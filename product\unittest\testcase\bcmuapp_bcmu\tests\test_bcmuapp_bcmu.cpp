/**
 * @file     test_bcmuapp_bcmu.cpp
 * @brief    test_bcmuapp_bcmu.cpp
 * @details  test_bcmuapp_bcmu.cpp
 * <AUTHOR> 
 * @date     2017-03-20
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 

/*  includes  */
#include <mockcpp/mokc.h>
#include "gtest/gtest.h"
#include "data_type.h"
#include "sids.h"
#include "plat_data_access.h"
#include "bcmu_business.h"
#include "data_access.h"
#include "mock_bcmuapp_bcmu.h"
#include "bmu_business.h"
#include "bmu_soc.h"

extern "C"
{
    extern char s_last_bcmu_fire_alm_state[3];
    extern int s_mock_fire_alm[3];
    extern bcmu_ana_data_t s_bcmu_ana_data;
    extern bcmu_dig_data_t s_bcmu_dig_data;
    extern bcmu_alm_data_t s_bcmu_alm_data;
    extern bcmu_alm_data_t s_last_bcmu_alm_data;
    extern float s_cell_volt[BMU_NUM][BMU_CELL_NUM];
    extern outlier_info_t s_outliers[MAX_OUTLIERS];
    extern int s_outlier_count;
    extern cell_history_t s_outlier_history[TOTAL_CELLS];
    extern outlier_info_t s_last_outliers[MAX_OUTLIERS];
    extern float s_cell_soc[BMU_NUM][BMU_CELL_NUM];
    extern float s_cell_avg_soc;
    extern float s_cell_avg_volt;
    extern float s_cell_soc_dev_fabs[TOTAL_CELLS];
    extern float s_cell_volt_dev[TOTAL_CELLS];
    extern int bmu_soh_data_syned[BMU_NUM];
    extern float s_ocv_temp_soc_tab[OCV_TAB_SIZE_TEMP_NUM][OCV_TAB_SIZE_SOC_NUM];
    extern short s_ocv_temp_map[OCV_TAB_SIZE_TEMP_NUM];
    extern int s_mock_flood_alm_sta;
    extern float s_mock_charge_max_current;
    extern int s_mock_cell_max_voltage;

    extern int deal_bcmu_business(void);
    extern int update_cut_off_status(void);
    extern int update_prt_status(void);
    extern int restore_bcmu_no_backlash_alm(void);
    extern int update_bcmu_battery_status(void);
    extern int bcmu_ctrl(void);
    extern int bcmu_do_relay_msg_output(void);
    extern int save_bcmu_his_event(char* str_cause, char* str_event);
    extern int bcmu_stop_liquid_cool_ctrl(boolean ctrl);
    extern int bcmu_pcs_energ_stop_ctrl(void);
    extern int bcmu_fire_action_alm_ctrl(void);
    extern int bcmu_flood_alm_ctrl(void);
    extern float calc_discharging_rate(float temp,float soc);
    extern int calc_max_charge_current(void);
    extern float update_charging_rate(float current_charge_rate);
    extern int is_valid(float value);
    extern int exe_opt_para_asy_cmd(sid sig_id);
    extern float optimizer_on_update_charge_rate(int dev_sn, float charging_rate_tmp);
    extern int update_cell_outliers_status(void);
    extern int should_calculate_outliers(void);
    extern int load_bmu_cell_volt(float (*voltages)[BMU_CELL_NUM]);
    extern int judge_cell_outliers1(float (*voltages)[BMU_CELL_NUM]);
    extern int remove_extreme_voltages(float (*voltages)[BMU_CELL_NUM], int *valid_cells, float mean, int remove_count);
    extern float calculate_std_dev(float (*voltages)[BMU_CELL_NUM], int *valid_cells, float mean);
    extern float calculate_mean(float (*voltages)[BMU_CELL_NUM], int *valid_cells);
    extern int calculate_z_scores(float (*voltages)[BMU_CELL_NUM], float mean, float std_dev, float *z_scores);
    extern int update_outliers(float (*voltages)[BMU_CELL_NUM], float mean, float *z_scores, cell_history_t *outlier_history, outlier_info_t *outliers, int *outlier_count);
    extern int save_outliers_info(void);
    extern int should_calculate_outliers2(void);
    extern int judge_cell_outliers2(float (*voltages)[BMU_CELL_NUM]);
    extern int calculate_cell_soc_volt_dev(float (*voltages)[BMU_CELL_NUM]);
    extern int sort_soc_dev_fabs(int *sorted_indices);
    extern int is_cell_volt_outlier_pos_change(void);
    extern int judge_sys_time_valid(void);
    extern int get_battery_capacity();
    extern int update_power_data();
    extern float calculate_max_power(enum OperationType op_type);
    extern int do_replace_finished_judge(void);
    extern int get_soc_from_ocv_table(float temperature, float volt, ocv_area_t *result);
    extern unsigned int get_point_index_by_median_lookup(float* array, unsigned int array_size, float value);
    int get_cutoff_status_by_sid( sid cutoff_sta_sid);
    int judge_cell_cutoff_status(int_type_t* cutoff_alm_val);
    int step_constant_power_charging(float charging_rate, float charging_current_pcs);
    extern int set_bcmu_business_period(void);
}

class ENV : public testing::Environment {
 public:
    virtual void SetUp() {
        // SetUp run once before all testcases
        // you can add mock group here
    }
    virtual void TearDown() {
        // TearDown run once before all testcases
    }
};

class bcmuapp_bcmu : public testing::Test {
 protected:
    static void SetUpTestCase() {
        // SetUpTestCase run once before testcase groups named by bcmuapp_bcmu
    }

    static void TearDownTestCase() {
        // TearDownTestCase run once before testcase groups named by bcmuapp_bcmu
    }

    virtual void SetUp() {
        // SetUp run before every testcase of group bcmuapp_bcmu
        s_bcmu_dig_data = {0};
    }

    virtual void TearDown() {
        // SetUp run before every testcase of group bcmuapp_bcmu
    }

    // Some expensive resource shared by all tests.
    // int share_variable ;
};

// init class bcmuapp_bcmu variable here
// int bcmuapp_bcmu::share_variable = 0;

// add testcase below

/* Started by AICoder, pid:o8ea1dda07z989f143200b2a906bb225c582cda6 */
TEST_F(bcmuapp_bcmu, TimerInitializationSuccess) {
    // 测试 bcmuapp_bcmu_init 函数是否成功初始化定时器
    // 使用MOCKER宏对 set_timer 进行打桩
    MOCKER(set_timer)
    .stubs()
    .will(returnValue(SUCCESS));
    EXPECT_EQ(bcmuapp_bcmu_init(), SUCCESS);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, TimerInitializationFailure) {
    // 重新打桩 set_timer 函数使其返回 FAILURE
    MOCKER(set_timer)
        .stubs()
        .will(returnValue(FAILURE));

    // 测试 bcmuapp_bcmu_init 函数在定时器初始化失败时的行为
    EXPECT_EQ(bcmuapp_bcmu_init(), FAILURE);

    // 清理打桩
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:o8ea1dda07z989f143200b2a906bb225c582cda6 */

TEST_F(bcmuapp_bcmu, deal_bcmu_business)
{
    int result = deal_bcmu_business();
    EXPECT_EQ(result, SUCCESS);
    GlobalMockObject::verify();
}


TEST_F(bcmuapp_bcmu, bcmuapp_bcmu_main)
{
    int result = 2;
    msgid msg_id = TIMER1_MSG;
    result = bcmuapp_bcmu_main(msg_id, 0, NULL, 0, "ABC");
    EXPECT_EQ(result, SUCCESS);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, bcmuapp_bcmu_main_timer5)
{
    int result = 2;
    msgid msg_id = TIMER5_MSG;
    result = bcmuapp_bcmu_main(msg_id, 0, NULL, 0, "ABC");
    EXPECT_EQ(result, SUCCESS);
    GlobalMockObject::verify();
}
/* Started by AICoder, pid:bb77bj59c8q529a1469009102095b564d2c56c65 */
TEST_F(bcmuapp_bcmu, judge_sys_time_valid)
{
    int result;
    result = judge_sys_time_valid();
    EXPECT_EQ(result, SUCCESS);
    GlobalMockObject::verify(); // Assuming this is part of your mocking framework
}

TEST_F(bcmuapp_bcmu, init_sys_time_error)
{
    MOCKER(check_common_time)
        .stubs()
        .will(returnValue(0));
    int result;
    result = judge_sys_time_valid();
    EXPECT_EQ(result, FAILURE);
    GlobalMockObject::verify(); // Assuming this is part of your mocking framework
}
/* Ended by AICoder, pid:bb77bj59c8q529a1469009102095b564d2c56c65 */

/* Started by AICoder, pid:i2a7cn4518k1b3e140240bf0b05f043a7bf560ed */

TEST_F(bcmuapp_bcmu, update_cut_off_status_NormalOperation) {
    EXPECT_EQ(update_cut_off_status(), SUCCESS);
}

TEST_F(bcmuapp_bcmu, update_cut_off_status_SomeBMUNotExist) {
    // 使用MOCKER宏打桩 is_bmu_exist 函数，使其总是返回 FAULT
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(FAULT));

    EXPECT_EQ(update_cut_off_status(), SUCCESS);
    EXPECT_EQ(s_bcmu_dig_data.module_vol_high_cut_off_sta.i_value, 0); // 由于所有模块都不存在，所以状态应为0
    EXPECT_EQ(s_bcmu_dig_data.module_vol_low_cut_off_sta.i_value, 0);

    // 清理打桩
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:i2a7cn4518k1b3e140240bf0b05f043a7bf560ed */

/* Started by AICoder, pid:l8581hdec44cb841481e0b2cc0b8ad67c1f15a9f */
TEST_F(bcmuapp_bcmu, UpdatePrtStatusNormalOperation) {
    bmu_alm_data_t bmu_alm_data[BMU_NUM] = {0};
    for (int i = 0; i < BMU_NUM; ++i) {
        bmu_alm_data[i].bmu_comm_fail_alm.i_value = NORMAL;
        bmu_alm_data[i].bmu_term_high_alm.i_value = NORMAL;
        for (int j = 0; j < BMU_CELL_TEMP_NUM; ++j) {
            bmu_alm_data[i].cell_temp_sample_fault[j].i_value = NORMAL;
        }
    }
    set_bmu_alm_data(&bmu_alm_data[0]);
    EXPECT_EQ(update_prt_status(), SUCCESS);
}

TEST_F(bcmuapp_bcmu, UpdatePrtStatusCommunicationFailure) {
    bmu_alm_data_t bmu_alm_data[BMU_NUM] = {0};
    for (int i = 0; i < BMU_NUM; ++i) {
        bmu_alm_data[i].bmu_comm_fail_alm.i_value = NORMAL;
        bmu_alm_data[i].bmu_term_high_alm.i_value = NORMAL;
        for (int j = 0; j < BMU_CELL_TEMP_NUM; ++j) {
            bmu_alm_data[i].cell_temp_sample_fault[j].i_value = NORMAL;
        }
    }
    set_bmu_alm_data(&bmu_alm_data[0]);
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(FAULT));
    EXPECT_EQ(update_prt_status(), SUCCESS);
}

TEST_F(bcmuapp_bcmu, UpdatePrtStatusAbnormalStatus) {
    bmu_alm_data_t bmu_alm_data[BMU_NUM] = {0};
    for (int i = 0; i < BMU_NUM; ++i) {
        bmu_alm_data[i].bmu_comm_fail_alm.i_value = NORMAL;
        bmu_alm_data[i].bmu_term_high_alm.i_value = ABNORMAL;
        for (int j = 0; j < BMU_CELL_TEMP_NUM; ++j) {
            bmu_alm_data[i].cell_temp_sample_fault[j].i_value = NORMAL;
        }
    }
    set_bmu_alm_data(&bmu_alm_data[0]);
    EXPECT_EQ(update_prt_status(), SUCCESS);
}

TEST_F(bcmuapp_bcmu, UpdatePrtStatusCellTemperatureAlarm) {
    bmu_alm_data_t bmu_alm_data[BMU_NUM] = {0};
    for (int i = 0; i < BMU_NUM; ++i) {
        bmu_alm_data[i].bmu_comm_fail_alm.i_value = NORMAL;
        bmu_alm_data[i].bmu_term_high_alm.i_value = NORMAL;
        for (int j = 0; j < BMU_CELL_TEMP_NUM; ++j) {
            bmu_alm_data[i].cell_temp_sample_fault[j].i_value = (j == 0) ? ABNORMAL : NORMAL;
        }
    }
    set_bmu_alm_data(&bmu_alm_data[0]);
    EXPECT_EQ(update_prt_status(), SUCCESS);
}

TEST_F(bcmuapp_bcmu, UpdatePrtStatusSomeBMUNotExist) {
    bmu_alm_data_t bmu_alm_data[BMU_NUM] = {0};
    for (int i = 0; i < BMU_NUM; ++i) {
        bmu_alm_data[i].bmu_comm_fail_alm.i_value = NORMAL;
        bmu_alm_data[i].bmu_term_high_alm.i_value = NORMAL;
        for (int j = 0; j < BMU_CELL_TEMP_NUM; ++j) {
            bmu_alm_data[i].cell_temp_sample_fault[j].i_value = NORMAL;
        }
    }
    set_bmu_alm_data(&bmu_alm_data[0]);
    MOCKER(is_bmu_exist)
        .stubs()
        .with(any())
        .will(returnValue(FAULT));
    EXPECT_EQ(update_prt_status(), SUCCESS);
}
/* Ended by AICoder, pid:l8581hdec44cb841481e0b2cc0b8ad67c1f15a9f */

/* Started by AICoder, pid:r7f7fqdb1dd16f2141de0b2bc001074a38a9d2c4 */
TEST_F(bcmuapp_bcmu, restore_bcmu_no_backlash_alm) {
    s_last_bcmu_alm_data.chg_curr_alm_lv1.i_value = 1;
    s_last_bcmu_alm_data.chg_curr_alm_lv2.i_value = 1;
    s_last_bcmu_alm_data.chg_curr_alm_lv3.i_value = 1;
    s_last_bcmu_alm_data.dischg_curr_alm_lv1.i_value = 1;
    s_last_bcmu_alm_data.dischg_curr_alm_lv2.i_value = 1;
    s_last_bcmu_alm_data.dischg_curr_alm_lv3.i_value = 1;

    s_bcmu_alm_data.chg_curr_alm_lv1.i_value = 0;
    s_bcmu_alm_data.chg_curr_alm_lv2.i_value = 0;
    s_bcmu_alm_data.chg_curr_alm_lv3.i_value = 0;
    s_bcmu_alm_data.dischg_curr_alm_lv1.i_value = 0;
    s_bcmu_alm_data.dischg_curr_alm_lv2.i_value = 0;
    s_bcmu_alm_data.dischg_curr_alm_lv3.i_value = 0;

    EXPECT_EQ(restore_bcmu_no_backlash_alm(), SUCCESS);
    EXPECT_EQ(s_last_bcmu_alm_data.chg_curr_alm_lv1.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.chg_curr_alm_lv2.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.chg_curr_alm_lv3.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.dischg_curr_alm_lv1.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.dischg_curr_alm_lv2.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.dischg_curr_alm_lv3.i_value, 0);

    s_bcmu_alm_data.chg_curr_alm_lv1.i_value = 1;
    s_bcmu_alm_data.chg_curr_alm_lv2.i_value = 0;
    s_bcmu_alm_data.chg_curr_alm_lv3.i_value = 0;
    s_bcmu_alm_data.dischg_curr_alm_lv1.i_value = 1;
    s_bcmu_alm_data.dischg_curr_alm_lv2.i_value = 0;
    s_bcmu_alm_data.dischg_curr_alm_lv3.i_value = 0;

    EXPECT_EQ(restore_bcmu_no_backlash_alm(), SUCCESS);
    EXPECT_EQ(s_last_bcmu_alm_data.chg_curr_alm_lv1.i_value, 1);
    EXPECT_EQ(s_last_bcmu_alm_data.chg_curr_alm_lv2.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.chg_curr_alm_lv3.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.dischg_curr_alm_lv1.i_value, 1);
    EXPECT_EQ(s_last_bcmu_alm_data.dischg_curr_alm_lv2.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.dischg_curr_alm_lv3.i_value, 0);

    s_bcmu_alm_data.chg_curr_alm_lv1.i_value = 0;
    s_bcmu_alm_data.chg_curr_alm_lv2.i_value = 1;
    s_bcmu_alm_data.chg_curr_alm_lv3.i_value = 0;
    s_bcmu_alm_data.dischg_curr_alm_lv1.i_value = 0;
    s_bcmu_alm_data.dischg_curr_alm_lv2.i_value = 1;
    s_bcmu_alm_data.dischg_curr_alm_lv3.i_value = 0;

    EXPECT_EQ(restore_bcmu_no_backlash_alm(), SUCCESS);
    EXPECT_EQ(s_last_bcmu_alm_data.chg_curr_alm_lv1.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.chg_curr_alm_lv2.i_value, 1);
    EXPECT_EQ(s_last_bcmu_alm_data.chg_curr_alm_lv3.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.dischg_curr_alm_lv1.i_value, 0);
    EXPECT_EQ(s_last_bcmu_alm_data.dischg_curr_alm_lv2.i_value, 1);
    EXPECT_EQ(s_last_bcmu_alm_data.dischg_curr_alm_lv3.i_value, 0);

    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:r7f7fqdb1dd16f2141de0b2bc001074a38a9d2c4 */

TEST_F(bcmuapp_bcmu, judge_bcmu_battery_status) {
    int bcmu_battery_status = 0;
    float current = 3.0f;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT, &current, sizeof(float), type_float);
    update_bcmu_battery_status();
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(int));
    EXPECT_EQ(bcmu_battery_status, 1);

    current = -1.0f;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT, &current, sizeof(float), type_float);
    update_bcmu_battery_status();
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(int));
    EXPECT_EQ(bcmu_battery_status, 0);

    current = -3.0f;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT, &current, sizeof(float), type_float);
    update_bcmu_battery_status();
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(int));
    EXPECT_EQ(bcmu_battery_status, 2);
}

/* Started by AICoder, pid:i5f99c70906dfae14923086170d6724eb8a06789 */
TEST_F(bcmuapp_bcmu, SOCAndTempBelowTableValues) {
    float ret = 0;
    float soc_value = 50.0f;
    float temp_value = -8.0f;
    float discharge_max_current = 0.0f;

    ret = calc_discharging_rate(temp_value,soc_value);
    EXPECT_FLOAT_EQ(ret, 0);

    temp_value = 65.0f;
    ret = calc_discharging_rate(temp_value,soc_value);
    EXPECT_FLOAT_EQ(ret, 0);
}

TEST_F(bcmuapp_bcmu, ValidTemperatureAndSOC) {
    float ret = 0;
    float soc_value = 53.0f;
    float temp_value = 35.0f;
    float discharge_max_current = 0.0f;

    ret = calc_discharging_rate(temp_value,soc_value);
    EXPECT_FLOAT_EQ(ret, 314 * 0.5);

    soc_value = 96.0f;
    temp_value = 60.0f;
    ret = calc_discharging_rate(temp_value,soc_value);
    EXPECT_FLOAT_EQ(ret, 314 * 0.2);
}
/* Ended by AICoder, pid:i5f99c70906dfae14923086170d6724eb8a06789 */
/* Started by AICoder, pid:68e75605fcu36eb149800a390012248c8350407d */
TEST_F(bcmuapp_bcmu, calc_max_charge_current_disabled) {
    int charge_map_enabled = 0;
    int ret = 0;
    float cell_temp = 0.0f;
    int batt_soc = 0;
    float charging_max_current = 0.0f;
    sid sid_temp_enable = SID_SET_DEV_SN(1, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CHARGE_MAP_ENABLE);
    sid max_sid = SID_SET_DEV_SN(1, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_CELL_TEMPERATURE_MAX);
    sid min_sid = SID_SET_DEV_SN(1, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_CELL_TEMPERATURE_MIN);
    
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CHARGE_MAP_ENABLE, &charge_map_enabled, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_MODULE_ANA_BMU_CELL_TEMPERATURE, &cell_temp, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOC, &batt_soc, sizeof(int), type_int);

    ret = calc_max_charge_current();
    EXPECT_EQ(ret, 0);
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &charging_max_current, sizeof(charging_max_current));
    EXPECT_FLOAT_EQ(charging_max_current, 157);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, calc_max_charge_current_enabled) {
    int charge_map_enabled = 1;
    int ret = 0;
    int bmu_comm_state = 1;
    float cell_temp = -1.0f;
    int batt_soc = 0;
    float charging_max_current = 0.0f;
    
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CHARGE_MAP_ENABLE, &charge_map_enabled, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_MODULE_ANA_BMU_CELL_TEMPERATURE, &cell_temp, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOC, &batt_soc, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_MODULE_DIG_BMU_COMMUNICATION_STATUS, &bmu_comm_state, sizeof(int), type_int);
    EXPECT_EQ(calc_max_charge_current(), 0);

    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &charging_max_current, sizeof(charging_max_current));
    EXPECT_FLOAT_EQ(charging_max_current, 0);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, calc_max_charge_current_enabled_range1) {
    int charge_map_enabled = 1;
    float cell_temp = 0.0f;
    int batt_soc = 0;
    float charging_max_current = 0.0f;
    int bmu_comm_state = 1;
    
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CHARGE_MAP_ENABLE, &charge_map_enabled, sizeof(float), type_int);
    pdt_set_data(SID_BATTERY_MODULE_ANA_BMU_CELL_TEMPERATURE, &cell_temp, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOC, &batt_soc, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_MODULE_DIG_BMU_COMMUNICATION_STATUS, &bmu_comm_state, sizeof(int), type_int);

    EXPECT_EQ(calc_max_charge_current(), 0);
    
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &charging_max_current, sizeof(charging_max_current));
    EXPECT_FLOAT_EQ(charging_max_current, 15.7);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, calc_max_charge_current_enabled_range2) {
    int charge_map_enabled = 1;
    float cell_temp = 5.0f;
    int batt_soc = 0;
    float charging_max_current = 0.0f;
    
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CHARGE_MAP_ENABLE, &charge_map_enabled, sizeof(float), type_int);
    pdt_set_data(SID_BATTERY_MODULE_ANA_BMU_CELL_TEMPERATURE, &cell_temp, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOC, &batt_soc, sizeof(int), type_int);

    EXPECT_EQ(calc_max_charge_current(), 0);
    
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &charging_max_current, sizeof(charging_max_current));
    EXPECT_FLOAT_EQ(charging_max_current, 31.4);
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:68e75605fcu36eb149800a390012248c8350407d */

/* Started by AICoder, pid:rb499v6edcm923414a8309c410eac90e27e4eb48 */
TEST_F(bcmuapp_bcmu, is_valid) {
    EXPECT_TRUE(is_valid(0.1f));
    EXPECT_FALSE(is_valid(CHARGE_RATE_INVALID));
}
/* Ended by AICoder, pid:rb499v6edcm923414a8309c410eac90e27e4eb48 */

/* Started by AICoder, pid:p13f2g1db3g4e471405e09a970d40c2d25f0bc58 */
TEST_F(bcmuapp_bcmu, test_update_charging_rate_cell_voltage_below_threshold) {
    int cell_max_voltage = 0;
    MOCKER(pdt_get_data)
        .stubs()
        .with(any(), outBoundP((void *)&cell_max_voltage, sizeof(float)), eq(sizeof(float)))
        .will(returnValue(0));
    EXPECT_FLOAT_EQ(update_charging_rate(0.5f), 0.5f);
    EXPECT_FLOAT_EQ(update_charging_rate(0.2f), 0.2f);
    EXPECT_FLOAT_EQ(update_charging_rate(0.1f), 0.1f);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, test_update_charging_rate_cell_voltage_above_threshold) {
    int cell_max_voltage = 3500;
    MOCKER(pdt_get_data)
        .stubs()
        .with(any(), outBoundP((void *)&cell_max_voltage, sizeof(float)), eq(sizeof(float)))
        .will(returnValue(0));
    EXPECT_FLOAT_EQ(update_charging_rate(0.5f), 0.2f);
    EXPECT_FLOAT_EQ(update_charging_rate(0.2f), 0.1f);
    EXPECT_FLOAT_EQ(update_charging_rate(0.25f), 0.1f);
    EXPECT_FLOAT_EQ(update_charging_rate(0.1f), 0.05f);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, test_update_charging_rate_other_values) {
    int cell_max_voltage = 3500;
    MOCKER(pdt_get_data)
        .stubs()
        .with(any(), outBoundP((void *)&cell_max_voltage, sizeof(float)), eq(sizeof(float)))
        .will(returnValue(0));
    EXPECT_FLOAT_EQ(update_charging_rate(0.3f), 0.3f);
    EXPECT_FLOAT_EQ(update_charging_rate(0.0f), 0.0f);
    EXPECT_FLOAT_EQ(update_charging_rate(1.0f), 1.0f);
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:p13f2g1db3g4e471405e09a970d40c2d25f0bc58 */

/* Started by AICoder, pid:o8c11jef9c908eb148280b0b3065231dacb4fdcb */
TEST_F(bcmuapp_bcmu, test_device_optimizer) {
    sid sig_id = SID_OPTIMIZER_ANA_OPTIMIZER_OUTPUT_CURRENT;
    EXPECT_EQ(exe_opt_para_asy_cmd(sig_id), SUCCESS);
}

TEST_F(bcmuapp_bcmu, test_not_device_optimizer) {
    sid sig_id = SID_BATTERY_MODULE_DEVINFO_BMU_RELEASE_DATE;
    EXPECT_EQ(exe_opt_para_asy_cmd(sig_id), FAILURE);
}
/* Ended by AICoder, pid:o8c11jef9c908eb148280b0b3065231dacb4fdcb */

/* Started by AICoder, pid:ec7dfhec7757b0f149d8089370508d34f12826c9 */
TEST_F(bcmuapp_bcmu, TestOptimizerOnUpdateChargeRate_SuccessWithDiffCurrGreaterThan5) {
    int dev_sn = 1;
    float charging_rate_tmp = 0.2f;
    float opt_output_current = 25.0f;
    sid sid_temp = SID_SET_DEV_SN_SIG_VINDEX(SID_OPTIMIZER_ANA_OPTIMIZER_OUTPUT_CURRENT, dev_sn, 1);

    pdt_set_data(sid_temp, &opt_output_current, sizeof(float), type_float);

    float result = optimizer_on_update_charge_rate(dev_sn, charging_rate_tmp);
    EXPECT_FLOAT_EQ(result, charging_rate_tmp - opt_output_current / DEFAULT_CAPACITY);
}

TEST_F(bcmuapp_bcmu, TestOptimizerOnUpdateChargeRate_SuccessWithDiffCurrLessThan5) {
    int dev_sn = 1;
    float charging_rate_tmp = 0.05f;
    float opt_output_current = 25.0f; 
    sid sid_temp = SID_SET_DEV_SN_SIG_VINDEX(SID_OPTIMIZER_ANA_OPTIMIZER_OUTPUT_CURRENT, dev_sn, 1);
    sid para_sid = SID_SET_DEV_SN(dev_sn, SID_OPTIMIZER_PARA_OPTIMIZER_OUTPUT_SET_CURRENT);

    pdt_set_data(sid_temp, &opt_output_current, sizeof(float), type_float);

    float result = optimizer_on_update_charge_rate(dev_sn, charging_rate_tmp);
    EXPECT_FLOAT_EQ(result, 5.0f / DEFAULT_CAPACITY);
}

/* Ended by AICoder, pid:ec7dfhec7757b0f149d8089370508d34f12826c9 */

/* Started by AICoder, pid:15731f24c48047ed93b1e9817e76fe3a */

// 假设的宏定义和结构体定义
// 测试用例

TEST_F(bcmuapp_bcmu, AllBMUsExistAndCommNormal) {
    EXPECT_EQ(should_calculate_outliers(), TRUE);
}

TEST_F(bcmuapp_bcmu, SomeBMUsDoNotExist) {
    MOCKER(is_bmu_exist).stubs().will(returnValue(FAULT));
    MOCKER(is_bmu_comm_normal).stubs().will(returnValue(NORMAL));

    EXPECT_EQ(should_calculate_outliers(), FALSE);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, SomeBMUsCommunicationFails) {
    MOCKER(is_bmu_exist).stubs().will(returnValue(NORMAL));
    MOCKER(is_bmu_comm_normal).stubs().will(returnValue(FAULT));

    EXPECT_EQ(should_calculate_outliers(), FALSE);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, SocOutOfHighRange) {
    // 设置SOC值超出范围/
   
    int cluster_soc = 96;
    float soc_nonplat_high_thre = 95;
    float soc_nonplat_low_thre = 5;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &cluster_soc,sizeof(cluster_soc), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_HIGH_THRESHOLD, &soc_nonplat_high_thre,sizeof(soc_nonplat_high_thre), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_LOW_THRESHOLD, &soc_nonplat_low_thre,sizeof(soc_nonplat_low_thre), type_float);

    EXPECT_EQ(should_calculate_outliers(), TRUE);
}

TEST_F(bcmuapp_bcmu, SocInOfRange) {
   
    int cluster_soc = 50;
    float soc_nonplat_high_thre = 95;
    float soc_nonplat_low_thre = 5;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &cluster_soc,sizeof(cluster_soc), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_HIGH_THRESHOLD, &soc_nonplat_high_thre,sizeof(soc_nonplat_high_thre), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_LOW_THRESHOLD, &soc_nonplat_low_thre,sizeof(soc_nonplat_low_thre), type_float);

    EXPECT_EQ(should_calculate_outliers(),  FALSE);
}

TEST_F(bcmuapp_bcmu, SocOutOfLowRange) {
    // 设置SOC值超出范围/
   
    int cluster_soc = 3;
    float soc_nonplat_high_thre = 95;
    float soc_nonplat_low_thre = 5;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &cluster_soc,sizeof(cluster_soc), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_HIGH_THRESHOLD, &soc_nonplat_high_thre,sizeof(soc_nonplat_high_thre), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_LOW_THRESHOLD, &soc_nonplat_low_thre,sizeof(soc_nonplat_low_thre), type_float);

    EXPECT_EQ(should_calculate_outliers(), TRUE);
}

/* Ended by AICoder, pid:15731f24c48047ed93b1e9817e76fe3a */

TEST_F(bcmuapp_bcmu, OptExit) {
    sid sid_temp = 0;
    int opt_exit_state = 1;
    int cluster_soc = 3;
    float soc_nonplat_high_thre = 95;
    float soc_nonplat_low_thre = 5;

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &cluster_soc,sizeof(cluster_soc), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_HIGH_THRESHOLD, &soc_nonplat_high_thre,sizeof(soc_nonplat_high_thre), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_LOW_THRESHOLD, &soc_nonplat_low_thre,sizeof(soc_nonplat_low_thre), type_float);

    for ( int i = 0; i < OPT_NUM; ++i)
    {
        sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_EXIST_STATE);
        pdt_set_data(sid_temp, &opt_exit_state, sizeof(opt_exit_state), type_int);
    }
    EXPECT_EQ(should_calculate_outliers(), TRUE);
}

TEST_F(bcmuapp_bcmu, OptCommNormal) {
    sid sid_temp = 0;
    int opt_exit_state = 1;
    int opt_comm_state = 0;
    int cluster_soc = 3;
    float soc_nonplat_high_thre = 95;
    float soc_nonplat_low_thre = 5;

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &cluster_soc,sizeof(cluster_soc), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_HIGH_THRESHOLD, &soc_nonplat_high_thre,sizeof(soc_nonplat_high_thre), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_LOW_THRESHOLD, &soc_nonplat_low_thre,sizeof(soc_nonplat_low_thre), type_float);

    for ( int i = 0; i < OPT_NUM; ++i)
    {
        sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_EXIST_STATE);
        pdt_set_data(sid_temp, &opt_exit_state, sizeof(opt_exit_state), type_int);
        sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_COMMUNICATION_STATUS);
        pdt_set_data(sid_temp, &opt_comm_state, sizeof(opt_comm_state), type_int);
    }
    EXPECT_EQ(should_calculate_outliers(), TRUE);
}

TEST_F(bcmuapp_bcmu, OptRunNormal) {
    sid sid_temp = 0;
    int opt_exit_state = 1;
    int opt_comm_state = 0;
    int opt_run_state = 1;
    int cluster_soc = 3;
    float soc_nonplat_high_thre = 95;
    float soc_nonplat_low_thre = 5;

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &cluster_soc,sizeof(cluster_soc), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_HIGH_THRESHOLD, &soc_nonplat_high_thre,sizeof(soc_nonplat_high_thre), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_LOW_THRESHOLD, &soc_nonplat_low_thre,sizeof(soc_nonplat_low_thre), type_float);

    for ( int i = 0; i < OPT_NUM; ++i)
    {
        sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_EXIST_STATE);
        pdt_set_data(sid_temp, &opt_exit_state, sizeof(opt_exit_state), type_int);
        sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_COMMUNICATION_STATUS);
        pdt_set_data(sid_temp, &opt_comm_state, sizeof(opt_comm_state), type_int);
        sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_RUN_STATUS);
        pdt_set_data(sid_temp, &opt_run_state, sizeof(opt_run_state), type_int);
    }
    EXPECT_EQ(should_calculate_outliers(), FALSE);
}




/* Started by AICoder, pid:l7c6dx30a2c8d3c140680bab706c6032621531ee */
TEST_F(bcmuapp_bcmu, CellOutlierNormalCase) {
    // 设置边界条件，例如所有电芯电压相同
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            s_cell_volt[i][j] = 3.7;
        }
    }
    int ret = judge_cell_outliers1(s_cell_volt);
    EXPECT_EQ(ret, FAILURE);
}

TEST_F(bcmuapp_bcmu, CellOutlierAbnormalCase) {
    // 设置异常条件，例如部分电芯电压极高或极低
    for (int i = 0; i < 4; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            s_cell_volt[i][j] = 3.7 + (rand() % 100) / 100.0;
        }
    }
    // 设置一些极端值
    s_cell_volt[0][0] = 7.5;
    s_cell_volt[1][1] = 2.5;
    int ret = judge_cell_outliers1(s_cell_volt);
    EXPECT_EQ(ret, SUCCESS);
    // 验证离群电芯的数量和内容
    //EXPECT_GT(s_outlier_count, 0);
}
/* Ended by AICoder, pid:l7c6dx30a2c8d3c140680bab706c6032621531ee */

/* Started by AICoder, pid:vfadah9da68b0b7141ac0b3c800596520182fe8a */
TEST_F(bcmuapp_bcmu, RemoveExtremeVoltagesNormalCase) {
    int remove_count = 10;
    int valid_cells[TOTAL_CELLS];
    float mean = 0.0;
    for (int i = 0; i < TOTAL_CELLS; ++i) {
        valid_cells[i] = i;
    }

    mean = calculate_mean(s_cell_volt, valid_cells);

    int ret = remove_extreme_voltages(s_cell_volt, valid_cells, mean, remove_count);
    EXPECT_EQ(ret, SUCCESS);
    // Check if exactly 'remove_count' cells are marked as invalid
    int invalid_count = 0;
    for (int i = 0; i < TOTAL_CELLS; ++i) {
        if (valid_cells[i] == -1) {
            invalid_count++;
        }
    }
    EXPECT_EQ(invalid_count, remove_count);
    EXPECT_EQ(-1, valid_cells[0]);
    EXPECT_EQ(1, valid_cells[1]);
    EXPECT_EQ(2, valid_cells[2]);
}

TEST_F(bcmuapp_bcmu, RemoveExtremeVoltagesBoundaryCase) {
    int remove_count = 0; // No cells should be removed
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < TOTAL_CELLS; ++i) {
        valid_cells[i] = i;
    }
    float mean = calculate_mean(s_cell_volt, valid_cells);
    int ret = remove_extreme_voltages(s_cell_volt, valid_cells, mean, remove_count);
    EXPECT_EQ(ret, SUCCESS);
    // Check if no cells are marked as invalid
    for (int i = 0; i < TOTAL_CELLS; ++i) {
        EXPECT_NE(valid_cells[i], -1);
    }
}

TEST_F(bcmuapp_bcmu, RemoveExtremeVoltagesAllCellsRemoved) {
    int remove_count = TOTAL_CELLS; // All cells should be removed
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < TOTAL_CELLS; ++i) {
        valid_cells[i] = i;
    }
    float mean = calculate_mean(s_cell_volt, valid_cells);
    int ret = remove_extreme_voltages(s_cell_volt, valid_cells, mean, remove_count);
    EXPECT_EQ(ret, SUCCESS);
    // Check if all cells are marked as invalid
    for (int i = 0; i < TOTAL_CELLS; ++i) {
        EXPECT_EQ(valid_cells[i], -1);
    }
}

TEST_F(bcmuapp_bcmu, RemoveExtremeVoltagesSomeCellsAlreadyInvalid) {
    int remove_count = 5;
    int valid_cells[TOTAL_CELLS];
    // Mark some cells as already invalid
    for (int i = 0; i < TOTAL_CELLS; ++i) {
        valid_cells[i] = i;
    }
    float mean = calculate_mean(s_cell_volt, valid_cells);
    int ret = remove_extreme_voltages(s_cell_volt, valid_cells, mean, remove_count);
    EXPECT_EQ(ret, SUCCESS);
    // Check if exactly 'remove_count' additional cells are marked as invalid
    int invalid_count = 0;
    for (int i = 0; i < TOTAL_CELLS; ++i) {
        if (valid_cells[i] == -1) {
            invalid_count++;
        }
    }
    EXPECT_EQ(invalid_count, remove_count);
}

/* Ended by AICoder, pid:vfadah9da68b0b7141ac0b3c800596520182fe8a */

/* Started by AICoder, pid:g5b5eg81f93c94e14c040956109e1e5603f6ee5f */
TEST_F(bcmuapp_bcmu, calculate_mean_NormalCase) {
    float test_voltages[BMU_NUM][BMU_CELL_NUM];
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < BMU_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f; // 设置所有电压为3.3V
            valid_cells[i * BMU_CELL_NUM + j] = i * BMU_CELL_NUM + j;
        }
    }
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float result = calculate_mean(s_cell_volt, valid_cells);
    EXPECT_FLOAT_EQ(3.3f, result);
}

TEST_F(bcmuapp_bcmu, calculate_mean_AllInvalidCells) {
    float test_voltages[BMU_NUM][BMU_CELL_NUM];
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < BMU_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f;
            valid_cells[i * BMU_CELL_NUM + j] = -1;
        }
    }
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float result = calculate_mean(s_cell_volt, valid_cells);
    EXPECT_FLOAT_EQ(0.0f, result);
}

TEST_F(bcmuapp_bcmu, calculate_mean_SingleValidCell) {
    float test_voltages[BMU_NUM][BMU_CELL_NUM];
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < BMU_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f;
            valid_cells[i * BMU_CELL_NUM + j] = -1;
        }
    }
    valid_cells[0] = 0; // 只有一个有效单元格
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float result = calculate_mean(s_cell_volt, valid_cells);
    EXPECT_FLOAT_EQ(3.3f, result);
}

TEST_F(bcmuapp_bcmu, calculate_mean_BoundaryCondition) {
    float test_voltages[BMU_NUM][BMU_CELL_NUM];
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < BMU_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f;
            valid_cells[i * BMU_CELL_NUM + j] = i * BMU_CELL_NUM + j;
        }
    }
    valid_cells[0] = -1; // 第一个单元格无效
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float result = calculate_mean(s_cell_volt, valid_cells);
    EXPECT_FLOAT_EQ(3.3f, result);
}
/* Ended by AICoder, pid:g5b5eg81f93c94e14c040956109e1e5603f6ee5f */

/* Started by AICoder, pid:e743967b4b63e6a149ea0bcec07c246aa4b0d7b1 */
TEST_F(bcmuapp_bcmu, calculate_std_dev_NormalCase) {
    float test_voltages[TOTAL_CELLS / BMU_CELL_NUM][BMU_CELL_NUM];
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < TOTAL_CELLS / BMU_CELL_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f + (float)(rand() % 10) / 10.0f; // 随机电压值
            valid_cells[i * BMU_CELL_NUM + j] = i * BMU_CELL_NUM + j;
        }
    }
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float mean = calculate_mean(s_cell_volt, valid_cells);
    float result = calculate_std_dev(s_cell_volt, valid_cells, mean);
    EXPECT_GT(result, 0.0f); // 标准差应大于0
}

TEST_F(bcmuapp_bcmu, calculate_std_dev_AllInvalidCells) {
    float test_voltages[TOTAL_CELLS / BMU_CELL_NUM][BMU_CELL_NUM];
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < TOTAL_CELLS / BMU_CELL_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f;
            valid_cells[i * BMU_CELL_NUM + j] = -1;
        }
    }
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float mean = calculate_mean(s_cell_volt, valid_cells);
    float result = calculate_std_dev(s_cell_volt, valid_cells, mean);
    EXPECT_FLOAT_EQ(0.0f, result); 
}

TEST_F(bcmuapp_bcmu, calculate_std_dev_SingleValidCell) {
    float test_voltages[TOTAL_CELLS / BMU_CELL_NUM][BMU_CELL_NUM];
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < TOTAL_CELLS / BMU_CELL_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f;
            valid_cells[i * BMU_CELL_NUM + j] = -1;
        }
    }
    valid_cells[0] = 0; // 只有一个有效单元格
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float mean = calculate_mean(s_cell_volt, valid_cells);
    float result = calculate_std_dev(s_cell_volt, valid_cells, mean);
    EXPECT_FLOAT_EQ(0.0f, result); // 单个有效单元格的标准差应为0
}

TEST_F(bcmuapp_bcmu, calculate_std_dev_BoundaryCondition) {
    float test_voltages[TOTAL_CELLS / BMU_CELL_NUM][BMU_CELL_NUM];
    int valid_cells[TOTAL_CELLS];
    for (int i = 0; i < TOTAL_CELLS / BMU_CELL_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f;
            valid_cells[i * BMU_CELL_NUM + j] = i * BMU_CELL_NUM + j;
        }
    }
    valid_cells[0] = -1; // 第一个单元格无效
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float mean = calculate_mean(s_cell_volt, valid_cells);
    float result = calculate_std_dev(s_cell_volt, valid_cells, mean);
    EXPECT_FLOAT_EQ(0.0f, result); // 所有有效单元格电压相同，标准差应为0
}
/* Ended by AICoder, pid:e743967b4b63e6a149ea0bcec07c246aa4b0d7b1 */

/* Started by AICoder, pid:g1c9ft4921xe7d81443808d900933850c601edab */
TEST_F(bcmuapp_bcmu, calculate_z_scores_NormalCase) {
    float test_voltages[TOTAL_CELLS / BMU_CELL_NUM][BMU_CELL_NUM];
    float z_scores[TOTAL_CELLS];
    for (int i = 0; i < TOTAL_CELLS / BMU_CELL_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f + (float)(rand() % 10) / 10.0f; // 随机电压值
        }
    }
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float mean = calculate_mean(s_cell_volt, (int*)malloc(TOTAL_CELLS * sizeof(int)));
    float std_dev = calculate_std_dev(s_cell_volt, (int*)malloc(TOTAL_CELLS * sizeof(int)), mean);
    int result = calculate_z_scores(s_cell_volt, mean, std_dev, z_scores);
    EXPECT_EQ(SUCCESS, result);
    for (int i = 0; i < TOTAL_CELLS; ++i) {
        float expected_z_score = (test_voltages[i / BMU_CELL_NUM][i % BMU_CELL_NUM] - mean) / std_dev;
        EXPECT_NEAR(z_scores[i], expected_z_score, 1e-6);
    }
}

TEST_F(bcmuapp_bcmu, calculate_z_scores_StdDevZero) {
    float test_voltages[TOTAL_CELLS / BMU_CELL_NUM][BMU_CELL_NUM];
    float z_scores[TOTAL_CELLS];
    for (int i = 0; i < TOTAL_CELLS / BMU_CELL_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f; // 所有电压相同，标准差为0
        }
    }
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float mean = calculate_mean(s_cell_volt, (int*)malloc(TOTAL_CELLS * sizeof(int)));
    float std_dev = 0.0f; // 强制设置标准差为0
    int result = calculate_z_scores(s_cell_volt, mean, std_dev, z_scores);
    EXPECT_EQ(FAILURE, result);
}

TEST_F(bcmuapp_bcmu, calculate_z_scores_BoundaryCondition) {
    float test_voltages[TOTAL_CELLS / BMU_CELL_NUM][BMU_CELL_NUM];
    float z_scores[TOTAL_CELLS];
    for (int i = 0; i < TOTAL_CELLS / BMU_CELL_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            test_voltages[i][j] = 3.3f + (float)(rand() % 10) / 10.0f; // 随机电压值
        }
    }
    test_voltages[0][0] = 100.0f; // 设置一个极端值
    memcpy(s_cell_volt, test_voltages, sizeof(test_voltages));
    float mean = calculate_mean(s_cell_volt, (int*)malloc(TOTAL_CELLS * sizeof(int)));
    float std_dev = calculate_std_dev(s_cell_volt, (int*)malloc(TOTAL_CELLS * sizeof(int)), mean);
    int result = calculate_z_scores(s_cell_volt, mean, std_dev, z_scores);
    EXPECT_EQ(SUCCESS, result);
    float expected_z_score = (test_voltages[0][0] - mean) / std_dev;
    EXPECT_NEAR(z_scores[0], expected_z_score, 1e-6);
}
/* Ended by AICoder, pid:g1c9ft4921xe7d81443808d900933850c601edab */

/* Started by AICoder, pid:3e96dkf7c8i776e143fc08df50a73058b1b9c46a */
TEST_F(bcmuapp_bcmu, update_outliers_NormalCase) {
    float z_scores[TOTAL_CELLS];
    outlier_info_t outliers[MAX_OUTLIERS];
    float outliner_set_thre = 5;
    int outliner_set_period = 10;
    float outliner_recovery_thre = 3;
    int outliner_recovery_period = 30;
    int result = 0;
    int i = 0;
    float mean = 3;
    for (i = 0; i < TOTAL_CELLS; ++i) {
        z_scores[i] = 2.0f; // 设置所有Z分数为2.0
    }
    z_scores[0] = 5.1f; // 设置第一个单元格的Z分数为4.0，超过阈值

    memset(s_outlier_history, 0, sizeof(s_outlier_history));
    s_outlier_count = 0;

    for(i = 0; i < outliner_set_period -1; i++) {
        result = update_outliers(s_cell_volt, mean, z_scores, s_outlier_history, outliers, &s_outlier_count);
        EXPECT_EQ(SUCCESS, result);
        EXPECT_EQ(0, s_outlier_count); // 应该有一个异常值
    }
    result = update_outliers(s_cell_volt, mean, z_scores, s_outlier_history, outliers, &s_outlier_count);
    EXPECT_EQ(SUCCESS, result);
    EXPECT_EQ(1, s_outlier_count); // 应该有一个异常值
    EXPECT_EQ(1, outliers[0].pack); // 第一个包
    EXPECT_EQ(1, outliers[0].cell); // 第一个单元格
    EXPECT_FLOAT_EQ(5.1f, outliers[0].z_score); // Z分数应为4.0
    EXPECT_EQ(OUTLINER, s_outlier_history[0].state); 

    z_scores[0] = 1.0f; // 设置Z分数为1.0，低于阈值
    for (i = 0; i < outliner_recovery_period -1; i++) {
        result = update_outliers(s_cell_volt, mean, z_scores, s_outlier_history, outliers, &s_outlier_count);
        EXPECT_EQ(SUCCESS, result);
        EXPECT_EQ(1, s_outlier_count); // 应该有一个异常值
         
    }
    EXPECT_EQ(OUTLINER, s_outlier_history[0].state);
    result = update_outliers(s_cell_volt, mean, z_scores, s_outlier_history, outliers, &s_outlier_count);
    EXPECT_EQ(SUCCESS, result);
    EXPECT_EQ(0, s_outlier_count); // 应该没有异常值
    EXPECT_EQ(0, outliers[0].pack); // 第一个包
    EXPECT_EQ(0, outliers[0].cell); // 第一个单元格
    EXPECT_EQ(NOT_OUTLINER, s_outlier_history[0].state);

    for (i = 0; i < MAX_OUTLIERS; ++i) {
        z_scores[i] = 5.1f; // 设置前MAX_OUTLIERS单元格的Z分数为5.1，超过阈值
    }
    z_scores[MAX_OUTLIERS+10] = 5.6f;

    for(i = 0; i < outliner_set_period; i++) {
        update_outliers(s_cell_volt, mean, z_scores, s_outlier_history, outliers, &s_outlier_count);
    }
    EXPECT_EQ(MAX_OUTLIERS, s_outlier_count); // 应该有MAX_OUTLIERS个异常值
    EXPECT_EQ(1, outliers[0].pack); // 第一个包
    EXPECT_EQ(MAX_OUTLIERS+11, outliers[0].cell); // 第一个单元格

    for (i = 0; i < MAX_OUTLIERS; ++i) {
        z_scores[i] = 2.0f; 
    }
    for(i = 0; i < outliner_recovery_period; i++) {
        update_outliers(s_cell_volt, mean, z_scores, s_outlier_history, outliers, &s_outlier_count);
    }
    EXPECT_EQ(1, s_outlier_count); // 应该有1个异常值
    EXPECT_EQ(1, outliers[0].pack); // 第一个包
    EXPECT_EQ(MAX_OUTLIERS+11, outliers[0].cell); // 第一个单元格
    EXPECT_NE(0, outliers[0].volt_diff); // 第一个单元格
    EXPECT_EQ(OUTLINER, s_outlier_history[MAX_OUTLIERS+10].state);

    z_scores[MAX_OUTLIERS+10] = 2.0f; 
    for(i = 0; i < outliner_recovery_period; i++) {
        update_outliers(s_cell_volt, mean, z_scores, s_outlier_history, outliers, &s_outlier_count);
    }
    EXPECT_EQ(0, s_outlier_count); // 应该没有异常值
    EXPECT_EQ(0, outliers[0].pack); // 第一个包
    EXPECT_EQ(0, outliers[0].cell); // 第一个单元格
    EXPECT_EQ(NOT_OUTLINER, s_outlier_history[MAX_OUTLIERS+10].state);
}
/* Ended by AICoder, pid:3e96dkf7c8i776e143fc08df50a73058b1b9c46a */

/* Started by AICoder, pid:202d4x605d0fede1490b08868077a024e8f028b1 */
TEST(BcmuBusinessTest, ShouldReturnFalseWhenBatteryStatusTest)
{
    int bcmu_battery_status = BCMU_BAT_STATUS_DISCHARGE;
    float avg_temp = 5.0f;
    // 非静置
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(int), type_int);
    EXPECT_EQ(FALSE, should_calculate_outliers2());
    // 静置但平均温度低于阈值
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BCMU_CELL_AVERAGE_TEMPERATURE, &avg_temp, sizeof(float), type_float);
    EXPECT_EQ(FALSE, should_calculate_outliers2());
    // 静置且平均温度高于阈值，但次数不够
    bcmu_battery_status = BCMU_BAT_STATUS_STANDY;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(int), type_int);
    avg_temp = 11;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BCMU_CELL_AVERAGE_TEMPERATURE, &avg_temp, sizeof(float), type_float);
    EXPECT_EQ(FALSE, should_calculate_outliers2());
    // 静置且平均温度高于阈值，且次数到达
    for (int i = 0; i < BCMUAPP_OUTLIERS_STANDY_KEEP_COUNT; i++)
    {
        should_calculate_outliers2();
    }
    EXPECT_EQ(TRUE, should_calculate_outliers2());
}
/* Ended by AICoder, pid:202d4x605d0fede1490b08868077a024e8f028b1 */

/* Started by AICoder, pid:y3a0725372p7a4114a820a4b808b004c7ad89ee5 */
TEST(BcmuBusinessTest, JudgeCellOutliers2Test) {
    int volt_mv_diff_expect = 0;
    float avg_temp = 25.0;
    int bcmu_battery_status = BCMU_BAT_STATUS_DISCHARGE;
    // NullVoltagesPointer
    EXPECT_EQ(judge_cell_outliers2(NULL), FAILURE);
    // ShouldNotCalculateOutliers
    /// 静置，计算条件不满足
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(int), type_int);
    EXPECT_EQ(judge_cell_outliers2(s_cell_volt), FAILURE);
    // NormalCaseWithNoOutliers
    /// 电芯温度
    for (int i = 0; i < BMU_NUM; i++) {
        for (int j = 0; j < BMU_CELL_NUM; j++) {
            s_cell_volt[i][j] = 3.2752f;  // 25度，85%
        }
    }
    /// 静置且平均温度高于阈值，且次数到达
    bcmu_battery_status = BCMU_BAT_STATUS_STANDY;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BCMU_CELL_AVERAGE_TEMPERATURE, &avg_temp, sizeof(float), type_float);
    for (int i = 0; i < BCMUAPP_OUTLIERS_STANDY_KEEP_COUNT; i++)
    {
        judge_cell_outliers2(s_cell_volt);
    }
    EXPECT_EQ(SUCCESS, judge_cell_outliers2(s_cell_volt));
    EXPECT_EQ(0, s_outlier_count);
    EXPECT_NEAR(3.2752f, s_cell_avg_volt, 0.01f);
    EXPECT_NEAR(85.0f, s_cell_avg_soc, 0.1f);
    for (int i = 0; i < MAX_OUTLIERS; i++)
    {
        EXPECT_EQ(0, s_outliers[i].pack);
        EXPECT_EQ(0, s_outliers[i].cell);
        EXPECT_EQ(0, s_outliers[i].volt_diff);
        EXPECT_NEAR(0.0f, s_outliers[i].z_score, 0.1);
    }
    EXPECT_EQ(FALSE, is_cell_volt_outlier_pos_change());
    EXPECT_EQ(SUCCESS, save_outliers_info());
    EXPECT_EQ(FALSE, is_cell_volt_outlier_pos_change());

    // NormalCaseWithSomeOutliers
    s_cell_volt[3][2] = 3.2628f;  // 低SOC |70.0% - 85.0%| = 15% > 5%
    s_cell_volt[2][7] = 3.2773f;  // 高SOC 95.0% - 85.0% = 10% > 5%
    EXPECT_EQ(SUCCESS, judge_cell_outliers2(s_cell_volt));
    EXPECT_EQ(2, s_outlier_count);
    /// check1
    EXPECT_EQ(4, s_outliers[0].pack);
    EXPECT_EQ(3, s_outliers[0].cell);
    volt_mv_diff_expect = (s_cell_volt[3][2] - s_cell_avg_volt) * 1000;
    printf("volt_mv_diff_expect2 = %d mv \n", volt_mv_diff_expect);
    printf("s_outliers[0].volt_diff = %d mv \n", s_outliers[0].volt_diff);
    EXPECT_NEAR(volt_mv_diff_expect, s_outliers[0].volt_diff, 1);
    /// check2
    EXPECT_EQ(3, s_outliers[1].pack);
    EXPECT_EQ(8, s_outliers[1].cell);
    volt_mv_diff_expect = (s_cell_volt[2][7] - s_cell_avg_volt) * 1000;
    printf("volt_mv_diff_expect1 = %d mv \n", volt_mv_diff_expect);
    printf("s_outliers[1].volt_diff = %d mv \n", s_outliers[1].volt_diff);
    EXPECT_NEAR(volt_mv_diff_expect, s_outliers[1].volt_diff, 1);
    /// check3
    for (int i = 2; i <= 9; i++)
    {
        EXPECT_EQ(0, s_outliers[i].pack);
        EXPECT_EQ(0, s_outliers[i].cell);
    }
    /// check4
    EXPECT_EQ(TRUE, is_cell_volt_outlier_pos_change());
    EXPECT_EQ(SUCCESS, save_outliers_info());
    EXPECT_EQ(FALSE, is_cell_volt_outlier_pos_change());
}
/* Ended by AICoder, pid:y3a0725372p7a4114a820a4b808b004c7ad89ee5 */

/* Started by AICoder, pid:m7394r23ddq934614a3408e8e0dc0919fd741287 */
TEST(BcmuBusinessTest, SortSocDevFabsTest_NullCase) {
    EXPECT_EQ(sort_soc_dev_fabs(NULL), FAILURE);
}

TEST(BcmuBusinessTest, CalculateCellSocVoltDev_NullCase) {
    EXPECT_EQ(calculate_cell_soc_volt_dev(NULL), FAILURE);
}
/* Ended by AICoder, pid:m7394r23ddq934614a3408e8e0dc0919fd741287 */

/* Started by AICoder, pid:441c3034af66e391499b0bbd60631c477b168d67 */
TEST(UpdateCellOutliersStatusTest, ShouldNotCalculateOutliers) {
    MOCKER(should_calculate_outliers).stubs().will(returnValue(FALSE));
    EXPECT_EQ(update_cell_outliers_status(), FAILURE);
    GlobalMockObject::verify();
}

TEST(UpdateCellOutliersStatusTest, LoadBmuCellVoltFailure) {
    MOCKER(should_calculate_outliers).stubs().will(returnValue(TRUE));
    MOCKER(load_bmu_cell_volt).stubs().will(returnValue(FAILURE));
    EXPECT_EQ(update_cell_outliers_status(), FAILURE);
    GlobalMockObject::verify();
}

TEST(UpdateCellOutliersStatusTest, PlanConfig1Success) {
    int plan_config = 1;
    MOCKER(should_calculate_outliers).stubs().will(returnValue(TRUE));
    MOCKER(load_bmu_cell_volt).stubs().will(returnValue(SUCCESS));
    MOCKER(judge_cell_outliers1).stubs().will(returnValue(SUCCESS));
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_PLAN_CONFIG, &plan_config, sizeof(int), type_int);
    EXPECT_EQ(update_cell_outliers_status(), SUCCESS);
    GlobalMockObject::verify();
}

TEST(UpdateCellOutliersStatusTest, PlanConfig2Success) {
    int plan_config = 2;
    MOCKER(should_calculate_outliers).stubs().will(returnValue(TRUE));
    MOCKER(load_bmu_cell_volt).stubs().will(returnValue(SUCCESS));
    MOCKER(judge_cell_outliers2).stubs().will(returnValue(SUCCESS));
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_PLAN_CONFIG, &plan_config, sizeof(int), type_int);
    EXPECT_EQ(update_cell_outliers_status(), SUCCESS);
    GlobalMockObject::verify();
}

TEST(UpdateCellOutliersStatusTest, JudgeCellOutliers1Failure) {
    int plan_config = 1;
    MOCKER(should_calculate_outliers).stubs().will(returnValue(TRUE));
    MOCKER(load_bmu_cell_volt).stubs().will(returnValue(SUCCESS));
    MOCKER(judge_cell_outliers1).stubs().will(returnValue(FAILURE));
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_PLAN_CONFIG, &plan_config, sizeof(int), type_int);
    EXPECT_EQ(update_cell_outliers_status(), FAILURE);
    GlobalMockObject::verify();
}

TEST(UpdateCellOutliersStatusTest, JudgeCellOutliers2Failure) {
    int plan_config = 2;
    MOCKER(should_calculate_outliers).stubs().will(returnValue(TRUE));
    MOCKER(load_bmu_cell_volt).stubs().will(returnValue(SUCCESS));
    MOCKER(judge_cell_outliers2).stubs().will(returnValue(FAILURE));
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_PLAN_CONFIG, &plan_config, sizeof(int), type_int);
    EXPECT_EQ(update_cell_outliers_status(), FAILURE);
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:441c3034af66e391499b0bbd60631c477b168d67 */

/* Started by AICoder, pid:adbcd35582sc19f1491c0bbbc06c3947216799d0 */
TEST_F(bcmuapp_bcmu, calculate_max_power_charge_boundary_zero_current) {
    float charging_max_current = 0.0f;
    float dc_voltage = 500.0f;
    float pcs_max_charge_power = 10.0f;
    //prev_max_charge_power = 0.0f;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &charging_max_current, sizeof(float), type_float);
    pdt_set_data(SID_POWER_CONVERSION_SYSTEM_ANA_PCS_DC_VOLTAGE, &dc_voltage, sizeof(float), type_float);
    pdt_set_data(SID_POWER_CONVERSION_SYSTEM_PARA_PCS_ACTIVE_POWER_MAX, &pcs_max_charge_power, sizeof(float), type_float);

    EXPECT_NEAR(calculate_max_power(CHARGE), 0.0f, 0.001);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, calculate_max_power_charge_normal) {
    float charging_max_current = 10.0f;
    float dc_voltage = 500.0f;
    float pcs_max_charge_power = 10.0f;
    //prev_max_charge_power = 0.0f;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &charging_max_current, sizeof(float), type_float);
    pdt_set_data(SID_POWER_CONVERSION_SYSTEM_ANA_PCS_DC_VOLTAGE, &dc_voltage, sizeof(float), type_float);
    pdt_set_data(SID_POWER_CONVERSION_SYSTEM_PARA_PCS_ACTIVE_POWER_MAX, &pcs_max_charge_power, sizeof(float), type_float);

    EXPECT_NEAR(calculate_max_power(CHARGE), 2.5f, 0.001);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, calculate_max_power_discharge_boundary_zero_current) {
    float max_discharge_current = 0.0f;
    float dc_voltage = 500.0f;
    float pcs_max_discharge_power = 10.0f;

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_DISCHARGE_MAX_CURRENT, &max_discharge_current, sizeof(float), type_float);
    pdt_set_data(SID_POWER_CONVERSION_SYSTEM_ANA_PCS_DC_VOLTAGE, &dc_voltage, sizeof(float), type_float);
    pdt_set_data(SID_POWER_CONVERSION_SYSTEM_PARA_PCS_ACTIVE_POWER_MAX, &pcs_max_discharge_power, sizeof(float), type_float);

    EXPECT_NEAR(calculate_max_power(DISCHARGE), 0.0f, 0.001);
    GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, calculate_max_power_discharge_power_normal) {
    float max_discharge_current = 10.0f;
    float dc_voltage = 500.0f;
    float pcs_max_discharge_power = 10.0f;

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_DISCHARGE_MAX_CURRENT, &max_discharge_current, sizeof(float), type_float);
    pdt_set_data(SID_POWER_CONVERSION_SYSTEM_ANA_PCS_DC_VOLTAGE, &dc_voltage, sizeof(float), type_float);
    pdt_set_data(SID_POWER_CONVERSION_SYSTEM_PARA_PCS_ACTIVE_POWER_MAX, &pcs_max_discharge_power, sizeof(float), type_float);

    EXPECT_NEAR(calculate_max_power(DISCHARGE), 2.5f, 0.001);
    GlobalMockObject::verify();

}

TEST_F(bcmuapp_bcmu, get_battery_capacity_normal) {
    int capacity = get_battery_capacity();
    EXPECT_EQ(capacity, BATTERY_CAPACITY);
}

TEST_F(bcmuapp_bcmu, update_power_data_normal) {
    float charging_max_current = 10.0f;
    float dc_voltage = 500.0f;
    float max_discharge_current = 10.0f;

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_DISCHARGE_MAX_CURRENT, &max_discharge_current, sizeof(float), type_float);
    pdt_set_data(SID_POWER_CONVERSION_SYSTEM_ANA_PCS_DC_VOLTAGE, &dc_voltage, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &charging_max_current, sizeof(float), type_float);
 
    EXPECT_EQ(update_power_data(), SUCCESS);
}
/* Ended by AICoder, pid:adbcd35582sc19f1491c0bbbc06c3947216799d0 */

int mock_get_bmu_index_pending_backup(int *bmu_index)
{
    bmu_index[0] = 0;
    bmu_index[1] = -1;
    bmu_index[2] = -1;
    bmu_index[3] = -1;
    return SUCCESS;
}

TEST_F(bcmuapp_bcmu, do_replace_finished_judge_test) {
    // 测试备件替换未进行时的情况
    MOCKER(get_backup_status)
        .stubs()
        .will(returnValue((int)enum_backup_none));
    EXPECT_EQ(SUCCESS, do_replace_finished_judge());
    GlobalMockObject::verify();
    
    // 测试备件替换进行中，但SOH数据未同步的情况
    MOCKER(get_backup_status)
        .stubs()
        .will(returnValue((int)enum_backup_doing));
    MOCKER(get_bmu_index_pending_backup)
        .stubs()
        .will(invoke(mock_get_bmu_index_pending_backup));
    
    // 模拟bmu_soh_data_syned[0]为FALSE
    bmu_soh_data_syned[0] = FALSE;
    EXPECT_EQ(FAILURE, do_replace_finished_judge());
    GlobalMockObject::verify();
    
    // 测试备件替换进行中，且SOH数据已同步的情况
    MOCKER(get_backup_status)
        .stubs()
        .will(returnValue((int)enum_backup_doing));
    MOCKER(get_bmu_index_pending_backup)
        .stubs()
        .will(invoke(mock_get_bmu_index_pending_backup));
    MOCKER(set_soh_sync_finish_flag)
        .stubs()
        .will(returnValue(SUCCESS));
    
    // 模拟bmu_soh_data_syned[0]为TRUE
    bmu_soh_data_syned[0] = TRUE;
    EXPECT_EQ(SUCCESS, do_replace_finished_judge());
    GlobalMockObject::verify();
}


/* Started by AICoder, pid:k3a20k1fa7u91e4148cb0803e0ac39650f011dc3 */
TEST(OCVTest, NullResultPointer) {
    float temperature = 25.0f;
    float volt = 3.24f;
    ocv_area_t result = {0};
    EXPECT_EQ(get_soc_from_ocv_table(temperature, volt, NULL), FAILURE);
    EXPECT_FLOAT_EQ(result.temp_match, 0);
    EXPECT_FLOAT_EQ(result.volt_area[0], 0);
    EXPECT_FLOAT_EQ(result.volt_area[1], 0);
    EXPECT_FLOAT_EQ(result.soc_area[0], 0);
    EXPECT_FLOAT_EQ(result.soc_area[1], 0);
    EXPECT_FLOAT_EQ(result.soc, 0);
}

TEST(OCVTest, TemperatureBelowRange) {
    float temperature = -40.0f;
    float volt = 2.8163f;
    ocv_area_t result = {0};
    EXPECT_EQ(get_soc_from_ocv_table(temperature, volt, &result), SUCCESS);
    EXPECT_FLOAT_EQ(result.temp_match, -30);
    EXPECT_FLOAT_EQ(result.volt_area[0], 2.8161);
    EXPECT_FLOAT_EQ(result.volt_area[1], 2.8620);
    EXPECT_FLOAT_EQ(result.soc_area[0], 50);
    EXPECT_FLOAT_EQ(result.soc_area[1], 55);
}

TEST(OCVTest, TemperatureAboveRange) {
    float temperature = 100.0f;
    float volt = 3.2620f;
    ocv_area_t result = {0};
    EXPECT_EQ(get_soc_from_ocv_table(temperature, volt, &result), SUCCESS);
    EXPECT_FLOAT_EQ(result.temp_match, 50);
    EXPECT_FLOAT_EQ(result.volt_area[0], 3.2616);
    EXPECT_FLOAT_EQ(result.volt_area[1], 3.2624);
    EXPECT_FLOAT_EQ(result.soc_area[0], 50);
    EXPECT_FLOAT_EQ(result.soc_area[1], 55);
    EXPECT_GT(result.soc, 50);
    EXPECT_LT(result.soc, 55);
}

TEST(OCVTest, VoltBelowRange) {
    float temperature = 25.0f;
    float volt = 1.0f;
    ocv_area_t result = {0};
    EXPECT_EQ(get_soc_from_ocv_table(temperature, volt, &result), SUCCESS);
    EXPECT_FLOAT_EQ(result.temp_match, 25);
    EXPECT_FLOAT_EQ(result.volt_area[0], 2.9013);
    EXPECT_FLOAT_EQ(result.volt_area[1], 3.0407);
    EXPECT_FLOAT_EQ(result.soc_area[0], 0);
    EXPECT_FLOAT_EQ(result.soc_area[1], 5);
    EXPECT_FLOAT_EQ(result.soc, 0);
}

TEST(OCVTest, VoltAboveRange) {
    float temperature = 25.0f;
    float volt = 10.0f;
    ocv_area_t result = {0};
    EXPECT_EQ(get_soc_from_ocv_table(temperature, volt, &result), SUCCESS);
    EXPECT_FLOAT_EQ(result.temp_match, 25);
    EXPECT_FLOAT_EQ(result.volt_area[0], 3.2773);
    EXPECT_FLOAT_EQ(result.volt_area[1], 3.4408);
    EXPECT_FLOAT_EQ(result.soc_area[0], 95);
    EXPECT_FLOAT_EQ(result.soc_area[1], 100);
    EXPECT_FLOAT_EQ(result.soc, 100);
}

TEST(OCVTest, NormalCase) {
    float temperature = 25.0f;
    float volt = 3.2400f;
    ocv_area_t result = {0};
    EXPECT_EQ(get_soc_from_ocv_table(temperature, volt, &result), SUCCESS);
    EXPECT_FLOAT_EQ(result.temp_match, 25);
    EXPECT_FLOAT_EQ(result.volt_area[0], 3.2372);
    EXPECT_FLOAT_EQ(result.volt_area[1], 3.2417);
    EXPECT_FLOAT_EQ(result.soc_area[0], 50);
    EXPECT_FLOAT_EQ(result.soc_area[1], 55);
    EXPECT_GT(result.soc, 50);
    EXPECT_LT(result.soc, 55);
}
/* Ended by AICoder, pid:k3a20k1fa7u91e4148cb0803e0ac39650f011dc3 */

/* Started by AICoder, pid:60c4676e9br697d14a550aa720189d1906b1f9c7 */
TEST(MidianLookupTest, AbnormalCase) {
    unsigned int index = 10;
    index = get_point_index_by_median_lookup(NULL, 11, 20);
    EXPECT_EQ(index, 0);

    float array_test[OCV_TAB_SIZE_TEMP_NUM] = {
        -30, -20, -15, -10, -5, 0, 5, 10, 25, 45, 50
    };
    index = get_point_index_by_median_lookup(&array_test[0], 0, 20);
    EXPECT_EQ(index, 0);
}
/* Ended by AICoder, pid:60c4676e9br697d14a550aa720189d1906b1f9c7 */

/* Started by AICoder, pid:i7f9bk3ee5u575014c900a253008980fc3b4fa6d */
TEST(GetCutoffStatusTest, NormalCase) {
    s_mock_flood_alm_sta = 1;
    EXPECT_EQ(get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_FLOOD_ALARM), s_mock_flood_alm_sta);
}
/* Ended by AICoder, pid:i7f9bk3ee5u575014c900a253008980fc3b4fa6d */

/* Started by AICoder, pid:z3209p6cb325ff814b5f084c70c6d20ca324a8bd */
TEST(JudgeCellCutoffStatusTest, NullPointer_CausesCrash) {
    int_type_t cutoff_alm_val[BMU_CELL_NUM] = {0};
    EXPECT_EQ(judge_cell_cutoff_status(cutoff_alm_val), (int)NORMAL);
    cutoff_alm_val[3].i_value = ABNORMAL;
    EXPECT_EQ(judge_cell_cutoff_status(cutoff_alm_val), (int)ABNORMAL);
}
/* Ended by AICoder, pid:z3209p6cb325ff814b5f084c70c6d20ca324a8bd */

TEST(step_constant_power_chargingTest, NormalCase) {
    int_type_t cutoff_alm_val[BMU_CELL_NUM] = {0};
    s_mock_cell_max_voltage = 3600;
    step_constant_power_charging(0.5, 20);
    EXPECT_FLOAT_EQ(s_mock_charge_max_current, 62.8f);
}

/* Started by AICoder, pid:adeb3ze33bs82ec148b00b21101e9c2155070bb6 */
TEST_F(bcmuapp_bcmu, set_bcmu_business_period_success) {
    // Mocking the set_timer function to return SUCCESS
    MOCKER(set_timer)
        .stubs()
        .will(returnValue(SUCCESS));

    EXPECT_EQ(set_bcmu_business_period(), SUCCESS);
     GlobalMockObject::verify();
}

TEST_F(bcmuapp_bcmu, set_bcmu_business_period_failure) {

    // Mocking the kill_timer function

    // Mocking the set_timer function to return FAILURE
    MOCKER(set_timer)
        .stubs()
        .will(returnValue(FAILURE));

    EXPECT_EQ(set_bcmu_business_period(), FAILURE);
     GlobalMockObject::verify();
}
/* Ended by AICoder, pid:adeb3ze33bs82ec148b00b21101e9c2155070bb6 */

int main(int argc, char *argv[]) {
    testing::AddGlobalTestEnvironment(new ENV);
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}


