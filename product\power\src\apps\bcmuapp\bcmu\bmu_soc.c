#include "oss/plat_oss.h"
#include "plat_error.h"
#include "bmu_soc.h"
#include "plat_data_access.h"
#include "pdt_dictionary_define.h"
#include "data_access.h"
#include "pdt_common.h"
#include "pdt_data_struct.h"
#include <sys/time.h>
#include <math.h>
#include "fapi_bmu_comm.h"
#include "bmu_business.h"
#include "bcmu_business.h"
#include "plat_utils_safe_inf.h"
#include "data_type.h"

extern int bmu_soh_data_syned[BMU_NUM]; // 标记对应索引的BMU的SOH基准数据是否已成功从BMU读取到BCMU

STATIC float s_bmu_current[BMU_NUM] = {0};
STATIC float s_bmu_real_capacity[BMU_NUM] = {0};
STATIC bmu_cal_soc_t s_bmu_cal_soc[BMU_NUM] = {0};
STATIC bmu_soc_save_t s_bmu_soc_save;
STATIC time_t  s_pre_time = 0;
STATIC bcmu_save_t s_bcmu_save_bak;
STATIC bmu_sox_info_t s_bmu_sox_in[BMU_NUM];
STATIC bmu_sox_deal_t s_bmu_sox_deal[BMU_NUM];
STATIC int s_is_limiting = FALSE;               // SOE限幅标志
STATIC float s_last_soe = 0.0f;                    // 上一次的SOE值
STATIC int s_last_opt_status = FALSE;              // 上一次优化器状态

STATIC float s_bmu_volt[BMU_NUM] = {0};
STATIC bmu_cal_soe_t s_bmu_cal_soe[BMU_NUM] = {0};     // SOE计算数据
STATIC float s_bmu_real_energy[BMU_NUM] = {0};         // 实际可用能量, Emax
STATIC int s_replace_finish_flag = enum_backup_none;           // 备件替换结束标志，TRUE表示完成，FALSE表示正在进行中
STATIC int s_bmu_index_pending_backup[BMU_NUM] = {-1, -1, -1, -1}; // 待同步SOH数据的BMU索引，-1表示无需同步
STATIC int s_soh_sync_finish_flag = FALSE;

STATIC time_t  s_idle_start_time = 0;
float global_max_v = -FLT_MAX;
float global_min_v =  FLT_MAX;
float global_temp_at_max = 0;
float global_temp_at_min = 0;
float bmu_cell_voltages_max[BMU_NUM];
float bmu_cell_temps_at_max[BMU_NUM];
float bmu_cell_voltages_min[BMU_NUM];
float bmu_cell_temps_at_min[BMU_NUM];

// 查表维度
#define NUM_TEMPS 11
#define NUM_SOC    5
#define IDLE_CURRENT_THRESHOLD 2.15f

// 只取这 5 列
static const float soc_table[NUM_SOC] = { 0.0f, 5.0f, 10.0f, 95.0f, 100.0f };
// 行是温度：–30, –20, –15, –10, –5, 0, 5, 10, 25, 45, 50 ℃
static const float temp_table[NUM_TEMPS] = {
    -30.0f, -20.0f, -15.0f, -10.0f, -5.0f,
      0.0f,   5.0f,  10.0f,  25.0f, 45.0f, 50.0f
};

// 电压表：volume_table[i][j] 对应 temp_table[i], soc_table[j]
// 请用实际查表值填充以下数组
static const float voltage_table[NUM_TEMPS][NUM_SOC] = {
    /*–30℃*/ {1.8253f, 1.8569f, 2.0187f, 2.9304f, 3.3744f},
    /*–20℃*/ {2.0387f, 2.1140f, 2.2019f, 2.9323f, 3.3826f},
    /*–15℃*/ {2.1087f, 2.1187f, 2.2647f, 3.0267f, 3.3827f},
    /*–10℃*/ {2.2104f, 2.1819f, 2.3271f, 3.0819f, 3.3720f},
    /* –5℃*/ {2.1520f, 2.3806f, 2.5107f, 3.1030f, 3.3910f},
    /*  0℃*/ {2.2526f, 2.3151f, 2.5284f, 3.1632f, 3.3864f},
    /*  5℃*/ {2.6335f, 2.6424f, 2.7567f, 3.1793f, 3.3992f},
    /* 10℃*/ {2.7133f, 2.7669f, 2.9281f, 3.2270f, 3.4164f},
    /* 25℃*/ {2.9013f, 3.0407f, 3.1218f, 3.2773f, 3.4408f},
    /* 45℃*/ {2.7769f, 3.1021f, 3.1522f, 3.2905f, 3.4165f},
    /* 50℃*/ {2.7265f, 3.1072f, 3.1607f, 3.2991f, 3.3841f}
};
STATIC soc_adjust_record_t s_before_soc_adjust_record = {0};

#ifndef  UNITTEST
STATIC int is_soc_saving_needed(void);
STATIC int read_check_soc_save(bmu_soc_save_t * save_ptr);
STATIC void init_soc_save(void);
STATIC int init_soc_data(void);
STATIC int cal_batt_current(void);
STATIC int cal_bmu_soc(void);
STATIC int if_config_opt(void);
STATIC int is_bmu_charge_full(int dev_sn);
STATIC int is_bmu_discharge_full(int dev_sn);
STATIC int judge_bmu_charge_full_status(void);
STATIC int judge_bmu_discharge_full_status(void);
STATIC int adjust_bmu_soc(void);
STATIC soc_adjust_record_t adjust_charge_end_soc(int opt_config_status);
STATIC soc_adjust_record_t adjust_discharge_end_soc(int opt_config_status);
STATIC int save_adjust_soc_history_event(soc_adjust_record_t soc_adjust_type);

STATIC int calculate_bmu_soh(void);
STATIC int deal_calendar_decline_when_start(void);
STATIC int init_bmu_sox_save(void);
STATIC float calc_average_temp(const float_type_t *arr, int size);
STATIC int calculate_cycle_times(bmu_sox_info_t *bmu_info_in, bmu_sox_deal_t *bmu_sox_deal);
STATIC int plus_cycle_life_decline(bmu_sox_deal_t *bmu_sox_deal, bmu_sox_info_t *bmu_info_in);
STATIC int accumulate_discharge_cap(bmu_sox_info_t *bmu_info_in, float *discharge_cap, unsigned int time_diff);
STATIC int write_bmu_soh_info_to_file(bcmu_save_t * bcmu_save_tmp);
STATIC int read_bmu_soh_info_from_file(bcmu_save_t * bcmu_save_tmp);
STATIC int calculate_calender_life_decline(bmu_sox_deal_t *bmu_sox_deal, bcmu_save_t *bmu_soh_save, bmu_sox_info_t *bmu_sox_in, int bmu_index);
STATIC int plus_calender_life_decline(bmu_sox_deal_t *bmu_sox_deal, bcmu_save_t *bmu_soh_save, bmu_sox_info_t *bmu_sox_in, int bmu_index);
STATIC float ocv_to_soc(float ocv);
STATIC int cal_adjust_soh(bmu_sox_info_t *batt_info, bmu_sox_deal_t *batt_deal);
STATIC int save_sox_to_file(bcmu_save_t * bcmu_save_tmp, bmu_sox_deal_t  *bmu_sox_deal);
STATIC float calc_soh_from_decline(bmu_sox_deal_t *bmu_sox_deal);
STATIC int cal_bmu_soh(bmu_sox_info_t *batt_info, bmu_sox_deal_t *batt_deal, sid soh_sid);
STATIC float get_cell_dcr(float cell_temp);
STATIC int adjust_bmu_soe_by_ocv(void);
STATIC int adjust_bmu_soe_efficiency(void);
STATIC int cal_and_adjust_bmu_soe(void);
STATIC int update_bcmu_soc(void);
STATIC int cal_bmu_soe(void);
STATIC int if_opt_open(void);
STATIC float calculate_soe_rack1(bmu_cal_soe_t *bmu_soe);
STATIC float calculate_soe_rack2(bmu_cal_soe_t *bmu_soe, float *bmu_energy);
STATIC float limit_soe_change(float new_soe, float last_soe, int* is_limiting);
STATIC float limit_charging_soe(float new_soe, float last_soe, int* is_limiting);
STATIC float limit_discharging_soe(float new_soe, float last_soe, int* is_limiting);
STATIC float calculate_cluster_soe(bmu_cal_soe_t *bmu_soe, float *bmu_energy, float* last_cluster_soe, int* last_opt_status, int* is_limiting);
STATIC int update_soh(int bcmu_soh, int new_value);
STATIC int get_min_soh(int bmu_soh[]);
STATIC int calculate_cluster_soh(void);
STATIC int load_bmu_serial_numbers(int dev_addr, serial_numbers_t *serials);
STATIC int compare_serial_numbers(serial_numbers_t bmu_saved_serials[], int bmu_count, serial_numbers_t *bcmu_saved_serials, serial_compare_result_t *result);
STATIC int get_bcmu_serial_numbers(serial_numbers_t *bcmu_serials);
STATIC int find_majority_bmu_record(serial_numbers_t bmu_saved_serials[], const int active_bmu_indices[], int num_active_bmus, serial_numbers_t *majority_record_out, int *majority_count_out);
STATIC int judge_serial_comparison_scenario(serial_numbers_t bmu_saved_serials[], const int active_bmu_indices[], int num_active_bmus, serial_numbers_t *bcmu_saved_serials, serial_numbers_t *majority_bmu_record, int majority_bmu_count, serial_compare_result_t *result);
STATIC int sync_soh_data_to_bmu(int dev_sn, bmu_save_t saved_data, bmu_sox_deal_t current_data);
STATIC int update_bmu_soh_calc_base_value(bmu_sox_deal_t  *bmu_sox_deal);
STATIC int record_bmu_index_pending_backup(int bmu_index);
STATIC int handle_serial_comparison_result(serial_compare_result_t *result);
STATIC int check_backup_replacement_soh_change(int bmu_index, bmu_sox_deal_t *bmu_sox_deal);
STATIC int check_and_save_charge_end_info(void);
STATIC int save_charge_end_info(last_charge_info_t *last_charge_info);
STATIC int update_sys_run_cnt_after_last_chg(void);
STATIC int soc_static_calibration(soc_adjust_record_t *adjust_ret);
STATIC int update_soc_correction_alarm_judge_data(void);
STATIC int calculate_cluster_cycle_times(void);
#endif

// 函数没有被使用
STATIC int read_check_soc_save(bmu_soc_save_t * save_ptr)
{
    char *p[2] = {FL_BMU_SOC_SOH_SAVE, FL_BMU_SOC_SOHBAK_SAVE};
    int i;

    if (save_ptr == NULL)
    {
        return FALSE;
    }

    for (i = 0; i < 2; i++)
    {
        if (read_file(p[i], save_ptr, sizeof(bmu_soc_save_t)) == FAILURE)
        {
            continue;
        }

        if (save_ptr->crc != calculate_crc32((char *)save_ptr, sizeof(bmu_soc_save_t) - sizeof(int)))
        {
            continue;
        }

        return TRUE;
    }

    return FALSE;
}

/// @brief 判断当前时刻是否需要保存SOC
/// @param void
/// @return SUCCESS:需要保存 FAILURE:当前暂不保存SOC
STATIC int is_soc_saving_needed(void)
{
    float delta_soc = 0.0f;
    time_t current_time;
    int i;
    if (get_system_time(&current_time) != SUCCESS) {
        return FAILURE;
    }
    //时间变化60s，且SOC有变化，才保存SOC数据
    if(current_time - s_pre_time < 60)
    {
        return FAILURE;
    }
    s_pre_time = current_time;

    for(i = 0; i < BMU_NUM; i++)
    {
        delta_soc = fabs(s_bmu_cal_soc[i].soc - s_bmu_soc_save.soc_save[i]);
        if(delta_soc > 0.01)
        {
            s_bmu_soc_save.soc_save[i] = s_bmu_cal_soc[i].soc;
            s_bmu_sox_deal[i].save_to_file = TRUE;
        }
    }

    return SUCCESS;
}

// 函数没有被使用
STATIC void init_soc_save(void)
{
    boolean is_need_init;
    int i;
    bmu_soc_save_t * save_ptr = &s_bmu_soc_save;

    if (read_check_soc_save(save_ptr) == FALSE)
    {
        plat_memset_s(save_ptr, sizeof(bmu_soc_save_t), 0x00, sizeof(bmu_soc_save_t));

        for (i = 0; i < BMU_NUM; i++)
        {
            //文件读取失败，以下变量恢复默认值
            save_ptr->soc_save[i] = 50.0f;  //动态放出容量默认为50%
            save_ptr->batt_rate_cap[i] = TOTAL_CAPACITY;
        }
    }
    return;
}

// 此函数没有被用到，之后会删除掉
STATIC int init_soc_data(void)
{
    int bmu_soh = 0;
    int i;

    init_soc_save();
    for (i = 0; i < BMU_NUM; i++)
    {
        s_bmu_cal_soc[i].soc = s_bmu_soc_save.soc_save[i];  //读取存储的SOC

        sid sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOH, 1, i+1);      //后续直接读取调电保存的SOH
        int ret = pdt_get_data(sid_temp, &bmu_soh, sizeof(bmu_soh));
        s_bmu_cal_soc[i].cumulative_ah = s_bmu_cal_soc[i].soc * bmu_soh * TOTAL_CAPACITY / 100.0f;
    }
    return SUCCESS;
}

STATIC int cal_batt_current(void)
{
    float total_cluster_current = 0.0f;
    float total_input_current = 0.0f;
    float opt_input_current = 0.0f;
    float opt_output_current = 0.0f;
    sid sid_temp = 0;
    int i;

    int ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT, &total_cluster_current, sizeof(total_cluster_current));
    if (ret != SUCCESS)
	{
		return FAILURE;
	}

    // 计算所有优化器输入电流总和
    for (i = 0; i < BMU_NUM; i++) {
        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_OPTIMIZER_ANA_OPTIMIZER_INPUT_CURRENT, i + 1, 1);
        ret = pdt_get_data(sid_temp, &opt_input_current, sizeof(opt_input_current));
        total_input_current += (ret == SUCCESS) ? opt_input_current : 0.0f; // 优化器未配置或者通讯断时认为其电流为0
    }
    total_cluster_current -= total_input_current;

    // 计算每个优化器的输出电流并生成最终BMU电流值
    for (i = 0; i < BMU_NUM; i++) {
        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_OPTIMIZER_ANA_OPTIMIZER_OUTPUT_CURRENT, i + 1, 1);
        ret = pdt_get_data(sid_temp, &opt_output_current, sizeof(opt_output_current));
        s_bmu_current[i] = total_cluster_current + ((ret == SUCCESS) ? opt_output_current : 0.0f);
        
        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_CURRENT, i + 1, 1);
        pdt_set_data(sid_temp, &s_bmu_current[i], sizeof(float), type_float);
    }

    return SUCCESS;
}

STATIC int cal_bmu_soc(void)
{
    time_t current_time;
    float now_current = 0.0f;
    int time_delta = 0;
    float efficiency  = 0.0f;
    int bmu_soh = 0;
    int i;
    
    if (get_system_time(&current_time) != SUCCESS) {
        return FAILURE;
    }

    for (i = 0; i < BMU_NUM; i++) {
        bmu_cal_soc_t *cal_bmu_soc = &s_bmu_cal_soc[i];
        now_current = s_bmu_current[i];

        efficiency = (now_current < 0.0f) ? CHARGE_EFFICIENCY : DISCHARGE_EFFICIENCY;

        // 初始化处理
        if (cal_bmu_soc->last_time == 0) {
            cal_bmu_soc->last_time = current_time;
            continue;
        }

        // 计算时间差（秒）
        time_delta = current_time - cal_bmu_soc->last_time;
        if (time_delta <= 0 || time_delta >= 300 || fabs(now_current) <= CURRENT_DETECTION_ERROR) {
            // 时间未前进或存在跳变(修改时间)或电流小于检测误差，过滤掉
            cal_bmu_soc->last_time = current_time;
            continue;
        }

        // 安时积分计算
        float delta_ah = now_current  * (float)time_delta / 3600.0f * efficiency;
        cal_bmu_soc->cumulative_ah -= delta_ah;

        // 计算最近一次SOC校正后BMUx的累计充放电容量（取绝对值）
        cal_bmu_soc->accumulated_ah_since_correction += fabs(now_current) * (float)time_delta / 3600.0f;

        //计算实际bmu容量
        sid sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOH, 1, i + 1);
        int ret = pdt_get_data(sid_temp, &bmu_soh, sizeof(bmu_soh));
        s_bmu_real_capacity[i] = TOTAL_CAPACITY * bmu_soh / 100.0f;

        // 计算SOC
        cal_bmu_soc->soc = (cal_bmu_soc->cumulative_ah / s_bmu_real_capacity[i]) * 100.0f;
        cal_bmu_soc->soc = (cal_bmu_soc->soc > 100.0f) ? 100.0f : cal_bmu_soc->soc;
        cal_bmu_soc->soc = (cal_bmu_soc->soc < 0.0f) ? 0.0f : cal_bmu_soc->soc;

        // 更新状态记录
        cal_bmu_soc->last_time = current_time;
    }

    return SUCCESS;
}

int cal_and_adjust_bmu_soc(void)
{
    int ret = cal_batt_current();
    ret |= cal_bmu_soc();
    if (ret != SUCCESS)
    {
        return FAILURE;
    }

    adjust_bmu_soc();
    update_bcmu_soc();
    check_and_save_charge_end_info(); // 检查充电结束状态并保存信息
    update_sys_run_cnt_after_last_chg();
    is_soc_saving_needed();
    return SUCCESS;
}

/// @brief 充电结束后开始计时，记录持续未充电的时间
/// @param void
/// @return SUCCESS:最后一次充电信息更新成功
STATIC int update_sys_run_cnt_after_last_chg(void)
{
    last_charge_info_t info;
    static unsigned long sys_run_cnt_after_chg_end = 0;

    // 查看文件是否存在，不存在直接返回，避免频繁打印文件读取失败的日志
    if(is_file_exist(LAST_CHARGE_INFO_FILE_NAME) == FALSE)
    {
        return FAILURE;
    }

    // 读取之前存储的最后一次充电信息
    if (read_file(LAST_CHARGE_INFO_FILE_NAME, &info, sizeof(info)) == FAILURE)
    {
        return FAILURE;
    }

    // 最后一次充电信息被使用时，不再进行计数更新，等再次充电后才会进行计数更新。
    if(info.last_charge_info_used == TRUE)
    {
        return FAILURE;
    }

    // 上次充电结束后的运行次数为0时表明需要开始更新计数了。
    if(info.sys_run_cnt_after_last_charge_end == 0)
    {
        info.last_charge_info_used = FALSE;
        info.sys_run_cnt_after_last_charge_end = 1;
        sys_run_cnt_after_chg_end = 0;
        save_charge_end_info(&info);
        return SUCCESS;
    }
    else
    {
        // 读取文件中的计数值并递增
        sys_run_cnt_after_chg_end++;
    }

    // 充电结束后每隔一天保存一次运行时间
    // 测试时2秒1天
    if(sys_run_cnt_after_chg_end % (24 * 60 * 60 / (BCMUAPP_BCMU_TIMER / 1000)) == 0)
    {
        sys_run_cnt_after_chg_end = 0;
        info.sys_run_cnt_after_last_charge_end += 24 * 60 * 60;
        save_charge_end_info(&info);
        return SUCCESS;
    }
    return FAILURE;
}

STATIC int if_config_opt(void)
{
    int opt_config_status = 0;
    //只要BMU1配置了优化器，即认为所有BMU均配置了优化器(不存在部分配置的使用场景)
    int ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_OPTIMIZER_CONFIGURATION_STATUS, &opt_config_status, sizeof(opt_config_status));

    if (ret != SUCCESS)
	{
		return FALSE;
	}

    if (opt_config_status == 1) {
        return TRUE;
    }

    return FALSE;
}

STATIC int is_bmu_charge_full(int dev_sn)
{
    float cell_volt = 0.0f;
    float bmu_module_voltage = 0.0f;
    float bmu_cell_overvoltage_end_threshold = 0.0f;
    float bmu_module_overvoltage_end_threshold = 0.0f;
    int i;
    sid sid_temp = 0;

    sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_VOLTAGE, dev_sn, 1);
    int ret = pdt_get_data(sid_temp, &bmu_module_voltage, sizeof(bmu_module_voltage));
    ret |= pdt_get_data(SID_BATTERY_MODULE_GROUP_PARA_BMUS_CELL_OVERVOLTAGE_END_THRESHOLD, &bmu_cell_overvoltage_end_threshold, sizeof(bmu_cell_overvoltage_end_threshold));
    ret |= pdt_get_data(SID_BATTERY_MODULE_GROUP_PARA_BMUS_VOLTAGE_HIGH_END_THRESHOLD, &bmu_module_overvoltage_end_threshold, sizeof(bmu_module_overvoltage_end_threshold));
    if (ret != SUCCESS)
	{
		return FALSE;
	}

    if(bmu_module_voltage >= bmu_module_overvoltage_end_threshold)
    {
        return TRUE;
    }

    for(i = 0; i < BMU_CELL_NUM; i ++)
    {
        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE, dev_sn, i + 1);
        ret = pdt_get_data(sid_temp, &cell_volt, sizeof(cell_volt));
        if (ret != SUCCESS)
        {
            continue;
        }
        if(cell_volt >= bmu_cell_overvoltage_end_threshold)
        {
            return TRUE;
        }
    }

    return FALSE;
}

// 新增：判断BMU是否满放
STATIC int is_bmu_discharge_full(int dev_sn)
{
    float cell_volt = 0.0f;
    float bmu_module_voltage = 0.0f;
    float bmu_cell_undervoltage_end_threshold = 0.0f;
    float bmu_module_undervoltage_end_threshold = 0.0f;
    int i;
    int ret = 0;
    sid sid_temp = 0;

    sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_VOLTAGE, dev_sn, 1);
    // 获取模块电压和阈值
    ret = pdt_get_data(sid_temp, &bmu_module_voltage, sizeof(bmu_module_voltage));
    ret |= pdt_get_data(SID_BATTERY_MODULE_GROUP_PARA_BMUS_CELL_UNDERVOLTAGE_END_THRESHOLD, &bmu_cell_undervoltage_end_threshold, sizeof(bmu_cell_undervoltage_end_threshold));
    ret |= pdt_get_data(SID_BATTERY_MODULE_GROUP_PARA_BMUS_VOLTAGE_LOW_END_THRESHOLD, &bmu_module_undervoltage_end_threshold, sizeof(bmu_module_undervoltage_end_threshold));
    if (ret != SUCCESS)
    {
        return FALSE;
    }

    // 判断模块电压是否达到放电截止
    if (bmu_module_voltage < bmu_module_undervoltage_end_threshold || if_float_equal(bmu_module_voltage, bmu_module_undervoltage_end_threshold))
    {
        return TRUE;
    }

    // 判断单体电压是否达到放电截止
    for (i = 0; i < BMU_CELL_NUM; i++)
    {
        sid sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE, dev_sn, i + 1);
        ret = pdt_get_data(sid_temp, &cell_volt, sizeof(cell_volt));
        if (ret != SUCCESS)
        {
            continue;
        }
        if (cell_volt < bmu_cell_undervoltage_end_threshold || if_float_equal(cell_volt, bmu_cell_undervoltage_end_threshold))
        {
            return TRUE;
        }
    }

    return FALSE;
}

STATIC int judge_bmu_charge_full_status(void)
{
    int i = 0;
    for(i = 0; i < BMU_NUM; i++)
    {
        if (is_bmu_charge_full(i+1)) {
            return TRUE;
        }
    }
    return FALSE;
}

STATIC int judge_bmu_discharge_full_status(void)
{
    int i = 0;
    for(i = 0; i < BMU_NUM; i++)
    {
        if (is_bmu_discharge_full(i+1)) {
            return TRUE;
        }
    }
    return FALSE;
}

STATIC int save_adjust_soc_history_event(soc_adjust_record_t soc_adjust_record) {
    history_event_t history_event;
    char event_content[64] = {0};
    char time_buff[21] = {0};
    struct tm t_time;
    time_t t;
    int i = 0;
    int if_record[BMU_NUM] = {FALSE};
    char index_str[STR_LEN_16] = {0};

    RETURN_VAL_IF_FAIL(soc_adjust_record.soc_adjust_type != enum_soc_adjust_none, FAILURE);
    if (soc_adjust_record.soc_adjust_type == s_before_soc_adjust_record.soc_adjust_type) {
        for(i = 0; i < BMU_NUM ; i++){
            if (soc_adjust_record.soc_adjust_record_index[i] == TRUE && soc_adjust_record.soc_adjust_record_index[i] != s_before_soc_adjust_record.soc_adjust_record_index[i]) {
                if_record[i] = TRUE;
                s_before_soc_adjust_record.soc_adjust_record_index[i] = TRUE;
            }
        }
    } else { 
        for(i = 0; i < BMU_NUM ; i++){
            if (soc_adjust_record.soc_adjust_record_index[i] == TRUE) {
                if_record[i] = TRUE;
            }
        }
        plat_memcpy_s(&s_before_soc_adjust_record, sizeof(s_before_soc_adjust_record), &soc_adjust_record, sizeof(s_before_soc_adjust_record));
    }

    plat_memset_s(&history_event, sizeof(history_event_t), 0, sizeof(history_event_t));
    plat_memset_s(&t_time, sizeof(t_time), 0, sizeof(t_time));

    t = time(NULL);
    localtime_r(&t, &t_time);

    snprintf_s((char *)time_buff, sizeof(time_buff), "%d-%02d-%02d %02d:%02d:%02d",
                 (unsigned short)(t_time.tm_year + 1900),
                 t_time.tm_mon +1,
                 t_time.tm_mday,
                 t_time.tm_hour,
                 t_time.tm_min,
                 t_time.tm_sec);

    switch (soc_adjust_record.soc_adjust_type)
    {
        case enum_soc_adjust_charge_end:
            snprintf_s(event_content, sizeof(event_content), "%s", "charge end adjust battery module SOC");
            break;
        case enum_soc_adjust_discharge_end:
            snprintf_s(event_content, sizeof(event_content), "%s", "discharge end adjust battery module SOC");
            break;
        case enum_soc_adjust_non_platform_static:
            snprintf_s(event_content, sizeof(event_content), "%s", "Non-platform static adjust battery module SOC");
            break;
        default:
            return FAILURE;
    }
    history_event.type = HISTORY_EVENT_TYPE_OPERATE;
    for(i = 0; i < BMU_NUM ; i++) {
        if(if_record[i] == TRUE){
            snprintf_s_void(index_str, sizeof(index_str), "%d", i);
            write_event_content(&history_event.content_info, event_content, index_str, time_buff, 
                NULL, NULL, NULL, NULL);
            set_history_event_2_db(&history_event);
        }
    }
    
    return SUCCESS;
}

STATIC soc_adjust_record_t adjust_charge_end_soc(int opt_config_status)
{
    int i = 0;
    soc_adjust_record_t adjust_ret;

    plat_memset_s(&adjust_ret, sizeof(soc_adjust_record_t), 0, sizeof(soc_adjust_record_t));

    if(if_config_opt() == TRUE)
    {
        for(i = 0; i < BMU_NUM; i++)
        {
            if (is_bmu_charge_full(i+1)) {
                
                s_bmu_cal_soc[i].soc = 100.0f;
                s_bmu_cal_soc[i].cumulative_ah = s_bmu_real_capacity[i];
                s_bmu_cal_soc[i].accumulated_ah_since_correction = 0.0f;
                adjust_ret.soc_adjust_type = enum_soc_adjust_charge_end;
                adjust_ret.soc_adjust_record_index[i] = TRUE;
            }
        }
    }
    else
    {
        if(judge_bmu_charge_full_status() == TRUE)
        {
            for(i = 0; i < BMU_NUM; i++)
            {
                s_bmu_cal_soc[i].soc = 100.0f;
                s_bmu_cal_soc[i].cumulative_ah = s_bmu_real_capacity[i];
                s_bmu_cal_soc[i].accumulated_ah_since_correction = 0.0f;
                adjust_ret.soc_adjust_record_index[i] = TRUE;
            }
            adjust_ret.soc_adjust_type = enum_soc_adjust_charge_end;
        }
    }
    return adjust_ret;
}

STATIC soc_adjust_record_t adjust_discharge_end_soc(int opt_config_status)
{
    int i = 0;
    soc_adjust_record_t adjust_ret;

    plat_memset_s(&adjust_ret, sizeof(soc_adjust_record_t), 0, sizeof(soc_adjust_record_t));

    if(opt_config_status == TRUE)
    {
        for(i = 0; i < BMU_NUM; i++)
        {
            if (is_bmu_discharge_full(i+1)) 
            {
                s_bmu_cal_soc[i].soc = 0.0f;
                s_bmu_cal_soc[i].cumulative_ah = 0.0f;
                s_bmu_cal_soc[i].accumulated_ah_since_correction = 0.0f;
                adjust_ret.soc_adjust_type = enum_soc_adjust_discharge_end;
                adjust_ret.soc_adjust_record_index[i] = TRUE;
            }
        }
    }
    else
    {
        if (judge_bmu_discharge_full_status() == TRUE) {
            for(i = 0; i < BMU_NUM; i++)
            {
                s_bmu_cal_soc[i].soc = 0.0f;
                s_bmu_cal_soc[i].cumulative_ah = 0.0f;
                s_bmu_cal_soc[i].accumulated_ah_since_correction = 0.0f;
                adjust_ret.soc_adjust_record_index[i] = TRUE;
            }
            adjust_ret.soc_adjust_type = enum_soc_adjust_discharge_end;
        }
    }
    return adjust_ret;
}

STATIC int adjust_bmu_soc(void)
{
    int opt_config_status = 0;
    soc_adjust_record_t adjust_ret;

    plat_memset_s(&adjust_ret, sizeof(soc_adjust_record_t), 0, sizeof(soc_adjust_record_t));

    opt_config_status = if_config_opt();

    // SOC充满和置空校准不需要有充放电状态的前提条件
    adjust_ret = adjust_charge_end_soc(opt_config_status);
    adjust_ret = adjust_discharge_end_soc(opt_config_status);
    soc_static_calibration(&adjust_ret);

    save_adjust_soc_history_event(adjust_ret);

    return SUCCESS;
}

/* Started by AICoder, pid:192413fd85v77c214c0d095e2085871cd0771224 */
/// @brief 计算平均温度
/// @param arr 待计算数组
/// @param size 待计算数组元素个数
/// @return 平均温度
STATIC float calc_average_temp(const float_type_t *arr, int size) {
    int i = 0;
    float sum = 0.0f;

    // 检查输入是否有效
    if (arr == NULL || size <= 0) {
        return 0.0f; // 对于空或无效的输入，返回0.0
    }
    
    for (i = 0; i < size; ++i) {
        sum += arr[i].f_value;
    }

    return sum / size;
}
/* Ended by AICoder, pid:192413fd85v77c214c0d095e2085871cd0771224 */

//4、SOH计算方法 ： 包含1、日历衰减 2、循环衰减 3、附加衰减
STATIC float calc_soh_from_decline(bmu_sox_deal_t *bmu_sox_deal)
{
    float batt_soh    = (100.0 - bmu_sox_deal->calender_life_decline //日历衰减
                               - bmu_sox_deal->cycle_life_decline    //循环衰减
                               + bmu_sox_deal->added_life_decline);  //附加衰减

    return pdt_get_valid_data(batt_soh, 100.0, 45.0);//SOH的范围在45到100之间为有效值
}

/* Started by AICoder, pid:t0633f00ebf840f1478009ff00822c1de611650c */
STATIC int calculate_bmu_soh(void)
{
    int i = 0;
    sid temp_sid = 0;
    for (i = 0; i < BMU_NUM; ++i)
    {
        temp_sid = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOH, 1, i+1);
        calculate_cycle_times(&s_bmu_sox_in[i], &s_bmu_sox_deal[i]);  // 计算循环次数
        calculate_calender_life_decline(&s_bmu_sox_deal[i], &s_bcmu_save_bak, &s_bmu_sox_in[i], i);  // 计算日历衰减
        cal_adjust_soh(&s_bmu_sox_in[i], &s_bmu_sox_deal[i]);   //校准SOH
        //计算SOH
        cal_bmu_soh(&s_bmu_sox_in[i], &s_bmu_sox_deal[i], temp_sid);
    }
    return SUCCESS;
}
/* Ended by AICoder, pid:t0633f00ebf840f1478009ff00822c1de611650c */

/// @brief 计算日历寿命衰减值
/// @param cell_average_temperature 单体平均温度
/// @param bmu_sox_deal 
STATIC int calculate_calender_life_decline(bmu_sox_deal_t *bmu_sox_deal, bcmu_save_t *bmu_soh_save, bmu_sox_info_t *bmu_sox_in, int bmu_index)
{
    RETURN_VAL_IF_FAIL(bmu_sox_deal != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(bmu_soh_save != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(bmu_sox_in != NULL, FAILURE);
    
    bmu_sox_deal->calender_decline_seconds += 1;
    // 日历寿命衰减周期以天为单位
    if (bmu_sox_deal->calender_decline_seconds >= ONE_DAY_SECONDS)
    {
        plus_calender_life_decline(bmu_sox_deal, bmu_soh_save, bmu_sox_in, bmu_index);
        bmu_sox_deal->calender_decline_seconds  = (int)0;
    }
    return SUCCESS;
}

/// @brief 日历日过了一天之后进行日历衰减计算
/// @param bmu_soh_decline_data 
/// @param cell_average_temperature 
/// @param bmu_soh_save 
/// @return 
STATIC int plus_calender_life_decline(bmu_sox_deal_t *bmu_sox_deal, bcmu_save_t *bmu_soh_save, bmu_sox_info_t *bmu_sox_in, int bmu_index)
{
    float cell_average_temperature = pdt_get_valid_data(bmu_sox_in->bmu_sox_data.cell_average_temperature.f_value , 45.0, 10.0);
    // 公式待更新
    bmu_sox_deal->calender_life_decline += 10.0 / (27.5 - 0.5 * cell_average_temperature) / 365;
 
    // 获取当前时间记录为上次计算日历衰减的时间
    pdt_get_time(&bmu_sox_deal->last_calc_calender_time);
    bmu_soh_save->bmu_save_data[bmu_index].last_calc_calender_time = bmu_sox_deal->last_calc_calender_time;
    write_bmu_soh_info_to_file(bmu_soh_save);
    return SUCCESS;
}

/* Started by AICoder, pid:kcc75w256a5d9b2147380a4d009c3b22773211e8 */
/**
 * @brief   系统启动时进行日历衰减计算
 * @details 如果上次计算日历衰减时间距离现在超过一天，则在启动时进行日历衰减计算
 */
STATIC int deal_calendar_decline_when_start(void)
{
    int i, j;
    int diff_days = 0;
    for (i = 0; i < BMU_NUM; i++) 
    {
        diff_days = get_diff_days(&s_bmu_sox_deal[i].last_calc_calender_time);

        // 只处理有效天数差值（1到365天）
        if (diff_days >= 1 && diff_days <= ONE_YEAR_DAYS)
        {
            for (j = 0; j < diff_days; j++)
            {
                s_bmu_sox_in[i].bmu_sox_data.cell_average_temperature.f_value = 35.0;
                plus_calender_life_decline(&s_bmu_sox_deal[i], &s_bcmu_save_bak, &s_bmu_sox_in[i], i);
            }
        }
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:kcc75w256a5d9b2147380a4d009c3b22773211e8 */

/* Started by AICoder, pid:rf3e1o8541xfcd314756085720cbd523b6037264 */
/// @brief 将需要保存的BMU SOH写入到文件中
/// @param bcmu_save_tmp 
STATIC int write_bmu_soh_info_to_file(bcmu_save_t *bcmu_save_tmp)
{
    bcmu_save_t bcmu_soh_save;

    if (bcmu_save_tmp == NULL) {
        return FALSE; // 处理空指针输入
    }

    plat_memset_s(&bcmu_soh_save, sizeof(bcmu_save_t), 0, sizeof(bcmu_save_t));
    plat_memcpy_s(&bcmu_soh_save, sizeof(bcmu_save_t) - sizeof(int), bcmu_save_tmp, sizeof(bcmu_save_t) - sizeof(int));
    // 计算CRC，排除CRC字段
    bcmu_soh_save.crc = calculate_crc32((char *)&bcmu_soh_save, sizeof(bcmu_save_t) - sizeof(int));

    // 写入文件
    if (write_file(BMU_LAST_SOH_INFO, &bcmu_soh_save, sizeof(bcmu_save_t)) != SUCCESS) {
        return FALSE; // 处理写入失败的情况
    }

    return TRUE;
}
/* Ended by AICoder, pid:rf3e1o8541xfcd314756085720cbd523b6037264 */

/* Started by AICoder, pid:x57bf06f7bh13b7143730b1d50e28b24d211df5f */
/// @brief 从文件中读取BMU SOH信息
/// @param bmu_soh_save_tmp 
STATIC int read_bmu_soh_info_from_file(bcmu_save_t *bcmu_save_tmp)
{
    // 检查输入参数是否为空
    RETURN_VAL_IF_FAIL(bcmu_save_tmp != NULL, FALSE);

    // 读取文件内容到结构体中
    if (read_file(BMU_LAST_SOH_INFO, bcmu_save_tmp, sizeof(bcmu_save_t)) == FAILURE) {
        return FALSE;
    }

    // 验证CRC，排除CRC字段本身
    if (bcmu_save_tmp->crc != calculate_crc32((char *)bcmu_save_tmp, sizeof(bcmu_save_t) - sizeof(int))) {
        return FALSE;
    }

    return TRUE;
}
/* Ended by AICoder, pid:x57bf06f7bh13b7143730b1d50e28b24d211df5f */


/// @brief 根据单次循环计算循环次数
/// @param bmu_info_in 电池模块信息（包含参数和实时量）
/// @param soh_decline_data SOH衰减数据
STATIC int calculate_cycle_times(bmu_sox_info_t *bmu_info_in, bmu_sox_deal_t *bmu_sox_deal)
{
    RETURN_VAL_IF_FAIL(bmu_info_in != NULL, FALSE);
    RETURN_VAL_IF_FAIL(bmu_sox_deal != NULL, FALSE);

    bmu_sox_deal->total_discharge_cap = pdt_get_valid_data(bmu_sox_deal->total_discharge_cap, TOTAL_CAPACITY, 0.0f);

    // 以BCMUAPP_BCMU_TIMER周期进行累计放电容量计算
    accumulate_discharge_cap(bmu_info_in, &bmu_sox_deal->total_discharge_cap, 1*1000);

    // 目前以放出80%可用容量为阈值判断是否达到一次循环
    if(bmu_sox_deal->total_discharge_cap > TOTAL_CAPACITY * 0.8f * bmu_info_in->bmu_sox_data.bmu_soh.i_value / 100.0f)
    {
        bmu_sox_deal->total_discharge_cap = 0;
        bmu_sox_deal->bmu_cycle_times++;
        plus_cycle_life_decline(bmu_sox_deal, bmu_info_in );
        return TRUE;
    }
    return FALSE;
}

/// @brief 根据安时积分法计算累计放电容量
/// @param bmu_info_in 电池模块信息（包含参数和实时量）
/// @param discharge_cap 
/// @param time_diff 
/// @return 
STATIC int accumulate_discharge_cap(bmu_sox_info_t *bmu_info_in, float *discharge_cap, unsigned int time_diff)
{
    // 放电电流超过最小检测电流时才进行安时积分计算累计放电容量
    if (bmu_info_in->bmu_sox_data.bmu_curr.f_value > CURRENT_DETECTION_ERROR)
    {
        *discharge_cap += bmu_info_in->bmu_sox_data.bmu_curr.f_value * time_diff / 3600.0;
    }
    return SUCCESS;
}
 
/**
 * @brief 附加循环寿命衰减计算
 * @param bmu_info_in 电池信息
 * @param soh_decline_data  soh衰减数据
 */
STATIC int plus_cycle_life_decline(bmu_sox_deal_t *bmu_sox_deal, bmu_sox_info_t *bmu_info_in)
{
    float ftimes  = 0.0;
    float fTemp = bmu_info_in->bmu_sox_data.cell_average_temperature.f_value;
    ftimes = -5.25 * fTemp * fTemp + 245 * fTemp + 1356.3;
    ftimes = pdt_get_valid_data(ftimes, 4500.0, 1500.0);

    // 当前代码暂时屏蔽，可以根据电芯循环次数等比例放大拟合的循环次数
    /*T_BmsPACKManufactStruct tPackInfo;
    plat_memset_s(&tPackInfo, 0, sizeof(T_BmsPACKManufactStruct));
    readPackManufact(&tPackInfo);
    if(tPackInfo.wCellCycleTimes != 0)
    {
        ftimes = ftimes*tPackInfo.wCellCycleTimes/((FLOAT)CYCLE_TIMES_MAX);//等比例扩大
    }*/

    if(ftimes >= 1500.0 && ftimes <= 8000.0)
    {
        bmu_sox_deal->cycle_life_decline += (10.0 / ftimes);
    }
    return TRUE;
}

STATIC int cal_bmu_soh(bmu_sox_info_t *batt_info, bmu_sox_deal_t *batt_deal, sid soh_sid) {
    int batt_soh = 100;
    
    RETURN_VAL_IF_FAIL(batt_info != NULL, FALSE);
    RETURN_VAL_IF_FAIL(batt_deal != NULL, FALSE);

    batt_deal->batt_SOH = calc_soh_from_decline(batt_deal);
    batt_soh = (int)round(batt_deal->batt_SOH);

    if (batt_soh != batt_info->bmu_sox_data.bmu_soh.i_value)
    {
        batt_deal->save_to_file = TRUE;
        pdt_set_data(soh_sid, &batt_soh, sizeof(batt_soh), type_int);
    }
    return TRUE;
}

//计算单体DCR
//输入：单体温度
STATIC float get_cell_dcr(float cell_temp)
{
    float x1, y1;
    float dcr = 0.0;
    int i = 0;
    //todo:确定温度-dcr数据点是否需要修改
    float points[][2] = {
        {-10.0, 20.0},
        {0.0, 12.0},
        {10.0, 6.0},
        {15.0, 4.0},
        {25.0, 3.0},
        {45.0, 2.0}, 
    };

    x1 = points[0][0];
    y1 = points[0][1];

    if (cell_temp >= 45)
    {
        return (float)2.0;
    }
    else if(cell_temp <= -10)
    {
        return (float)20.0;
    }
    else
    {
        for(i=0; i<sizeof(points)/sizeof(points[0]); i++)
        {
            if (cell_temp <= points[i][0])
            {
                if(SUCCESS == get_linear_point(x1, y1,points[i][0],points[i][1],cell_temp, &dcr))
                {
                    return dcr;
                }
                else
                {
                    break;
                }
            }
            x1 = points[i][0];
            y1 = points[i][1];
        }
    }    
    
    return (float)3.0;
}

/***************************************************************************
 * @brief    载入电池电芯OCV信息
 **************************************************************************/
STATIC int load_cell_ocv(bmu_sox_info_t *bmu_soc_in)
{
    float cell_res = 0.01;
    float_type_t cell_volt[BMU_CELL_NUM];
    int cell_num = 0;

    RETURN_VAL_IF_FAIL(bmu_soc_in != NULL, FAILURE);

    cell_res = get_cell_dcr(bmu_soc_in->bmu_sox_data.cell_average_temperature.f_value);

    plat_memcpy_s(cell_volt, BMU_CELL_NUM * sizeof(bmu_soc_in->bmu_sox_data.cell_volt[0]), &bmu_soc_in->bmu_sox_data.cell_volt[0], BMU_CELL_NUM * sizeof(bmu_soc_in->bmu_sox_data.cell_volt[0]));

    //排序
    bubble_sorts(cell_volt, BMU_CELL_NUM);

    cell_num = (sizeof(cell_volt)/sizeof(float_type_t)) / 2;
    bmu_soc_in->bmu_sox_data.cell_ocv.f_value = (cell_volt[cell_num].f_value - bmu_soc_in->bmu_sox_data.bmu_curr.f_value / 1000 * cell_res);
    return SUCCESS;
}

/**
 * @brief 开路电压与电池SOC的拟合曲线
 * @param  ocv 电芯开路电压
 */
STATIC float ocv_to_soc(float ocv)
{ // 25℃时标准OCV-SOC曲线(0-8%校准值)
    float soc = 0.0;
    ocv = 0.65 * ocv + 1.12; // 将其他温度下OCV拟合转化为25摄氏度OCV减少温度影响
    //此处，不同电池则使用不同拟合曲线。暂定使用光宇的拟合曲线
    soc = 132.98*ocv*ocv*ocv -1147.5*ocv*ocv + 3307.1*ocv - 3182.4; //光宇拟合曲线
//  soc = 46.492*ocv*ocv*ocv -377.41*ocv*ocv + 1024.1*ocv - 927.9;  //统一拟合曲线
    return pdt_get_valid_data(soc, 8.0f, 0.0f);
}

/**
 * @brief 附加衰减,校准时发生
 * @param 
 * @param  
 */
STATIC int cal_adjust_soh(bmu_sox_info_t *batt_info, bmu_sox_deal_t *batt_deal)
{
    /*
    满足OCV下端修正条件，才开始SOH修正
    */
    float batt_rate_cap = 0.0;
    float batt_discharge_all_cap = 0.0;
    float added_life_decline_bak = 0.0;
    float soc = 0.0;
    float min = 0.0;

    RETURN_VAL_IF_FAIL(batt_info != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(batt_deal != NULL, FAILURE);

    batt_rate_cap = TOTAL_CAPACITY;
    added_life_decline_bak = batt_deal->added_life_decline;

    if (batt_info->bmu_sox_data.cell_ocv.f_value > OCV_MAX || batt_info->bmu_sox_data.cell_ocv.f_value  < OCV_MIN
        || !batt_deal->discharge_from_full || batt_deal->discharge_cap_for_celebrate <
        0.8f*batt_rate_cap || batt_info->bmu_sox_data.cell_average_temperature.f_value <= (float)0 )
    {
        return FAILURE;
    }

    soc = pdt_get_valid_data(ocv_to_soc(batt_info->bmu_sox_data.cell_ocv.f_value), 5.0, 0.0);
    batt_discharge_all_cap = batt_deal->discharge_cap_for_celebrate/(100.0 - soc)*100;

   // if(batt_info->bmu_sox_data.cell_average_temperature.f_value >= 15 && batt_rate_cap >= BATT_CAP_MIN)
    if(batt_info->bmu_sox_data.cell_average_temperature.f_value >= 15)
    {  // 充满到放电跳变时，SOH校正并且放电时温度不低于15度
        batt_deal->added_life_decline   = (batt_discharge_all_cap / batt_rate_cap *100) - (100.0 - batt_deal->added_life_decline - batt_deal->cycle_life_decline);
        min = MIN(5.0, batt_deal->calender_life_decline);
        batt_deal->added_life_decline   = pdt_get_valid_data(batt_deal->added_life_decline , 0.0f, -1.0f*min);
    }

    if (fabs(added_life_decline_bak - batt_deal->added_life_decline) >= 1)
    {
        batt_deal->save_to_file        = TRUE;
    }
    return SUCCESS;
}

/* Started by AICoder, pid:f549281816p4e0614baf0ac67009d1266a33c66e */
STATIC int save_sox_to_file(bcmu_save_t * bcmu_save_tmp, bmu_sox_deal_t  *bmu_sox_deal) {
    int need_save = FALSE;
    int i = 0;

    RETURN_VAL_IF_FAIL(bcmu_save_tmp != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(bmu_sox_deal != NULL, FAILURE);
    // 备件替换过程中BMU存储的SOH都同步上来后，更新BMU SOH计算的中间值。
    if((s_replace_finish_flag == enum_backup_doing) && (s_soh_sync_finish_flag == TRUE))
    {
        update_bmu_soh_calc_base_value(bmu_sox_deal);
    }
    // 检查是否有任何一个成员的save_to_file为TRUE
    for (i = 0; i < BMU_NUM; i++) {
        // 识别备件替换SOH参数是否发生变化
        check_backup_replacement_soh_change(i, bmu_sox_deal);
        if (bmu_sox_deal[i].save_to_file) {
            need_save = TRUE;
            // 先检查并同步SOH数据，使用保存在s_bcmu_save_bak中的历史数据进行比较
            sync_soh_data_to_bmu(i+1, s_bcmu_save_bak.bmu_save_data[i], bmu_sox_deal[i]);
            bcmu_save_tmp->bmu_save_data[i].batt_cap                 = bmu_sox_deal[i].batt_cap;
            bcmu_save_tmp->bmu_save_data[i].added_life_decline       = bmu_sox_deal[i].added_life_decline;
            bcmu_save_tmp->bmu_save_data[i].bmu_cycle_times          = bmu_sox_deal[i].bmu_cycle_times;
            bcmu_save_tmp->bmu_save_data[i].last_calc_calender_time  = bmu_sox_deal[i].last_calc_calender_time;
            bcmu_save_tmp->bmu_save_data[i].total_discharge_cap      = bmu_sox_deal[i].total_discharge_cap;
            bcmu_save_tmp->bmu_save_data[i].calender_life_decline    = bmu_sox_deal[i].calender_life_decline;
            bcmu_save_tmp->bmu_save_data[i].cycle_life_decline       = bmu_sox_deal[i].cycle_life_decline;
            bcmu_save_tmp->bmu_save_data[i].bmu_soc                  = s_bmu_cal_soc[i].soc;                     // 保存SOC
            bcmu_save_tmp->bmu_save_data[i].cumulative_ah            = s_bmu_cal_soc[i].cumulative_ah;           // 保存累计AH
            bcmu_save_tmp->bmu_save_data[i].bmu_soe                  = s_bmu_cal_soe[i].soe;                     // 保存SOE
            bcmu_save_tmp->bmu_save_data[i].cumulative_wh            = s_bmu_cal_soe[i].cumulative_wh;           // 保存累计能量
            bcmu_save_tmp->bmu_save_data[i].soe_chg_efficiency       = s_bmu_cal_soe[i].soe_chg_efficiency;      // 充电SOE效率
            bcmu_save_tmp->bmu_save_data[i].soe_dischg_efficiency    = s_bmu_cal_soe[i].soe_dischg_efficiency;   // 放电SOE效率
            bcmu_save_tmp->bmu_save_data[i].accumulated_ah_since_correction = s_bmu_cal_soc[i].accumulated_ah_since_correction; // 保存累计充放电容量
        }
    }

    if (need_save) {
        // 将所有的save_to_file置为FALSE
        for (i = 0; i < BMU_NUM; i++) {
            bmu_sox_deal[i].batt_cap_save = bmu_sox_deal[i].batt_cap;
            bmu_sox_deal[i].save_to_file = FALSE;
        }
        // 执行写文件操作
        write_bmu_soh_info_to_file(bcmu_save_tmp);
    }

    return SUCCESS;  // 返回0表示成功
}

STATIC int update_bmu_soh_calc_base_value(bmu_sox_deal_t  *bmu_sox_deal)
{
    int i;
    sid sid_temp = 0x00l;
    float temp_float = 0.0f;
    time_base_t temp_time = {0};
    int bmu_index_to_update[BMU_NUM] = {0};

    RETURN_VAL_IF_FAIL(bmu_sox_deal != NULL, FAILURE);

    // 获取需要更新SOH基准数据的BMU索引
    get_bmu_index_pending_backup(bmu_index_to_update);

    for (i = 0; i < BMU_NUM; i++)
    {
        // 仅更新更换的BMU的SOH
        if ((bmu_index_to_update[i] != -1) && (bmu_soh_data_syned[i] == TRUE))
        {
            int bmu_actual_index = bmu_index_to_update[i];
            int dev_sn = bmu_actual_index + 1;

            sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CALENDAR_LIFE_DECLINE, dev_sn, 1);
            if (pdt_get_data(sid_temp, &temp_float, sizeof(temp_float)) == SUCCESS)
            {
                bmu_sox_deal[bmu_actual_index].calender_life_decline = temp_float;
            }

            sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CYCLE_LIFE_DECLINE, dev_sn, 1);
            if (pdt_get_data(sid_temp, &temp_float, sizeof(temp_float)) == SUCCESS)
            {
                bmu_sox_deal[bmu_actual_index].cycle_life_decline = temp_float;
            }

            sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_ADDED_LIFE_DECLINE, dev_sn, 1);
            if (pdt_get_data(sid_temp, &temp_float, sizeof(temp_float)) == SUCCESS)
            {
                bmu_sox_deal[bmu_actual_index].added_life_decline = temp_float;
            }

            sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_RATED_CAPACITY, dev_sn, 1);
            if (pdt_get_data(sid_temp, &temp_float, sizeof(temp_float)) == SUCCESS)
            {
                bmu_sox_deal[bmu_actual_index].total_discharge_cap = temp_float;
            }

            sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_LAST_CALC_CALENDER_TIME, dev_sn, 1);
            if (pdt_get_data(sid_temp, &temp_time, sizeof(temp_time)) == SUCCESS)
            {
                bmu_sox_deal[bmu_actual_index].last_calc_calender_time = temp_time;
            }

            // 清除该BMU的待同步标记
            s_bmu_index_pending_backup[i] = -1;
            // 重置该BMU的SOH数据已同步标记，为下次可能的同步做准备
            bmu_soh_data_syned[i] = FALSE;
            // 标记SOH数据有变更，需要重新计算SOH保存文件
            bmu_sox_deal[bmu_actual_index].save_to_file = TRUE;
        }
    }
    // 备件替换置位为完成标志
    s_replace_finish_flag = enum_backup_done;
    // 备件替换完成，初始化SOH同步完成标志为FALSE，待下一次判断使用
    s_soh_sync_finish_flag = FALSE;
    return SUCCESS;
}

/**
 * @brief 检查备件替换SOH参数变更
 * @param bmu_index BMU索引(0-based)
 * @param bmu_sox_deal BMU SOX处理数据数组
 * @note 检测新的备件替换SOH参数变更，当变更时增加calender_life_decline，其余两个衰减置0
 */
STATIC int check_backup_replacement_soh_change(int bmu_index, bmu_sox_deal_t *bmu_sox_deal)
{
    static int s_last_backup_replacement_soh[BMU_NUM] = {0};
    static int s_first_run_after_power_on = TRUE;
    int current_backup_replacement_soh = 0;
    sid sid_temp = 0ll;
    int i = 0;

    // 首次上电，读取所有BMU的备件替换SOH参数进行备份数据更新。
    if (s_first_run_after_power_on == TRUE)
    {
        for (; i < BMU_NUM; i++)
        {
            sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_BACKUP_REPLACEMENT_SOH, 1, i + 1);
            if (pdt_get_data(sid_temp, &current_backup_replacement_soh, sizeof(current_backup_replacement_soh)) != SUCCESS)
            {
                return FAILURE;
            }
            s_last_backup_replacement_soh[i] = current_backup_replacement_soh;
        }
        s_first_run_after_power_on = FALSE;
        return SUCCESS;
    }

    // 读取当前备件替换SOH参数
    sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_BACKUP_REPLACEMENT_SOH, 1, bmu_index + 1);
    if (pdt_get_data(sid_temp, &current_backup_replacement_soh, sizeof(current_backup_replacement_soh)) != SUCCESS)
    {
        return FAILURE;
    }

    // 检查参数是否发生变更
    if (s_last_backup_replacement_soh[bmu_index] != current_backup_replacement_soh)
    {
        // 参数发生变更，更新历史值
        s_last_backup_replacement_soh[bmu_index] = current_backup_replacement_soh;
        // 设置save_to_file标志
        bmu_sox_deal[bmu_index].save_to_file = TRUE;
        // 增加calender_life_decline
        bmu_sox_deal[bmu_index].calender_life_decline = 100- current_backup_replacement_soh;
        // 将其余两个衰减参数置为0
        bmu_sox_deal[bmu_index].cycle_life_decline = 0.0f;
        bmu_sox_deal[bmu_index].added_life_decline = 0.0f;
        return SUCCESS;
    }
    return FAILURE;
}

/**
 * @brief 判断SOH关键数据是否变更并同步给BMU
 * @param dev_sn BMU设备编号
 * @param saved_data 上次保存的数据
 * @param current_data 当前数据
 * @return 成功返回SUCCESS
 * @note 无备件替换或者备件替换完成，才会真正触发数据同步。
 */
STATIC int sync_soh_data_to_bmu(int dev_sn, bmu_save_t saved_data, bmu_sox_deal_t current_data)
{
    if(s_replace_finish_flag == enum_backup_doing)
    {
        return FAILURE;
    }
    if(s_replace_finish_flag == enum_backup_done)
    {
        s_replace_finish_flag = enum_backup_none;
    }
    boolean need_sync = FALSE;
    sid sid_temp = 0ll;

    if (is_bmu_exist(dev_sn) != NORMAL || is_bmu_comm_normal(dev_sn) != NORMAL)
    {
        return FAILURE;
    }

    // 检查关键SOH数据是否发生变更，判断是否需要触发同步
    if (fabs(saved_data.added_life_decline - current_data.added_life_decline) >= FLOAT_EPSILON)
    {
        need_sync = TRUE;
    }
    if (fabs(saved_data.calender_life_decline - current_data.calender_life_decline) >= FLOAT_EPSILON)
    {
        need_sync = TRUE;
    }

    if (fabs(saved_data.cycle_life_decline - current_data.cycle_life_decline) >= FLOAT_EPSILON)
    {
        need_sync = TRUE;
    }

    if(memcmp(&saved_data.last_calc_calender_time, &current_data.last_calc_calender_time, sizeof(time_base_t)) != 0)
    {
        need_sync = TRUE;
    }

    // 如果SOH数据有变更，则发消息同步给BMU。
    if (need_sync)
    {
        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CALENDAR_LIFE_DECLINE, dev_sn, 1);
        pdt_set_data(sid_temp, &current_data.calender_life_decline, sizeof(current_data.calender_life_decline), type_float);

        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CYCLE_LIFE_DECLINE, dev_sn, 1);
        pdt_set_data(sid_temp, &current_data.cycle_life_decline, sizeof(current_data.cycle_life_decline), type_float);

        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_ADDED_LIFE_DECLINE, dev_sn, 1);
        pdt_set_data(sid_temp, &current_data.added_life_decline, sizeof(current_data.added_life_decline), type_float);

        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_RATED_CAPACITY, dev_sn, 1);
        pdt_set_data(sid_temp, &current_data.total_discharge_cap, sizeof(current_data.total_discharge_cap), type_float);

        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_LAST_CALC_CALENDER_TIME, dev_sn, 1);
        pdt_set_data(sid_temp, &current_data.last_calc_calender_time, sizeof(current_data.last_calc_calender_time), type_time);

        return send_soh_data_to_bmu(dev_sn);
    }
    return SUCCESS;
}

STATIC int init_bmu_sox_save(void)
{
    int i = 0;
    int soe = 0;
    sid sid_temp = 0ll;

    if(FALSE == read_bmu_soh_info_from_file(&s_bcmu_save_bak)) {
        plat_memset_s(&s_bcmu_save_bak, sizeof(s_bcmu_save_bak), 0, sizeof(s_bcmu_save_bak));
        for( i = 0; i < BMU_NUM; i++)
        {
            s_bmu_sox_deal[i].save_to_file = TRUE;

            //SOH初始化
            pdt_get_time(&s_bcmu_save_bak.bmu_save_data[i].last_calc_calender_time);
            s_bcmu_save_bak.bmu_save_data[i].batt_cap  = 0.5*TOTAL_CAPACITY;
            s_bcmu_save_bak.bmu_save_data[i].accumulated_ah_since_correction = 0.0f; // 初始化累计充放电容量为0

            // SOC初始化
            s_bcmu_save_bak.bmu_save_data[i].bmu_soc                                     = 50.0f;
            s_bcmu_save_bak.bmu_save_data[i].cumulative_ah = TOTAL_CAPACITY / 2;

            // SOE初始化
            s_bcmu_save_bak.bmu_save_data[i].bmu_soe                                     = 50.0f;
            s_bcmu_save_bak.bmu_save_data[i].soe_chg_efficiency                      = 1.0f;
            s_bcmu_save_bak.bmu_save_data[i].soe_dischg_efficiency                   = 1.0f;
            s_bcmu_save_bak.bmu_save_data[i].cumulative_wh                           = NOMINAL_ENERGY / 2;
        }
        s_bcmu_save_bak.bcmu_soh = BCMU_SOH_MAX;
        // 初始化电池簇循环次数相关数据
        s_bcmu_save_bak.bcmu_cycle_times = 0;
        s_bcmu_save_bak.bcmu_total_discharge_cap = 0.0f;

    }

    for( i = 0; i < BMU_NUM; i++)
    {
        s_bmu_sox_deal[i].last_calc_calender_time                = s_bcmu_save_bak.bmu_save_data[i].last_calc_calender_time;
        s_bmu_sox_deal[i].bmu_cycle_times                        = s_bcmu_save_bak.bmu_save_data[i].bmu_cycle_times;
        s_bmu_sox_deal[i].added_life_decline                     = s_bcmu_save_bak.bmu_save_data[i].added_life_decline;
        s_bmu_sox_deal[i].total_discharge_cap                    = s_bcmu_save_bak.bmu_save_data[i].total_discharge_cap;
        s_bmu_sox_deal[i].calender_life_decline                  = s_bcmu_save_bak.bmu_save_data[i].calender_life_decline;
        s_bmu_sox_deal[i].cycle_life_decline                     = s_bcmu_save_bak.bmu_save_data[i].cycle_life_decline;  
        s_bmu_sox_deal[i].batt_SOH                               = calc_soh_from_decline(&s_bmu_sox_deal[i]);
        s_bmu_sox_deal[i].batt_cap                               = MIN(s_bcmu_save_bak.bmu_save_data[i].batt_cap, BATT_SOC_MAX*(s_bmu_sox_deal[i].batt_SOH / 100 * TOTAL_CAPACITY));

        // 初始化SOC保存基准值，用于is_soc_saving_needed函数中的变化检测
        s_bmu_soc_save.soc_save[i] = s_bcmu_save_bak.bmu_save_data[i].bmu_soc;

        // 初始化SOC相关数据
        s_bmu_cal_soc[i].soc                                     = s_bcmu_save_bak.bmu_save_data[i].bmu_soc;
        s_bmu_cal_soc[i].cumulative_ah                           = s_bcmu_save_bak.bmu_save_data[i].cumulative_ah;
        s_bmu_cal_soc[i].accumulated_ah_since_correction = s_bcmu_save_bak.bmu_save_data[i].accumulated_ah_since_correction; // 读取存储的累计充放电容量
        // 初始化SOE相关数据
        s_bmu_cal_soe[i].soe                                     = s_bcmu_save_bak.bmu_save_data[i].bmu_soe;
        s_bmu_cal_soe[i].soe_chg_efficiency                      = s_bcmu_save_bak.bmu_save_data[i].soe_chg_efficiency;
        s_bmu_cal_soe[i].soe_dischg_efficiency                   = s_bcmu_save_bak.bmu_save_data[i].soe_dischg_efficiency;
        s_bmu_cal_soe[i].cumulative_wh                           = s_bcmu_save_bak.bmu_save_data[i].cumulative_wh;

        // 初始设置SOE信息，防止未计算导致的invalid
        soe = FLOAT_TO_INT32S(s_bmu_cal_soe[i].soe);
        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOE, 1, i + 1);
        pdt_set_data(sid_temp, &soe, sizeof(soe), type_int);
    }

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH, &s_bcmu_save_bak.bcmu_soh, sizeof(int), type_int);
    return TRUE;
}

int init_sox(void) {
    deal_calendar_decline_when_start();
    init_bmu_sox_save();
    save_sox_to_file(&s_bcmu_save_bak, &s_bmu_sox_deal[0]);
    return SUCCESS;
}

int load_sox_data(void)
{ 
    int i = 0;
    sid temp_sid = 0;

    //加载数据字典中的值
    static sig_2_sid_info_t sig_2_sid_map[] = {
        {&s_bmu_sox_in[0].bmu_sox_data.bmu_curr, SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_CURRENT, 1, sizeof(float), type_float},
        {s_bmu_sox_in[0].bmu_sox_data.cell_temperature, SID_BATTERY_MODULE_ANA_BMU_CELL_TEMPERATURE, BMU_CELL_TEMP_NUM, sizeof(float), type_float},
        {s_bmu_sox_in[0].bmu_sox_data.cell_volt, SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE, BMU_CELL_NUM, sizeof(float), type_float},
        //{&s_bmu_sox_in[0].bmu_sox_para.batt_rated_cap, SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_CURRENT, 1, sizeof(float), type_float},
        {NULL, 0, 0, 0, 0} // 确保最后一个元素的type字段也初始化为0
    };

    static dev_2_sid_info_t dev_2_sid_info = {
        sig_2_sid_map,
        BMU_NUM,
        (sizeof(sig_2_sid_map) / sizeof(sig_2_sid_info_t)) - 1,
        sizeof(bmu_sox_info_t),
        NULL
    };
    get_sm_data_to_custom_struct_by_dev(&dev_2_sid_info);

    //加载需要计算的值
    for(i = 0; i < BMU_NUM; i++) {
        temp_sid = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOH, 1, i+1);
        pdt_get_data(temp_sid, &s_bmu_sox_in[i].bmu_sox_data.bmu_soh, sizeof(int));
        s_bmu_sox_in[i].bmu_sox_data.cell_average_temperature.f_value = calc_average_temp(s_bmu_sox_in[i].bmu_sox_data.cell_temperature, BMU_CELL_TEMP_NUM);  // 计算电芯平均温度
        load_cell_ocv(&s_bmu_sox_in[i]);
    }
    
    return SUCCESS;
}

int calculate_sox(void) {
    cal_and_adjust_bmu_soc();
    calculate_bmu_soh();
    cal_and_adjust_bmu_soe();
    calculate_cluster_soe(s_bmu_cal_soe, s_bmu_real_energy, &s_last_soe, &s_last_opt_status, &s_is_limiting);
    calculate_cluster_soh();
    calculate_cluster_cycle_times();
    update_soc_correction_alarm_judge_data();
    save_sox_to_file(&s_bcmu_save_bak, &s_bmu_sox_deal[0]);
    return SUCCESS;
}

/// @brief 计算电池簇循环次数
STATIC int calculate_cluster_cycle_times(void)
{
    float cluster_current = 0.0f;
    int cluster_cycle_times_param = 0;
    int ret = SUCCESS;
    int i = 0;
    int bcmu_soh = 0;

    // 获取电池簇总电流
    ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT,
                       &cluster_current, sizeof(cluster_current));
    ret |= pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH, &bcmu_soh, sizeof(int));
    if (ret != SUCCESS) {
        return FAILURE;
    }

    // 获取当前参数中的循环次数（支持北向协议设置）
    ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CYCLE_TIMES,
                       &cluster_cycle_times_param, sizeof(cluster_cycle_times_param));
    if (ret == SUCCESS && cluster_cycle_times_param != s_bcmu_save_bak.bcmu_cycle_times) {
        // 参数被北向协议修改，更新本地计算值
        s_bcmu_save_bak.bcmu_cycle_times = cluster_cycle_times_param;
    }

    // 限制累计放电容量在有效范围内
    s_bcmu_save_bak.bcmu_total_discharge_cap = pdt_get_valid_data(s_bcmu_save_bak.bcmu_total_discharge_cap, TOTAL_CAPACITY, 0.0f);

    // 放电电流超过最小检测电流时才进行安时积分计算累计放电容量
    if (cluster_current > CURRENT_DETECTION_ERROR) {
        // 按照BCMUAPP_BCMU_TIMER周期(1秒)进行累计放电容量计算
        s_bcmu_save_bak.bcmu_total_discharge_cap += cluster_current * BCMUAPP_BCMU_TIMER / 3600.0f / 1000.0f;
    }

    // 按照电池累计放出容量达到标称容量算作一次循环次数
    if (s_bcmu_save_bak.bcmu_total_discharge_cap >= (TOTAL_CAPACITY * bcmu_soh / 100)) {
        s_bcmu_save_bak.bcmu_total_discharge_cap = 0.0f;
        s_bcmu_save_bak.bcmu_cycle_times++;

        // 更新参数中的循环次数
        ret = pdt_set_para(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CYCLE_TIMES,
                           &s_bcmu_save_bak.bcmu_cycle_times, sizeof(s_bcmu_save_bak.bcmu_cycle_times));
        // 更新文件的的电池簇循环次数
        write_bmu_soh_info_to_file(&s_bcmu_save_bak);
        // 和BMU备件存储的SOH数据一起同步给所有BMU
        if (s_replace_finish_flag != enum_backup_doing)
        {
            for (; i < BMU_NUM; i++)
            {
                send_soh_data_to_bmu(i + 1);
            }
        }
        return TRUE;
    }
    return FALSE;
}

STATIC int update_bcmu_soc(void)
{
    float min_soc = 100.0f;
    int i;
    sid sid_temp;
    int set_bmu_soc = 0;
    int set_cluster_soc = 0;
    int exist_bmu_num = 0;
    int comm_fail_bmu_num = 0;

    // 遍历所有BMU找出最小SOC
    for (i = 0; i < BMU_NUM; i++)
    {
        if (is_bmu_exist(i + 1) == NORMAL)
        {
            exist_bmu_num++;
            if (is_bmu_comm_normal(i + 1) == FAULT)
            {
                comm_fail_bmu_num++;
                continue;
            }
        }
        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOC, 1, i+1);
        set_bmu_soc = (int)round(s_bmu_cal_soc[i].soc);
        pdt_set_data(sid_temp, &set_bmu_soc, sizeof(int), type_int);

        if (s_bmu_cal_soc[i].soc < min_soc)
        {
            min_soc = s_bmu_cal_soc[i].soc;
        }
    }
    // 任意在位的BMU通讯失败会造成电池簇充、放电保护，电池簇SOC维持上一次的值，否则更新BCMU SOC。
    if ((exist_bmu_num != 0) && (comm_fail_bmu_num == 0))
    {
        set_cluster_soc = (int)round(min_soc);
        pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &set_cluster_soc, sizeof(int), type_int);
    }

    return SUCCESS;
}

/* Started by AICoder, pid:c059el576bpfaf2142ab0b89607bb480aa706046 */
STATIC int cal_bmu_soe(void)
{
    int i = 0;
    int ret = 0;
    long long time_delta = 0;
    long long current_time = 0;
    sid sid_mod_volt = 0ll;
    float now_current = 0.0f;
    float voltage = 0.0f;
    float delta_wh = 0.0f;
    float bmu_soh = 100.0f;
    bmu_cal_soe_t* cal_bmu_soe = NULL;

    if (get_system_time(&current_time) != SUCCESS)
    {
        return FAILURE;
    }

    for (i = 0; i < BMU_NUM; i++) {
        cal_bmu_soe = &s_bmu_cal_soe[i];
        now_current = s_bmu_current[i];

        // 首次运行初始化
        if (cal_bmu_soe->last_time == 0) {
            cal_bmu_soe->last_time = current_time;
            continue;
        }

        // 计算时间差并进行有效性验证
        time_delta = current_time - cal_bmu_soe->last_time;
        if (time_delta <= 0 || time_delta >= 300) {
            cal_bmu_soe->last_time = current_time;
            continue;
        }

        // 读取模块电压
        sid_mod_volt = SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_VOLTAGE, i + 1, 1);
        voltage = 0.0f;
        if (SUCCESS != pdt_get_data(sid_mod_volt, &voltage, sizeof(voltage)))
        {
            continue;
        }

        // 获取实际能量容量Emax
        bmu_soh = 100.0f;
        sid sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOH, 1, i + 1);
        pdt_get_data(sid_temp, &bmu_soh, sizeof(bmu_soh));

        s_bmu_real_energy[i] = NOMINAL_ENERGY * bmu_soh  / 100.0f; // Emax = SOH*Qnom*Vnom=SOH*Enom
        if (s_bmu_real_energy[i] < FLOAT_EPSILON)
        {
            continue;
        }

        // 根据充放电状态选择效率系数
        if (now_current < -CURRENT_DETECTION_ERROR)
        {
            // 该BMU充电
            delta_wh = voltage * now_current * (float)time_delta / 3600.0f * s_bmu_cal_soe[i].soe_chg_efficiency;
            cal_bmu_soe->cumulative_wh += fabs(delta_wh);
        }
        else if (now_current > CURRENT_DETECTION_ERROR)
        {
            if (s_bmu_cal_soe[i].soe_dischg_efficiency < FLOAT_EPSILON)
            {
                continue;
            }
            // 该BMU放电
            delta_wh = voltage * now_current * (float)time_delta / 3600.0f / s_bmu_cal_soe[i].soe_dischg_efficiency;
            cal_bmu_soe->cumulative_wh -= fabs(delta_wh);
        }
        else
        {
            // 该BMU静置
            cal_bmu_soe->last_time = current_time;
            continue;
        }

        cal_bmu_soe->cumulative_wh = pdt_get_valid_data(cal_bmu_soe->cumulative_wh, s_bmu_real_energy[i], 0.0f);
        cal_bmu_soe->soe = (cal_bmu_soe->cumulative_wh / s_bmu_real_energy[i]) * 100.0f;   // SOE = Ecur / Emax
        cal_bmu_soe->soe = (cal_bmu_soe->soe > 100.0f) ? 100.0f : cal_bmu_soe->soe;
        cal_bmu_soe->soe = (cal_bmu_soe->soe < 0.0f) ? 0.0f : cal_bmu_soe->soe;

        cal_bmu_soe->last_time = current_time;
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:c059el576bpfaf2142ab0b89607bb480aa706046 */

/* Started by AICoder, pid:p9801a4845wd01c145ff0859f0a637338f955e41 */
STATIC int cal_and_adjust_bmu_soe(void)
{
    int ret = 0;
    int i = 0;
    int soe = 0;
    sid sid_temp = 0ll;

    ret = cal_batt_current();  // 计算电流
    ret |= cal_bmu_soe();      // 计算SOE
    if (ret != SUCCESS)
    {
        return FAILURE;
    }

    adjust_bmu_soe_efficiency();  // 校准soe转换系数
    adjust_bmu_soe_by_ocv();             // 校准SOE

    for (i = 0; i < BMU_NUM; i++)
    {
        soe = FLOAT_TO_INT32S(s_bmu_cal_soe[i].soe);
        sid_temp = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOE, 1, i + 1);
        pdt_set_data(sid_temp, &soe, sizeof(soe), type_int);
    }
    return SUCCESS;
}
/* Ended by AICoder, pid:p9801a4845wd01c145ff0859f0a637338f955e41 */

/*
 * @brief 低容量场景下通过OCV-SOC曲线校准SOE
*/
/* Started by AICoder, pid:m4f5ee5991he76114b9f0a2110aead463e115bc3 */
STATIC int adjust_bmu_soe_by_ocv(void)
{
    int i = 0;
    static int keep_count[BMU_NUM] = {0};
    float cell_ocv = 0.0f;
    float soc_by_ocv = 0.0;
    float soe_by_ocv = 0.0f;
    // 电池容量较低时(soc<8%)才具有较好的高阶拟合特征，此处取5%，与SOC一致
    const float SOE_ADJUST_THRESHOLD = 5.0f;  // SOE校准阈值

    for (i = 0; i < BMU_NUM; i++)
    {
        // 非低电量情况，不校正
        if (s_bmu_cal_soe[i].soe > SOE_ADJUST_THRESHOLD)
        {
            keep_count[i] = 0;
            continue;
        }

        // 检查BMU通信是否正常
        if (is_bmu_comm_normal(i + 1) == FAULT)
        {
            keep_count[i] = 0;
            continue;
        }

        // 静置状态下进行校准
        if (fabs(s_bmu_current[i]) > CURRENT_DETECTION_ERROR)
        {
            keep_count[i] = 0;
            continue;
        }
        else
        {
            keep_count[i]++;
        }

        // 等待BMU静置一段时间
        if (keep_count[i] < SOE_CAL_STANDY_KEEP_COUNT)
        {
            continue;
        }

        // 获取OCV值
        cell_ocv = s_bmu_sox_in[i].bmu_sox_data.cell_ocv.f_value;

        // 通过OCV估算SOE
        soc_by_ocv = pdt_get_valid_data(ocv_to_soc(cell_ocv), 5.0, 0.0);
        soe_by_ocv = soc_by_ocv * s_bmu_real_energy[i] / NOMINAL_ENERGY;

        // 标记需要保存
        if (fabs(s_bmu_cal_soe[i].soe - soe_by_ocv) >= 1)
        {
            s_bmu_sox_deal[i].save_to_file = TRUE;
        }

        // 更新累计能量
        s_bmu_cal_soe[i].cumulative_wh = soe_by_ocv * s_bmu_real_energy[i] / 100.0f;
        s_bmu_cal_soe[i].soe = soe_by_ocv;
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:m4f5ee5991he76114b9f0a2110aead463e115bc3 */

/**
 * @brief 校准SOE充放效率，终止处理同步
*/
/* Started by AICoder, pid:jdbe237d079ec6d144390b9b00bd1b306f78986e */
STATIC int adjust_bmu_soe_efficiency(void)
{
    int i = 0;
    float chg_eff_adjust = 0.0f;
    float dischg_eff_adjust = 0.0f;

    for(i = 0; i < BMU_NUM; i++)
    {
        if (s_bmu_real_energy[i] < FLOAT_EPSILON)
        {
            continue;
        }
        // 满充实时校准充电效率,跟随SOC==100%进行校准
        if (fabs(s_bmu_cal_soc[i].soc - 100.0f) < FLOAT_EPSILON)
        {
            s_bmu_cal_soe[i].soe = 100.0f;
            chg_eff_adjust = s_bmu_cal_soe[i].cumulative_wh / s_bmu_real_energy[i];
            chg_eff_adjust = pdt_get_valid_data(chg_eff_adjust, 1.0f, 0.0f);
            if (fabs(s_bmu_cal_soe[i].soe_chg_efficiency - chg_eff_adjust) >= FLOAT_EPSILON)
            {
                s_bmu_sox_deal[i].save_to_file = TRUE;
            }
            s_bmu_cal_soe[i].soe_chg_efficiency = chg_eff_adjust;
        }
        // 满放实时校准放电效率,跟随SOC==0%进行校准
        else if (fabs(s_bmu_cal_soc[i].soc - 0.0f) < FLOAT_EPSILON)
        {
            s_bmu_cal_soe[i].soe = 0.0f;
            if (s_bmu_real_energy[i] <= s_bmu_cal_soe[i].cumulative_wh)
            {
                continue;
            }

            dischg_eff_adjust = (s_bmu_real_energy[i] - s_bmu_cal_soe[i].cumulative_wh) / s_bmu_real_energy[i];
            dischg_eff_adjust = pdt_get_valid_data(dischg_eff_adjust, 1.0f, 0.0f);
            if (fabs(s_bmu_cal_soe[i].soe_dischg_efficiency - dischg_eff_adjust) > FLOAT_EPSILON)
            {
                s_bmu_sox_deal[i].save_to_file = TRUE;
            }
            s_bmu_cal_soe[i].soe_dischg_efficiency = dischg_eff_adjust;
        }
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:jdbe237d079ec6d144390b9b00bd1b306f78986e */

/* Started by AICoder, pid:d6d96926d0pb37914add08ed70b0b820f1565ae4 */
STATIC float calculate_soe_rack1(bmu_cal_soe_t *bmu_soe) 
{
    float soe_min = 100.0f;
    float soe_max = 0.0f;
    int i;

    for (i = 0; i < BMU_NUM; i++) {
        if (bmu_soe[i].soe < soe_min) {
            soe_min = bmu_soe[i].soe;
        }
        if (bmu_soe[i].soe > soe_max) {
            soe_max = bmu_soe[i].soe;
        }
    }

    if (soe_max < soe_min || if_float_equal(soe_min, 0)) {
        return 0.0f;
    }
    
    return soe_min * 100.0f / (100.0f + soe_min - soe_max);
}
/* Ended by AICoder, pid:d6d96926d0pb37914add08ed70b0b820f1565ae4 */


/* Started by AICoder, pid:wa1310bf8dv4cea14c550aa2b0af261573e5fc21 */
STATIC float calculate_soe_rack2(bmu_cal_soe_t *bmu_soe, float *bmu_energy) 
{
    float sum_soe_emax = 0.0f;
    float sum_emax = 0.0f;
    int i;
    
    for (i = 0; i < BMU_NUM; i++) {
        sum_soe_emax += bmu_soe[i].soe * bmu_energy[i];
        sum_emax += bmu_energy[i];
    }
    
    return (if_float_equal(sum_emax, 0)) ?  0.0f : (sum_soe_emax / sum_emax);
}
/* Ended by AICoder, pid:wa1310bf8dv4cea14c550aa2b0af261573e5fc21 */

/* Started by AICoder, pid:md2fe31cbe92411147a60b020082ba1232f79a6c */
STATIC float limit_charging_soe(float new_soe, float last_soe, int* is_limiting)
{
    float delta = new_soe - last_soe;

    if (delta < 0) {
        *is_limiting = TRUE;
        return last_soe;  // 充电时SOE下降，保持上一次值
    }

    if (delta > SOE_LIMIT_RATE) {
        *is_limiting = TRUE;
        return last_soe + SOE_LIMIT_RATE;  // 限制上升速率
    }

    *is_limiting = FALSE;
    return new_soe;  // 在限制范围内，不限幅
}
/* Ended by AICoder, pid:md2fe31cbe92411147a60b020082ba1232f79a6c */

// 处理放电状态SOE限幅
/* Started by AICoder, pid:oea28y7532ya1f214b1f0bd5b05a36142c777936 */
STATIC float limit_discharging_soe(float new_soe, float last_soe, int* is_limiting)
{
    float delta = new_soe - last_soe;

    if (delta > 0) {
        *is_limiting = TRUE;
        return last_soe;  // 放电时SOE上升，保持上一次值
    }

    if (delta < -SOE_LIMIT_RATE) {
        *is_limiting = TRUE;
        return last_soe - SOE_LIMIT_RATE;  // 限制下降速率
    }

    *is_limiting = FALSE;
    return new_soe;  // 在限制范围内，不限幅
}
/* Ended by AICoder, pid:oea28y7532ya1f214b1f0bd5b05a36142c777936 */



// SOE变化限幅处理
/* Started by AICoder, pid:45a9ekc06dh275e140af08db808edf2db0f5ff6b */
STATIC float limit_soe_change(float new_soe, float last_soe, int* is_limiting)
{
    float delta = new_soe - last_soe;
    float limited_soe = new_soe;
    int bcmu_battery_status = BCMU_BAT_STATUS_CHARGE;
    int ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(bcmu_battery_status));

    RETURN_VAL_IF_FAIL(ret == SUCCESS, new_soe);

    switch (bcmu_battery_status) {
        case BCMU_BAT_STATUS_CHARGE:
            limited_soe = limit_charging_soe(new_soe, last_soe, is_limiting);
            break;
        case BCMU_BAT_STATUS_DISCHARGE:
            limited_soe = limit_discharging_soe(new_soe, last_soe, is_limiting);
            break;
        default:
            break;
    }

    return limited_soe;
}
/* Ended by AICoder, pid:45a9ekc06dh275e140af08db808edf2db0f5ff6b */


/* Started by AICoder, pid:x104fl2a6ca547414bc30bbfa0a54e39a0208859 */
STATIC int if_opt_open(void) {
    int i = 0;
    sid sid_temp = 0;
    int ret = SUCCESS;
    int opt_run_state = 0;
    int opt_exit_state = 0;
    int opt_comm_state = 0;

    for (i = 0; i < OPT_NUM; ++i)
    {
        sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_EXIST_STATE);
        ret = pdt_get_data(sid_temp, &opt_exit_state, sizeof(opt_exit_state));
        //不在位直接跳过
        if (ret != SUCCESS || opt_exit_state == 0) {
            continue;
        }

        sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_COMMUNICATION_STATUS);
        ret = pdt_get_data(sid_temp, &opt_comm_state, sizeof(opt_comm_state));

        //通讯失败直接跳过
        if (ret != SUCCESS || opt_comm_state == 1) {
            continue;
        }
        
        sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_RUN_STATUS);
        ret = pdt_get_data(sid_temp, &opt_run_state, sizeof(opt_run_state));
        //开启优化器
        if (ret == SUCCESS && opt_run_state == 1) {
            return TRUE;
        }    
    }
    return FALSE;
}
/* Ended by AICoder, pid:x104fl2a6ca547414bc30bbfa0a54e39a0208859 */

// 计算并更新簇SOE
/* Started by AICoder, pid:p84deh9ae8u80e614c3c0b4920466a223ee911b8 */
STATIC float calculate_cluster_soe(bmu_cal_soe_t *bmu_soe, float *bmu_energy, float* last_cluster_soe, int* last_opt_status, int* is_limiting) 
{
    float soe_rack1 = 0.0; 
    float soe_rack2 = 0.0; 
    float cluster_soe = 0.0;
    int opt_status = FALSE;
    int set_cluster_soe = 0;
    
    // 计算SOErack1
    soe_rack1 = calculate_soe_rack1(bmu_soe);
    opt_status = if_opt_open();
    if (opt_status) {
        // 优化器开启时，计算SOErack2并取最小值
        soe_rack2 = calculate_soe_rack2(bmu_soe, bmu_energy);
        cluster_soe = (soe_rack1 < soe_rack2) ? soe_rack1 : soe_rack2;
    } else {
        // 优化器未开启时，直接使用SOErack1
        cluster_soe = soe_rack1;
    }
    
    // 在以下两种情况下进行限幅处理：
    // 1. 优化器状态发生变化
    // 2. 正在进行限幅过程中（s_is_limiting为TRUE）
    if (opt_status != *last_opt_status || *is_limiting) {
        cluster_soe = limit_soe_change(cluster_soe, *last_cluster_soe, is_limiting);
    }
    
    // 更新状态
    *last_cluster_soe = cluster_soe;
    *last_opt_status = opt_status;

    set_cluster_soe = (int)round(cluster_soe);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOE, &set_cluster_soe, sizeof(int), type_int);
    
    return cluster_soe;
}
/* Ended by AICoder, pid:p84deh9ae8u80e614c3c0b4920466a223ee911b8 */

/* Started by AICoder, pid:3deb8q4700205be1433c0b9650e58f8660f447f8 */
STATIC int calculate_cluster_soh(void)
{
    int i = 0;
    int bmu_num = 0;
    sid status_temp_sid = 0;
    sid soh_temp_sid = 0;
    int bmu_soh[BMU_NUM] = {100, 100, 100, 100};
    int bmu_soh_sum = 0;
    int bcmu_soh = 100;
    int bcmu_soh_old = 100;
    int opt_status = FALSE;
    int bmu_status = 0;

    for (i = 0; i < BMU_NUM; i++)
    {
        status_temp_sid = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_DIG_BMU_COMMUNICATION_STATUS, i + 1, 1);
        // 获取bmu通讯状态
        pdt_get_data(status_temp_sid, &bmu_status, sizeof(int));
        if (0 == bmu_status)
        {
            bmu_num += 1;
            soh_temp_sid = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOH, 1, i + 1);
            // 获取bmu SOH
            pdt_get_data(soh_temp_sid, &bmu_soh[i], sizeof(int));
            bmu_soh_sum += bmu_soh[i];
        }
    }

    if (0 == bmu_num)
    {
        return FAILURE;
    }

    // 配置了优化器，则取平均，否则取最小值
    opt_status = if_opt_open();
    if (opt_status)
    {
        bcmu_soh = bmu_soh_sum / bmu_num;
    }
    else
    {
        bcmu_soh = get_min_soh(bmu_soh);
    }

    // SOH的范围为30-100, 且单向递减
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH, &bcmu_soh_old, sizeof(int));
    bcmu_soh = update_soh(bcmu_soh_old, bcmu_soh);
    if (bcmu_soh != bcmu_soh_old)
    {
        pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH, &bcmu_soh, sizeof(int), type_int);
        s_bcmu_save_bak.bcmu_soh = bcmu_soh;
        write_bmu_soh_info_to_file(&s_bcmu_save_bak);
    }

    return SUCCESS;
}

// 限制并更新cluster_soh_display值（30-100且单向递减）
STATIC int update_soh(int bcmu_soh, int new_value)
{
    // 边界限制（0-100）
    if (new_value < BCMU_SOH_MIN) new_value = BCMU_SOH_MIN;
    if (new_value > BCMU_SOH_MAX) new_value = BCMU_SOH_MAX;

    // 单向递减检查
    if (new_value < bcmu_soh)
    {
        bcmu_soh = new_value;
    }
    return bcmu_soh;
}

STATIC int get_min_soh(int bmu_soh[])
{
    int min = bmu_soh[0];
    int i = 0;
    for (i = 1; i < BMU_NUM; i++)
    {
        if (bmu_soh[i] < min)
        {
            min = bmu_soh[i];
        }
    }
    return min;
}
/* Ended by AICoder, pid:3deb8q4700205be1433c0b9650e58f8660f447f8 */

/// @brief 更新SOH数据并下发到BMU
/// @return 成功返回SUCCESS
int update_and_send_soh_data(void)
{
    int i;
    // 遍历所有BMU，下发SOH数据
    for (i = 0; i < BMU_NUM; i++)
    {
        send_soh_data_to_bmu(i + 1);
    }
    return SUCCESS;
}

/**
 * @brief 从文件加载序列号数据
 * @param dev_addr 设备地址
 * @param serials 序列号数据结构
 * @return int SUCCESS-成功, FAILURE-失败
 */
STATIC int load_bmu_serial_numbers(int dev_addr, serial_numbers_t *serials)
{
    char file_path[128] = {0};
    unsigned char buffer[32 * 5 + 2] = {0}; // 序列号数据 (5 * 32) + CRC16 (2) = 162 字节
    int read_result = 0;
    unsigned short stored_crc = 0;
    unsigned short calculated_crc = 0;

    if (serials == NULL)
    {
        return FAILURE;
    }

    snprintf_s_void(file_path, sizeof(file_path), "%sbmu_%d_serial.bin", PATH_DATA_WORK_SPARE_SERIAL, dev_addr);

    if(is_file_exist(file_path) == FALSE)
    {
        return FAILURE;
    }

    read_result = read_file(file_path, buffer, sizeof(buffer));

    // 检查 read_file 的返回值。如果返回 1，表示成功读取了 sizeof(buffer) 字节
    if (read_result != 1)
    {
        return FAILURE;
    }

    calculated_crc = calculate_crc16(buffer, 32 * 5);
    stored_crc = get_int16_data(buffer + 32 * 5);

    if (calculated_crc != stored_crc)
    {
        return FAILURE;
    }

    plat_memcpy_s(serials->bcmu_serial, 32, buffer, 32);

    for (int i = 0; i < BMU_NUM; i++)
    {
        plat_memcpy_s(serials->bmu_serials[i], 32, buffer + 32 + i * 32, 32);
    }

    return SUCCESS;
}

/**
 * @brief 获取BCMU当前存储的序列号信息
 * @details 从数据字典获取BCMU自身的序列号和其记录的四个BMU序列号
 * @param bcmu_serials 存储BCMU序列号的结构体指针
 * @return int SUCCESS-成功, FAILURE-失败
 */
STATIC int get_bcmu_serial_numbers(serial_numbers_t *bcmu_serials)
{
    sid sig_id;
    int i;

    if (bcmu_serials == NULL)
    {
        plat_printf_s("func name:%s, line num: %d : 参数无效, bcmu_serials 为 NULL\n", __func__, __LINE__);
        return FAILURE;
    }

    plat_memset_s(bcmu_serials, sizeof(serial_numbers_t), 0, sizeof(serial_numbers_t));

    sig_id = SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DEVINFO_BCMU_SERIAL_NUMBER;
    if (pdt_get_data(sig_id, bcmu_serials->bcmu_serial, sizeof(bcmu_serials->bcmu_serial)) != SUCCESS)
    {
        return FAILURE;
    }

    // 确保bcmu_serial在第一个空字符后全部清零
    for (int i = 0; i < sizeof(bcmu_serials->bcmu_serial); i++)
    {
        if (bcmu_serials->bcmu_serial[i] == '\0')
        {
            for (int j = i + 1; j < sizeof(bcmu_serials->bcmu_serial); j++)
            {
                bcmu_serials->bcmu_serial[j] = '\0';
            }
            break; // 找到第一个空字符后就停止查找
        }
    }

    for (i = 0; i < BMU_NUM; i++)
    {
        sig_id = SID_SET_DEV_SN(i + 1, SID_BATTERY_MODULE_DEVINFO_BMU_CELL_SERIAL_NUMBER);
        if (pdt_get_data(sig_id, bcmu_serials->bmu_serials[i], sizeof(bcmu_serials->bmu_serials[i])) != SUCCESS)
        {
            return FAILURE;
        }
    }

    return SUCCESS;
}

/**
 * @brief 在活动BMU中查找多数派序列号记录
 * @param bmu_saved_serials 所有BMU保存的序列号数据
 * @param active_bmu_indices 活动BMU在bmu_saved_serials中的原始索引数组
 * @param num_active_bmus 活动BMU数量
 * @param majority_record_out 输出：多数派序列号记录
 * @param majority_count_out 输出：多数派记录出现次数
 * @return int SUCCESS-成功, FAILURE-失败 (未能找到多数派或处理异常)
 */
STATIC int find_majority_bmu_record(serial_numbers_t bmu_saved_serials[], const int active_bmu_indices[], int num_active_bmus, serial_numbers_t *majority_record_out, int *majority_count_out)
{
    int i, j;
    // 用于统计每个活动BMU记录的出现次数
    int record_counts[BMU_NUM] = {0};
    // 存储不同序列号记录的活动BMU索引
    int unique_record_indices[BMU_NUM];
    int num_unique_records = 0;

    plat_memset_s(unique_record_indices, sizeof(unique_record_indices), 0, sizeof(unique_record_indices));

    // 收集所有活动BMU的记录，并统计每种记录的出现次数
    for (i = 0; i < num_active_bmus; i++)
    {
        int current_bmu_idx = active_bmu_indices[i];
        boolean found_match = FALSE;
        for (j = 0; j < num_unique_records; j++)
        {
            if (memcmp(&bmu_saved_serials[current_bmu_idx], &bmu_saved_serials[unique_record_indices[j]], sizeof(serial_numbers_t)) == 0)
            {
                record_counts[j]++;
                found_match = TRUE;
                break;
            }
        }
        if (!found_match && num_unique_records < BMU_NUM)
        {
            unique_record_indices[num_unique_records] = current_bmu_idx;
            record_counts[num_unique_records] = 1;
            num_unique_records++;
        }
    }

    // 找到出现次数最多的记录作为多数派
    if (num_unique_records > 0)
    {
        *majority_count_out = 0;
        int majority_idx = -1;
        for (i = 0; i < num_unique_records; i++)
        {
            if (record_counts[i] > *majority_count_out)
            {
                *majority_count_out = record_counts[i];
                majority_idx = unique_record_indices[i];
            }
        }
        if (majority_idx != -1)
        {
            plat_memcpy_s(majority_record_out, sizeof(serial_numbers_t), &bmu_saved_serials[majority_idx], sizeof(serial_numbers_t));
            return SUCCESS;
        }
    }

    // 未能找到多数派记录 (例如，所有记录都不相同且数量相等)
    return FAILURE;
}


/**
 * @brief 根据多数派记录和BCMU当前记录判断序列号更换场景
 * @param bmu_saved_serials 所有BMU保存的序列号数据
 * @param active_bmu_indices 活动BMU在bmu_saved_serials中的原始索引数组
 * @param num_active_bmus 活动BMU数量
 * @param bcmu_saved_serials 当前BCMU中存储的序列号
 * @param majority_bmu_record 多数派BMU序列号记录
 * @param majority_bmu_count 多数派记录出现次数
 * @param result 输出：比对结果
 * @return int SUCCESS-成功, FAILURE-失败
 */
STATIC int judge_serial_comparison_scenario(serial_numbers_t bmu_saved_serials[], const int active_bmu_indices[], int num_active_bmus, serial_numbers_t *bcmu_saved_serials, serial_numbers_t *majority_bmu_record, int majority_bmu_count, serial_compare_result_t *result)
{
    int i, j;

    // 初始化结果
    result->bcmu_changed = 0;
    result->bmu_changed_mask = 0;
    result->bmu_changed_count = 0;

    // 优先判断是否存在多数派（至少3个活动BMU记录一致）
    if (majority_bmu_count >= 3)
    {
        // 存在多数派，根据BCMU自身序列号判断主要更换场景
        // 比较BCMU当前序列号与多数派BMU记录中的BCMU序列号
        if (memcmp(bcmu_saved_serials->bcmu_serial, majority_bmu_record->bcmu_serial, sizeof(bcmu_saved_serials->bcmu_serial)) != 0)
        {
            // 场景2：BCMU更换了 (BCMU当前序列号与多数派BMU记录中的BCMU序列号不一致)
            result->bcmu_changed = 1;
        }
        else
        {
            result->bcmu_changed = 0;
            // 检查是否存在少数派BMU (多数派数量小于活动BMU总数)
            if (majority_bmu_count < num_active_bmus)
            {
                // 场景3：BCMU未更换，但部分BMU更换 (BMU多数派数量小于活动BMU总数)
                // 找出哪些BMU的完整记录与多数派记录不一致
                result->bmu_changed_mask = 0;
                result->bmu_changed_count = 0;
                for (j = 0; j < num_active_bmus; j++) {
                    int current_bmu_idx = active_bmu_indices[j];
                    if (memcmp(&bmu_saved_serials[current_bmu_idx], majority_bmu_record, sizeof(serial_numbers_t)) != 0) {
                         // 在掩码中标记这个BMU (使用其在bmu_saved_serials中的原始索引)
                        result->bmu_changed_mask |= (1 << current_bmu_idx);
                        result->bmu_changed_count++;
                        break;
                    }
                }
            }
            else
            {
                // 场景1：BCMU和所有活动BMU的记录都一致，均未更换
                result->bmu_changed_mask = 0;
                result->bmu_changed_count = 0;
            }
        }
    }
    else
    {
        // 没有多数派BMU记录（活动BMU少于3个或记录都不一致）
        result->bcmu_changed = 0;
        result->bmu_changed_mask = 0; // 异常情况下无法确定具体BMU更换
        result->bmu_changed_count = 0;
    }

    return SUCCESS;
}

/**
 * @brief 比较BMU和BCMU的序列号信息，判断是否有设备更换
 * @param bmu_saved_serials 所有BMU保存的序列号数据
 * @param bmu_count BMU总数
 * @param bcmu_saved_serials 当前BCMU中存储的序列号
 * @param result 输出：比对结果
 * @return int SUCCESS-成功, FAILURE-失败
 */
STATIC int compare_serial_numbers(serial_numbers_t bmu_saved_serials[], int bmu_count, serial_numbers_t *bcmu_saved_serials, serial_compare_result_t *result)
{
    int i;
    // 活动BMU的索引数组
    int active_bmu_indices[BMU_NUM] = {0, 1, 2, 3};
    // 记录出现次数最多的序列号记录
    serial_numbers_t majority_bmu_record = {0};
    int majority_bmu_count = 0;

    if (bmu_saved_serials == NULL || bcmu_saved_serials == NULL || result == NULL)
    {
        return FAILURE;
    }

    // 初始化结果
    plat_memset_s(result, sizeof(serial_compare_result_t), 0, sizeof(serial_compare_result_t));

    // 查找多数派BMU记录
    if (find_majority_bmu_record(bmu_saved_serials, active_bmu_indices, BMU_NUM, &majority_bmu_record, &majority_bmu_count) != SUCCESS)
    {
        result->bcmu_changed = 0;
        return FAILURE;
    }

    // 根据多数派记录和BCMU当前记录判断序列号更换场景
    return judge_serial_comparison_scenario(bmu_saved_serials, active_bmu_indices, BMU_NUM, bcmu_saved_serials, &majority_bmu_record, majority_bmu_count, result);
}

/**
 * @brief 处理序列号比较结果
 * @param result 序列号比较结果
 * @return int SUCCESS-成功, FAILURE-失败
 */
STATIC int handle_serial_comparison_result(serial_compare_result_t *result)
{
    int i;

    // 场景1: BCMU和BMU都没有变更，或者场景4无法识别变更
    if (result->bcmu_changed == 0 && result->bmu_changed_count == 0)
    {
        return FAILURE;
    }
    // 场景2: BCMU更换了+ 至多一个BMU更换
    else if (result->bcmu_changed == 1)
    {
        // BCMU更换，需要将所有BMU保存的SOH数据同步回新的BCMU
        for (i = 0; i < BMU_NUM; i++)
        {
            get_soh_data_from_bmu(i + 1);
            // 记录待同步SOH的BMU索引
            record_bmu_index_pending_backup(i);
        }
        // 备件替换中
        s_replace_finish_flag = enum_backup_doing;

        // 将BCMU中当前序列号和通讯读取到的每个BMU序列号同步到所有BMU中
        boardcast_serial_num_to_bmu();
    }
    // 场景3: BCMU未更换, BMUx 更换
    else if (result->bcmu_changed == 0 && result->bmu_changed_count > 0)
    {
        // 处理所有被更换的BMU
        for (i = 0; i < BMU_NUM; i++)
        {
            if (result->bmu_changed_mask & (1 << i))
            {
                // 只同步被更换BMU的数据到BCMU
                get_soh_data_from_bmu(i + 1);
                // 记录待同步SOH的BMU索引
                record_bmu_index_pending_backup(i);
            }
        }
        // 备件替换中
        s_replace_finish_flag = enum_backup_doing;
        // 广播最新序列号到所有BMU
        boardcast_serial_num_to_bmu();
    }
    return SUCCESS;
}

/**
 * @brief 周期性检查序列号变化
 * @return int SUCCESS-成功, FAILURE-失败
 */
int check_serial_number_changes(void)
{
    serial_numbers_t bmu_saved_serials[BMU_NUM];
    serial_numbers_t bcmu_saved_serials;
    serial_compare_result_t result = {0};
    int invalid_bmu_file_cnt = 0;
    int ret = FAILURE;
    int i;

    // 如果正处于备件替换中，跳过此次序列号校验。
    if(s_replace_finish_flag == enum_backup_doing)
    {
        return SUCCESS;
    }

    // 检查所有BMU是否通讯正常且在位
    for (i = 0; i < BMU_NUM; i++)
    {
        if (is_bmu_exist(i + 1) == FAULT || is_bmu_comm_normal(i + 1) == FAULT)
        {
            // 如果任何一个BMU不在位或通讯异常，则直接返回 FAILURE
            return FAILURE;
        }
    }

    // 初始化bmu_saved_serials
    plat_memset_s(bmu_saved_serials, sizeof(bmu_saved_serials), 0, sizeof(bmu_saved_serials));
    plat_memset_s(&bcmu_saved_serials, sizeof(serial_numbers_t), 0, sizeof(serial_numbers_t));

    // ### 识别系统首次运行且BMU文件为默认空值场景 ###
    // 尝试从每个BMU的存储加载序列号，并检查是否都为默认空值
    for (i = 0; i < BMU_NUM; i++)
    {
        // 尝试加载序列号文件
        ret = load_bmu_serial_numbers(i + 1, &bmu_saved_serials[i]);

        if (ret == FAILURE)
        {
            return FAILURE;
        }
        if(bmu_saved_serials[i].bcmu_serial[0] == '\0')
        {
            invalid_bmu_file_cnt++;
        }
    }

    // 如果所有BMU的序列号文件都存在且内容为默认空值，则广播序列号并返回
    if (invalid_bmu_file_cnt == BMU_NUM)
    {
        boardcast_serial_num_to_bmu(); // 广播BCMU序列号到所有BMU
        return SUCCESS; // 处理完成，返回成功
    }

    // 获取BCMU自身当前的序列号以及它记录的BMU序列号，直接读入bcmu_saved_serials结构体
    ret = get_bcmu_serial_numbers(&bcmu_saved_serials);
    if (ret != SUCCESS)
    {
        return FAILURE;
    }

    // 比较序列号
    ret = compare_serial_numbers(bmu_saved_serials, BMU_NUM, &bcmu_saved_serials, &result);
    if (ret != SUCCESS)
    {
        return FAILURE;
    }
    ret = handle_serial_comparison_result(&result);
    return ret;
}

/**
 * @brief 设置SOH同步完成标志
 * @param flag 标志值
 * @return int SUCCESS-成功, FAILURE-失败
 */
int set_soh_sync_finish_flag(int flag)
{
    s_soh_sync_finish_flag = flag;
    return SUCCESS;
}

/**
 * @brief 获取备件替换状态
 * @return int 备件替换状态
 */
int get_backup_status(void)
{
    return s_replace_finish_flag;
}

/**
 * @brief 记录待同步SOH的BMU索引
 * @param bmu_index 待同步SOH的BMU索引
 * @return int SUCCESS-成功, FAILURE-失败
 */
STATIC int record_bmu_index_pending_backup(int bmu_index)
{
    s_bmu_index_pending_backup[bmu_index] = bmu_index;
    return SUCCESS;
}

/**
 * @brief 获取待同步SOH的BMU索引
 * @param bmu_index 输出：待同步SOH的BMU索引
 * @return int SUCCESS-成功, FAILURE-失败
 */
int get_bmu_index_pending_backup(int *bmu_index)
{
    int i;
    for(i = 0; i < BMU_NUM; i++)
    {
        bmu_index[i] = s_bmu_index_pending_backup[i];
    }
    return SUCCESS;
}

/**
 * @brief 检查充电结束状态并保存最后一次充电信息
 * @return SUCCESS-成功，FAILURE-失败
 */
STATIC int check_and_save_charge_end_info(void)
{
    static int s_last_charging_state = FALSE;
    int current_charging_state = FALSE;
    int cluster_soc = 0;
    unsigned long sys_run_cnt_after_chg_end = 0;
    int bcmu_battery_status = BCMU_BAT_STATUS_STANDY;
    last_charge_info_t info;

    // 获取当前电池状态
    if (pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, 
                    &bcmu_battery_status, sizeof(bcmu_battery_status)) != SUCCESS) {
        return FAILURE;
    }

    // 判断当前是否处于充电状态
    current_charging_state = (bcmu_battery_status == BCMU_BAT_STATUS_CHARGE);

    // 之前处于充电状态，现在为非充电状态，那么需要更新最后一次充电信息。
    if (s_last_charging_state == TRUE && current_charging_state == FALSE)
    {
        if (pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, 
                       &cluster_soc, sizeof(cluster_soc)) == SUCCESS)
        {
            info.last_charge_info_used = FALSE;
            info.sys_run_cnt_after_last_charge_end = 0;
            info.last_charge_end_soc = cluster_soc;
            save_charge_end_info(&info);
        }
    }

    // 更新充电状态
    s_last_charging_state = current_charging_state;

    return SUCCESS;
}

/**
 * @brief 保存充电结束时间和SOC
 * @param end_time 充电结束时间
 * @param end_soc 充电结束时的SOC
 * @return SUCCESS-成功，FAILURE-失败
 */
STATIC int save_charge_end_info(last_charge_info_t *last_charge_info)
{
    int ret = FAILURE;
    int fd = -1;
    if(last_charge_info == NULL)
    {
        return FAILURE;
    }
    last_charge_info->crc = calculate_crc32((char*)last_charge_info, sizeof(last_charge_info_t) - sizeof(int));

    if (lock_file_write(LAST_CHARGE_INFO_FILE_NAME, &fd) != SUCCESS)
    {
        SYSLOG_ERR("获取充电信息文件写锁失败");
        return FAILURE;
    }

    ret = write_file(LAST_CHARGE_INFO_FILE_NAME, last_charge_info, sizeof(last_charge_info_t));
    unlock_file(fd);
    return ret;
}

/// @brief 判断系统是否处于静止状态
/// @details 当簇总电流绝对值 ≤ 2.15A 且持续时间 ≥ 2 小时(7200s)时，返回 TRUE
STATIC int judge_idle_status(void)
{
    time_t current_time = 0;
    float total_cluster_current = 0.0f;
    int idle_time_threshold = 0;
    int ret;

    // 获取当前系统时间
    if (get_system_time(&current_time) != SUCCESS) {
        return FALSE;
    }

    // 读取簇总电流信号
    ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT, &total_cluster_current, sizeof(total_cluster_current));
    if (ret != SUCCESS) {
        return FALSE;
    }

    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_IDLE_TIME_THRESHOLD, &idle_time_threshold, sizeof(idle_time_threshold));

    // 判断是否低于静止电流阈值
    if (fabs(total_cluster_current) <= IDLE_CURRENT_THRESHOLD) {
        // 首次满足静止条件时记录起始时间
        if (s_idle_start_time == 0) {
            s_idle_start_time = current_time;
        }
        // 如果持续时间超过阈值，则认为系统处于静止
        if ((current_time - s_idle_start_time) > idle_time_threshold*3600) {
            return TRUE;
        } else {
            return FALSE;
        }
    } else {
        // 簇电流超出静止阈值，重置静止起始时间
        s_idle_start_time = 0;
        return FALSE;
    }
}

/// @brief 统计每个 BMU 单体电压的最大/最小值及对应温度，并计算簇级全局最大/最小单体电压
STATIC int calculate_cell_voltage_extremes(void)
{
    const char s_vol_temp_map[65] = {0,  1,  1,  2,  2,  3,  3,  4,  4,  5,  5,  6,  6,
                                     7,  7,  8,  8,  9,  9,  10, 10, 11, 11, 12, 12, 13,
                                     14, 15, 15, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20,
                                     21, 21, 22, 22, 23, 23, 24, 24, 25, 25, 26, 26, 27,
                                     28, 29, 29, 30, 30, 31, 31, 32, 32, 33, 33, 34, 34}; // 将65个电芯电压和35个电芯温度对应
    int i, j;

    for (i = 0; i < BMU_NUM; i++) {
        float max_v = -FLT_MAX, min_v = FLT_MAX;
        int idx_max = 0, idx_min = 0;

        // 扫描该 BMU 所有单体电压
        for (j = 0; j < BMU_CELL_NUM; j++) {
            float v = s_bmu_sox_in[i].bmu_sox_data.cell_volt[j].f_value;
            if (v > max_v) { max_v = v; idx_max = j; }
            if (v < min_v) { min_v = v; idx_min = j; }
        }

        // 对应的温度
        float temp_at_max = s_bmu_sox_in[i].bmu_sox_data.cell_temperature[s_vol_temp_map[idx_max]].f_value;
        float temp_at_min = s_bmu_sox_in[i].bmu_sox_data.cell_temperature[s_vol_temp_map[idx_min]].f_value;

        bmu_cell_voltages_max[i] = max_v;
        bmu_cell_temps_at_max[i] = temp_at_max;
        bmu_cell_voltages_min[i] = min_v;
        bmu_cell_temps_at_min[i] = temp_at_min;

        if (max_v > global_max_v)
        {
            global_max_v = max_v;
            global_temp_at_max = temp_at_max;
        }

        if (min_v < global_min_v) 
        {
            global_min_v = min_v;
            global_temp_at_min = temp_at_min;
        }
    }
    return SUCCESS;

}


// 在数组里查找索引 k，使得 table[k] <= x <= table[k+1]
// 返回 k，若 x <= table[0] 返回 0，x >= table[n-1] 返回 n-2
static int find_interval(const float *table, int n, float x) {
    if (x <= table[0]) return 0;
    if (x >= table[n-1]) return n-2;
    for (int k = 0; k < n-1; k++) {
        if (x >= table[k] && x <= table[k+1]) {
            return k;
        }
    }
    return 0; // should not happen
}

/// @brief 双线性插值：给定网格四点 (x0,y0)->q00, (x1,y0)->q10, (x0,y1)->q01, (x1,y1)->q11
static float bilinear(float x, float y,
                      float x0, float x1, 
                      float y0, float y1,
                      float q00, float q10, float q01, float q11)
{
    float dx = (if_float_equal(x1, x0) ? 0.0f : (x - x0)/(x1 - x0));
    float dy = (if_float_equal(y1, y0) ? 0.0f : (y - y0)/(y1 - y0));
    float r0 = q00 + dx*(q10 - q00);
    float r1 = q01 + dx*(q11 - q01);
    return r0 + dy*(r1 - r0);
}

/// @brief 根据 温度 temp（℃）和 SOC（%）查表并双线性插值得到电压（V）
float lookup_voltage(float temp, float soc)
{
    // 限幅
    if (soc < soc_table[0]) soc = soc_table[0];
    if (soc > soc_table[NUM_SOC-1]) soc = soc_table[NUM_SOC-1];
    if (temp < temp_table[0]) temp = temp_table[0];
    if (temp > temp_table[NUM_TEMPS-1]) temp = temp_table[NUM_TEMPS-1];

    int i = find_interval(temp_table, NUM_TEMPS, temp);
    int j = find_interval(soc_table, NUM_SOC, soc);

    float t0 = temp_table[i],   t1 = temp_table[i+1];
    float s0 = soc_table[j],     s1 = soc_table[j+1];
    float q00 = voltage_table[i][j];
    float q10 = voltage_table[i+1][j];
    float q01 = voltage_table[i][j+1];
    float q11 = voltage_table[i+1][j+1];

    return bilinear(temp, soc, t0, t1, s0, s1, q00, q10, q01, q11);
}

static float limit_temp(float temp)
{
    if (temp < temp_table[0]) temp = temp_table[0];
    if (temp > temp_table[NUM_TEMPS-1]) temp = temp_table[NUM_TEMPS-1];
    return temp;
}

/// @brief 根据 温度 temp（℃）和 电压 voltage（V）反向插值得到 SOC（%）
///   先对每个相邻温度行做水平插值，再对两行做垂直插值
float lookup_soc(float temp, float voltage)
{
    // 限幅
    temp = limit_temp(temp);

    // 找到 temp 所在的上下行
    int i = find_interval(temp_table, NUM_TEMPS, temp);
    float t0 = temp_table[i], t1 = temp_table[i+1];
    float w = (if_float_equal(t1, t0) ? 0.0f : (temp - t0)/(t1 - t0));

    // 在行 i 上，找到电压所在的 SOC 段
    // 先在 row i 水平查找
    float *row0 = (float*)voltage_table[i];
    int j0 = 0;
    float voltage_temp = voltage;
    // clamp 在 row0 的范围内
    if (voltage <= row0[0]) {
        j0 = 0;
        voltage_temp = row0[0];
    } else if (voltage >= row0[NUM_SOC-1]) {
        j0 = NUM_SOC-2;
        voltage_temp = row0[NUM_SOC-1];
    } else {
        for (int k = 0; k < NUM_SOC-1; k++) {
            if (voltage >= row0[k] && voltage <= row0[k+1]) {
                j0 = k;
                break;
            }
        }
    }
    float s00 = soc_table[j0], s01 = soc_table[j0+1];
    float v00 = row0[j0],     v01 = row0[j0+1];
    // 水平插值得到行 i 上的 SOC
    float soc0 = (if_float_equal(v01, v00) ? s00 : s00 + (voltage_temp - v00)/(v01 - v00)*(s01 - s00));

    // 同理在行 i+1 上插值
    float *row1 = (float*)voltage_table[i+1];
    int j1 = j0;
    if (voltage <= row1[0]) {
        j1 = 0;
        voltage_temp = row1[0];
    } else if (voltage >= row1[NUM_SOC-1]) {
        j1 = NUM_SOC-2;
        voltage_temp = row1[NUM_SOC-1];
    } else {
        for (int k = 0; k < NUM_SOC-1; k++) {
            if (voltage >= row1[k] && voltage <= row1[k+1]) {
                j1 = k;
                break;
            }
        }
    }
    float t10 = soc_table[j1], t11 = soc_table[j1+1];
    float v10 = row1[j1],     v11 = row1[j1+1];
    float soc1 = (if_float_equal(v11, v10) ? t10 : t10 + (voltage_temp - v10)/(v11 - v10)*(t11 - t10));

    // 垂直插值
    return soc0 + w*(soc1 - soc0);
}

STATIC int is_cell_voltage_out_of_platform_region(void)
{
    float v_min = global_min_v, t_min = global_temp_at_min;
    float v_max = global_max_v, t_max = global_temp_at_max;
    float low_threshold, high_threshold;
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_LOW_THRESHOLD, &low_threshold, sizeof(low_threshold));
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_HIGH_THRESHOLD, &high_threshold, sizeof(high_threshold));
    float v_low_h  = lookup_voltage(t_min, low_threshold);  // 平台低区上边界
    float v_low_l  = lookup_voltage(t_min, 0.0f);   // 平台低区下边界
    float v_high_l = lookup_voltage(t_max, high_threshold);  // 平台高区下边界
    float v_high_h = lookup_voltage(t_max, 100.0f); // 平台高区上边界
    // 最小电压和最大电压都在低区或高区，即视为"非平台区"
    return ((v_min>v_low_l&&v_max<v_low_h) || (v_min>v_high_l&&v_max < v_high_h)) ? TRUE : FALSE;
}

/*
1. 判断是否处于静置状态
2. 获取最大最小单体电压
3. 获取非平台电压区阈值（10% / 95%对应电压）
4. 判断最大/最小单体电压是否超出非平台区
5. 判断是否有优化器，分别做簇级或单模块校准
*/
STATIC int soc_static_calibration(soc_adjust_record_t *adjust_ret)
{
    int ret = judge_idle_status();
    int set_bmu_soc = 0;

    plat_memset_s(adjust_ret, sizeof(soc_adjust_record_t), 0, sizeof(soc_adjust_record_t));
    if (ret != TRUE) {
        return FAILURE;
    }

    // 统计各 BMU 的单体最大/最小电压及对应温度，并更新全局极值
    calculate_cell_voltage_extremes();
    ret = is_cell_voltage_out_of_platform_region();
    if (ret != TRUE) {
        return FAILURE;
    }

    // 校正阈值（%）
    int soc_threshold = 0;
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_MINIMUM_ALLOWABLE_CORRECTION_DELTA_THRESHOLD, &soc_threshold, sizeof(soc_threshold));

    // 是否配置优化器
    int has_opt = if_config_opt();

    if (FALSE == has_opt) {
        // —— 簇级校准 —— 
        float v_min = global_min_v, t_min = global_temp_at_min;
        float v_max = global_max_v, t_max = global_temp_at_max;

        // 查表求出 min/max 对应的 SOC
        float soc_min = lookup_soc(t_min, v_min);
        float soc_max = lookup_soc(t_max, v_max);
        // 计算校准值
        float soc0 = soc_min * 100.0f / (100.0f + soc_min - soc_max);

        int need_calibrate = 1;
        for (int i = 0; i < BMU_NUM; i++) {
        if (fabsf(s_bmu_cal_soc[i].soc - soc0) <= soc_threshold) {
            need_calibrate = 0;
            break;
        }
    }
        if (need_calibrate) {
            // 更新每个 BMU 的 SOC
            for (int i = 0; i < BMU_NUM; i++) {
                sid sid_mod = PDT_SID_SET_DEV_SN_SIG_VINDEX(
                    SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOC, 1, i + 1);
                set_bmu_soc = (int)round(soc0);
                pdt_set_data(sid_mod, &set_bmu_soc, sizeof(set_bmu_soc), type_int);
                s_bmu_cal_soc[i].soc = soc0;
                s_bmu_cal_soc[i].cumulative_ah = soc0 / 100.0f * s_bmu_real_capacity[i];
                s_bmu_cal_soc[i].accumulated_ah_since_correction = 0.0f;
                adjust_ret->soc_adjust_record_index[i] = TRUE;
            }
            adjust_ret->soc_adjust_type = enum_soc_adjust_non_platform_static;
        }
    } else {
        // —— 单模块校准 —— 
        for (int i = 0; i < BMU_NUM; i++) {
            float v_min = bmu_cell_voltages_min[i];
            float t_min = bmu_cell_temps_at_min[i];
            float v_max = bmu_cell_voltages_max[i];
            float t_max = bmu_cell_temps_at_max[i];

            float soc_min = lookup_soc(t_min, v_min);
            float soc_max = lookup_soc(t_max, v_max);
            float soc0 = soc_min * 100.0f / (100.0f + soc_min - soc_max);

            float cur_soc = s_bmu_cal_soc[i].soc;
            if (fabsf(soc0 - cur_soc) > soc_threshold) {
                // 校正该 BMU 的 SOC
                s_bmu_cal_soc[i].soc = soc0;
                s_bmu_cal_soc[i].cumulative_ah = soc0 / 100.0f * s_bmu_real_capacity[i];
                s_bmu_cal_soc[i].accumulated_ah_since_correction = 0.0f;

                sid sid_mod = PDT_SID_SET_DEV_SN_SIG_VINDEX(
                    SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOC, 1, i + 1);
                set_bmu_soc = (int)round(soc0);
                pdt_set_data(sid_mod, &set_bmu_soc, sizeof(set_bmu_soc), type_int);
                pdt_set_data(sid_mod, &soc0, sizeof(soc0), type_float);
                adjust_ret->soc_adjust_type = enum_soc_adjust_non_platform_static;
                adjust_ret->soc_adjust_record_index[i] = TRUE;
            }
        }
    }

    return SUCCESS;
}

// 更新SOC校正告警状态相关数据
STATIC int update_soc_correction_alarm_judge_data(void)
{
    int i = 0;
    float max_accumulated_ah = 0.0f;
    int is_full_event = FALSE;
    int soc_cal_alarm_status = FALSE; // SOC校正告警状态

    // 1. 获取SOC校正告警状态
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_CLUSTER_SOC_CORRECTION_ALARM, &soc_cal_alarm_status, sizeof(soc_cal_alarm_status));

    // 2. 遍历所有BMU，找出最大累计充放电容量
    for (i = 0; i < BMU_NUM; i++) {
        if (s_bmu_cal_soc[i].accumulated_ah_since_correction > max_accumulated_ah) {
            max_accumulated_ah = s_bmu_cal_soc[i].accumulated_ah_since_correction;
        }
    }

    // 3. SOC校正告警置位时才判断是否满充满放
    if ((judge_bmu_charge_full_status() || judge_bmu_discharge_full_status()) && (soc_cal_alarm_status == TRUE)) {
        is_full_event = TRUE;
    } else {
        is_full_event = FALSE;
    }

    // 4. 更新SID数据
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_MAX_CHG_DISCHG_AH_SINCE_LAST_SOC_CALIBRATION, &max_accumulated_ah, sizeof(max_accumulated_ah), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_FULL_CHG_OR_DISCHG_STATUS, &is_full_event, sizeof(is_full_event), type_int);

    return SUCCESS;
}
