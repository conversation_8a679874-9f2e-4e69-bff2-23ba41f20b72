// 
/*****************************************************************************
 模块名      :  bcmu业务模块
 文件名      :  bcmu_business.c
 内容摘要    : 	主要用于是实现BCMU电池业务
 版本        : 	V1.0
*****************************************************************************/
#include <stdlib.h>
#include <unistd.h>
#include <math.h>
#include "oss/plat_oss.h"
#include "plat_error.h"
#include "pdt_dictionary_define.h"
#include "unitest.h"
#include "bcmu_business.h"
#include "product_oss_mid.h"
#include "product_oss_msg.h"
#include "plat_data_access.h"
#include "data_access.h"
#include "fapi_bmu_comm.h"
#include "bmu_business.h"
#include "pdt_common.h"
#include "plat_sps_cmd.h"
#include "bmu_soc.h"

int bmu_soh_data_syned[BMU_NUM] = {FALSE}; // 标记对应索引的BMU的SOH基准数据是否已成功从BMU读取到BCMU

STATIC bcmu_alm_data_t s_bcmu_alm_data;
STATIC bcmu_alm_data_t s_last_bcmu_alm_data = {0};

typedef struct {
    float Tmin;
    float Tmax;
    float SOCMin;
    float SOCMax;
    float ChargeRateC;
} TempSOCChargeMap;

#define INIT_SYS_TIME "2025-01-01 00:00:00"
#define NUM_TEMP_RANGES 11
#define NUM_SOC_RANGES  11

const TempSOCChargeMap temp_soc_map[NUM_TEMP_RANGES][NUM_SOC_RANGES] = {
    // T<0℃
    {{-1e9, 0, 0, 10, 0}, {-1e9, 0, 10, 20, 0}, {-1e9, 0, 20, 30, 0}, {-1e9, 0, 30, 40, 0}, {-1e9, 0, 40, 50, 0}, {-1e9, 0, 50, 60, 0}, {-1e9, 0, 60, 70, 0}, {-1e9, 0, 70, 80, 0}, {-1e9, 0, 80, 90, 0}, {-1e9, 0, 90, 95, 0}, {-1e9, 0, 95, 100, 0}},
    // 0≤T＜5℃
    {{0, 5, 0, 10, 0.05}, {0, 5, 10, 20, 0.05}, {0, 5, 20, 30, 0.05}, {0, 5, 30, 40, 0.05}, {0, 5, 40, 50, 0.05}, {0, 5, 50, 60, 0.05}, {0, 5, 60, 70, 0.05}, {0, 5, 70, 80, 0.05}, {0, 5, 80, 90, 0.05}, {0, 5, 90, 95, 0.05}, {0, 5, 95, 100, 0.05}},
    // 5≤T＜10℃
    {{5, 10, 0, 10, 0.1}, {5, 10, 10, 20, 0.1}, {5, 10, 20, 30, 0.1}, {5, 10, 30, 40, 0.1}, {5, 10, 40, 50, 0.1}, {5, 10, 50, 60, 0.1}, {5, 10, 60, 70, 0.1}, {5, 10, 70, 80, 0.1}, {5, 10, 80, 90, 0.1}, {5, 10, 90, 95, 0.1}, {5, 10, 95, 100, 0.1}},
    // 10≤T＜15℃
    {{10, 15, 0, 10, 0.2}, {10, 15, 10, 20, 0.2}, {10, 15, 20, 30, 0.2}, {10, 15, 30, 40, 0.2}, {10, 15, 40, 50, 0.2}, {10, 15, 50, 60, 0.2}, {10, 15, 60, 70, 0.2}, {10, 15, 70, 80, 0.2}, {10, 15, 80, 90, 0.2}, {10, 15, 90, 95, 0.2}, {10, 15, 95, 100, 0.2}},
    // 15≤T＜20℃
    {{15, 20, 0, 10, 0.5}, {15, 20, 10, 20, 0.5}, {15, 20, 20, 30, 0.5}, {15, 20, 30, 40, 0.5}, {15, 20, 40, 50, 0.5}, {15, 20, 50, 60, 0.5}, {15, 20, 60, 70, 0.5}, {15, 20, 70, 80, 0.5}, {15, 20, 80, 90, 0.5}, {15, 20, 90, 95, 0.5}, {15, 20, 95, 100, 0.5}},
    // 20≤T＜25℃
    {{20, 25, 0, 10, 0.5}, {20, 25, 10, 20, 0.5}, {20, 25, 20, 30, 0.5}, {20, 25, 30, 40, 0.5}, {20, 25, 40, 50, 0.5}, {20, 25, 50, 60, 0.5}, {20, 25, 60, 70, 0.5}, {20, 25, 70, 80, 0.5}, {20, 25, 80, 90, 0.5}, {20, 25, 90, 95, 0.5}, {20, 25, 95, 100, 0.5}},
    // 25≤T＜45℃
    {{25, 45, 0, 10, 0.5}, {25, 45, 10, 20, 0.5}, {25, 45, 20, 30, 0.5}, {25, 45, 30, 40, 0.5}, {25, 45, 40, 50, 0.5}, {25, 45, 50, 60, 0.5}, {25, 45, 60, 70, 0.5}, {25, 45, 70, 80, 0.5}, {25, 45, 80, 90, 0.5}, {25, 45, 90, 95, 0.5}, {25, 45, 95, 100, 0.5}},
    // 45≤T＜50℃
    {{45, 50, 0, 10, 0.5}, {45, 50, 10, 20, 0.5}, {45, 50, 20, 30, 0.5}, {45, 50, 30, 40, 0.5}, {45, 50, 40, 50, 0.5}, {45, 50, 50, 60, 0.5}, {45, 50, 60, 70, 0.5}, {45, 50, 70, 80, 0.5}, {45, 50, 80, 90, 0.5}, {45, 50, 90, 95, 0.5}, {45, 50, 95, 100, 0.5}},
    // 50≤T＜55℃
    {{50, 55, 0, 10, 0.5}, {50, 55, 10, 20, 0.5}, {50, 55, 20, 30, 0.5}, {50, 55, 30, 40, 0.5}, {50, 55, 40, 50, 0.5}, {50, 55, 50, 60, 0.5}, {50, 55, 60, 70, 0.5}, {50, 55, 70, 80, 0.5}, {50, 55, 80, 90, 0.5}, {50, 55, 90, 95, 0.5}, {50, 55, 95, 100, 0.5}},
    // 55≤T≤60℃
    {{55, 60, 0, 10, 0.2}, {55, 60, 10, 20, 0.2}, {55, 60, 20, 30, 0.2}, {55, 60, 30, 40, 0.2}, {55, 60, 40, 50, 0.2}, {55, 60, 50, 60, 0.2}, {55, 60, 60, 70, 0.2}, {55, 60, 70, 80, 0.2}, {55, 60, 80, 90, 0.2}, {55, 60, 90, 95, 0.2}, {55, 60, 95, 100, 0.2}},
    // T>60℃
    {{60, 1e9, 0, 10, 0}, {60, 1e9, 10, 20, 0}, {60, 1e9, 20, 30, 0}, {60, 1e9, 30, 40, 0}, {60, 1e9, 40, 50, 0}, {60, 1e9, 50, 60, 0}, {60, 1e9, 60, 70, 0}, {60, 1e9, 70, 80, 0}, {60, 1e9, 80, 90, 0}, {60, 1e9, 90, 95, 0}, {60, 1e9, 95, 100, 0}},
  
};

STATIC float s_ocv_temp_map[OCV_TAB_SIZE_TEMP_NUM] = {
    -30, -20, -15, -10, -5, 0, 5, 10, 25, 45, 50
};

/* Started by AICoder, pid:t3b77beeacb018914ea9088aa065c92e3a20156a */
// 单体离群可配置策略二 静态OCV表
STATIC float s_ocv_temp_soc_tab[OCV_TAB_SIZE_TEMP_NUM][OCV_TAB_SIZE_SOC_NUM] = {
// SOC= 0       5      10      15      20      25       30     35      40      45      50      55      60      65      70      75      80      85      90      95      100
    {1.8253, 1.8569, 2.0187, 2.1647, 2.2939, 2.4134, 2.5180, 2.6106, 2.6914, 2.7596, 2.8161, 2.8620, 2.8974, 2.9222, 2.9409, 2.9541, 2.9612, 2.9603, 2.9504, 2.9304, 3.3744},  // -30度
    {2.0387, 2.1140, 2.2019, 2.2832, 2.3683, 2.4549, 2.5364, 2.6116, 2.6800, 2.7424, 2.7986, 2.8473, 2.8878, 2.9198, 2.9436, 2.9597, 2.9679, 2.9669, 2.9556, 2.9323, 3.3826},  // -20度
    {2.1087, 2.1187, 2.2647, 2.3887, 2.4971, 2.5948, 2.6801, 2.7544, 2.8182, 2.8717, 2.9159, 2.9518, 2.9800, 3.0009, 3.0168, 3.0279, 3.0340, 3.0339, 3.0267, 3.0116, 3.3827},  // -15度
    {2.2104, 2.1819, 2.3271, 2.4616, 2.5768, 2.6761, 2.7638, 2.8420, 2.9090, 2.9623, 3.0017, 3.0303, 3.0508, 3.0660, 3.0767, 3.0837, 3.0874, 3.0869, 3.0819, 3.0717, 3.3720},  // -10度
    {2.1520, 2.3806, 2.5107, 2.6127, 2.7002, 2.7762, 2.8421, 2.8983, 2.9450, 2.9838, 3.0156, 3.0415, 3.0625, 3.0796, 3.0927, 3.1018, 3.1069, 3.1075, 3.1030, 3.0928, 3.3910},  // -5度
    {2.2526, 2.3151, 2.5284, 2.6791, 2.8004, 2.9008, 2.9798, 3.0361, 3.0731, 3.0978, 3.1152, 3.1283, 3.1383, 3.1463, 3.1529, 3.1580, 3.1616, 3.1635, 3.1632, 3.1605, 3.3864},  // 0度
    {2.6335, 2.6424, 2.7567, 2.8368, 2.9033, 2.9576, 3.0042, 3.0421, 3.0718, 3.0959, 3.1154, 3.1313, 3.1450, 3.1583, 3.1686, 3.1756, 3.1797, 3.1810, 3.1793, 3.1740, 3.3992},  // 5度
    {2.7133, 2.7669, 2.9281, 3.0318, 3.0913, 3.1257, 3.1476, 3.1627, 3.1738, 3.1825, 3.1899, 3.1962, 3.2020, 3.2074, 3.2127, 3.2178, 3.2221, 3.2253, 3.2270, 3.2273, 3.4164},  // 10度
    {2.9013, 3.0407, 3.1218, 3.1570, 3.1830, 3.1994, 3.2117, 3.2208, 3.2275, 3.2328, 3.2372, 3.2417, 3.2470, 3.2545, 3.2628, 3.2688, 3.2728, 3.2752, 3.2768, 3.2773, 3.4408},  // 25度
    {2.7769, 3.1021, 3.1522, 3.1752, 3.1996, 3.2159, 3.2309, 3.2409, 3.2463, 3.2502, 3.2533, 3.2562, 3.2608, 3.2706, 3.2801, 3.2849, 3.2876, 3.2894, 3.2907, 3.2905, 3.4165},  // 45度
    {2.7265, 3.1072, 3.1607, 3.1814, 3.2052, 3.2211, 3.2401, 3.2524, 3.2565, 3.2592, 3.2616, 3.2624, 3.2695, 3.2829, 3.2897, 3.2932, 3.2951, 3.2969, 3.2983, 3.2991, 3.3841},  // 50度
};
/* Ended by AICoder, pid:t3b77beeacb018914ea9088aa065c92e3a20156a */

#ifndef UNITTEST
    STATIC bcmu_dig_data_t s_bcmu_dig_data;
    STATIC float s_cell_volt[BMU_NUM][BMU_CELL_NUM] = {0};
    STATIC outlier_info_t s_outliers[MAX_OUTLIERS] = {0};
    STATIC outlier_info_t s_last_outliers[MAX_OUTLIERS] = {0};
    STATIC int s_outlier_count = 0;
    STATIC cell_history_t s_outlier_history[TOTAL_CELLS] = {0};
    STATIC float s_cell_soc[BMU_NUM][BMU_CELL_NUM] = {0.0f};
    STATIC float s_cell_avg_soc = 0.0f;
    STATIC float s_cell_avg_volt = 0.0f;
    STATIC float s_cell_soc_dev_fabs[TOTAL_CELLS] = {0.0f};
    STATIC float s_cell_volt_dev[TOTAL_CELLS] = {0.0f};
    STATIC int judge_sys_time_valid(void);
    STATIC int deal_bcmu_business(void);
    STATIC int load_bcmu_ana_data(void);
    STATIC int load_bcmu_dig_data(void);
    STATIC int load_bcmu_alm_data(void);
    STATIC int update_prt_status(void);
    STATIC int update_chg_prt_status(void);
    STATIC int update_dischg_prt_status(void);
    STATIC int get_cutoff_status_by_sid( sid cutoff_sta_sid);
    STATIC int update_bcmu_state(void);
    STATIC int update_cut_off_status(void);
    STATIC int judge_cell_cutoff_status(int_type_t* cutoff_alm_val);
    STATIC int judge_cell_temp_cutoff_status(int_type_t* cutoff_alm_val);
    STATIC int set_bcmu_prt_status(void);
    STATIC int update_bcmu_battery_status(void);
    STATIC int restore_bcmu_no_backlash_alm(void);
    STATIC int calc_max_charge_current(void);
    STATIC float update_charging_rate(float current_charge_rate);
    STATIC int is_valid(float value);
    STATIC float get_charge_rate_for_temp_and_soc(int i, float SOC);
    STATIC float calc_charging_rate(float Tmin, float Tmax);
    STATIC int step_constant_power_charging(float charging_rate, float charging_current_pcs);
    STATIC int exe_opt_para_asy_cmd(sid sig_id);
    STATIC float optimizer_on_update_charge_rate(int dev_sn, float charging_rate_tmp);
    STATIC int update_cell_outliers_status(void);
    STATIC int should_calculate_outliers(void);
    STATIC int load_bmu_cell_volt(float (*voltages)[BMU_CELL_NUM]);
    STATIC int judge_cell_outliers1(float (*voltages)[BMU_CELL_NUM]);
    STATIC int remove_extreme_voltages(float (*voltages)[BMU_CELL_NUM], int *valid_cells, float mean, int remove_count);
    STATIC float calculate_std_dev(float (*voltages)[BMU_CELL_NUM], int *valid_cells, float mean);
    STATIC float calculate_mean(float (*voltages)[BMU_CELL_NUM], int *valid_cells);
    STATIC int calculate_z_scores(float (*voltages)[BMU_CELL_NUM], float mean, float std_dev, float *z_scores);
    STATIC int update_outliers(float (*voltages)[BMU_CELL_NUM], float mean, float *z_scores, cell_history_t *outlier_history, outlier_info_t *outliers, int *outlier_count);
    STATIC int update_outliners_info(float (*voltages)[BMU_CELL_NUM], float mean, float *z_scores, outlier_info_t *outliers, int outlier_count);
    STATIC int save_outliers_info(void);
    STATIC int should_calculate_outliers2(void);
    STATIC int judge_cell_outliers2(float (*voltages)[BMU_CELL_NUM]);
    STATIC int calculate_cell_soc_volt_dev(float (*voltages)[BMU_CELL_NUM]);
    STATIC int sort_soc_dev_fabs(int *sorted_indices);
    STATIC int is_cell_volt_outlier_pos_change(void);
    STATIC int get_battery_capacity();
    STATIC int update_power_data();
    STATIC float calculate_max_power(enum OperationType op_type);
    STATIC int do_replace_finished_judge(void);
    STATIC int handle_bmu_soh_data_ready(void *msg);
    STATIC int get_soc_from_ocv_table(float temperature, float volt, ocv_area_t *result);
    STATIC unsigned int get_point_index_by_median_lookup(float* array, unsigned int array_size, float value);
    STATIC int set_bcmu_business_period(void);
#endif

/* Started by AICoder, pid:67208t5801pea851497d0b2d8027931b8f53fb6f */
STATIC float DisChargeMaxCurrent_Table[110][3]=
{
    // soc>=95%     soc>=90%     soc>=80%      soc>=70%      soc>=60%      soc>=50%      soc>=40%      soc>=30%      soc>=20%       soc>=10%      soc>=0%
    {95,55,0.2},  {90,55,0.2},  {80,55,0.2},  {70,55,0.2},  {60,55,0.2},  {50,55,0.2},  {40,55,0.2},  {30,55,0.2},  {20,55,0.2},   {10,55,0.2},  {0,55,0.2},  // >=55°C
    {95,50,0.5},  {90,50,0.5},  {80,50,0.5},  {70,50,0.5},  {60,50,0.5},  {50,50,0.5},  {40,50,0.5},  {30,50,0.5},  {20,50,0.5},   {10,50,0.5},  {0,50,0.5},  // >=50°C
    {95,45,0.5},  {90,45,0.5},  {80,45,0.5},  {70,45,0.5},  {60,45,0.5},  {50,45,0.5},  {40,45,0.5},  {30,45,0.5},  {20,45,0.5},   {10,45,0.5},  {0,45,0.5},  // >=45°C
    {95,25,0.5},  {90,25,0.5},  {80,25,0.5},  {70,25,0.5},  {60,25,0.5},  {50,25,0.5},  {40,25,0.5},  {30,25,0.5},  {20,25,0.5},   {10,25,0.5},  {0,25,0.5},  // >=25°C
    {95,20,0.5},  {90,20,0.5},  {80,20,0.5},  {70,20,0.5},  {60,20,0.5},  {50,20,0.5},  {40,20,0.5},  {30,20,0.5},  {20,20,0.5},   {10,20,0.5},  {0,20,0.5},  // >=20°C
    {95,15,0.5},  {90,15,0.5},  {80,15,0.5},  {70,15,0.5},  {60,15,0.5},  {50,15,0.5},  {40,15,0.5},  {30,15,0.5},  {20,15,0.5},   {10,15,0.5},  {0,15,0.5},  // >=15°C
    {95,10,0.5},  {90,10,0.5},  {80,10,0.5},  {70,10,0.5},  {60,10,0.5},  {50,10,0.5},  {40,10,0.5},  {30,10,0.5},  {20,10,0.5},   {10,10,0.5},  {0,10,0.5},  // >=10°C
    {95,5,0.5},   {90,5,0.5},   {80,5,0.5},   {70,5,0.5},   {60,5,0.5},   {50,5,0.5},   {40,5,0.5},   {30,5,0.5},   {20,5,0.5},    {10,5,0.5},   {0,5,0.5},   // >=5°C
    {95,0,0.5},   {90,0,0.5},   {80,0,0.5},   {70,0,0.5},   {60,0,0.5},   {50,0,0.5},   {40,0,0.5},   {30,0,0.5},   {20,0,0.5},    {10,0,0.5},   {0,0,0.5}    // >=0°C
};
/* Ended by AICoder, pid:67208t5801pea851497d0b2d8027931b8f53fb6f */

STATIC int load_bcmu_ana_data(void)
{
    //todo:
    return SUCCESS;
}

/* Started by AICoder, pid:g71e2e673f7e87b14db10beae0d35d2b459375df */
STATIC int load_bcmu_dig_data(void)
{
    static sig_2_sid_info_t sig_2_sid_map[] = {
        {&s_bcmu_dig_data.module_vol_high_cut_off_sta, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_VOLT_HIGH_CUT_OFF_STATUS, 1, sizeof(int), type_int},
        {&s_bcmu_dig_data.cell_chg_high_temp_cut_off_sta, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_CHARGE_HIGH_TEMPERATURE_CUT_OFF_STATUS, 1, sizeof(int), type_int},
        {&s_bcmu_dig_data.cell_chg_low_temp_cut_off_sta, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_CHARGE_LOW_TEMPERATURE_CUT_OFF_STATUS, 1, sizeof(int), type_int},
        {&s_bcmu_dig_data.module_vol_low_cut_off_sta, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_VOLTAGE_LOW_CUT_OFF_STATUS, 1, sizeof(int), type_int},
        {&s_bcmu_dig_data.cell_vol_high_cut_off_sta, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_VOLT_HIGH_CUT_OFF_STATUS, 1, sizeof(int), type_int},
        {&s_bcmu_dig_data.cell_vol_low_cut_off_sta, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_VOLT_LOW_CUT_OFF_STATUS, 1, sizeof(int), type_int},
        {NULL, 0, 0, 0, 0} // 确保最后一个元素的type字段也初始化为0
    };

    static dev_2_sid_info_t dev_2_sid_info = {
        sig_2_sid_map,
        1,
        (sizeof(sig_2_sid_map) / sizeof(sig_2_sid_info_t)) - 1,
        sizeof(bcmu_dig_data_t),
        NULL
    };

    get_sm_data_to_custom_struct_by_dev(&dev_2_sid_info);
    return SUCCESS;
}
/* Ended by AICoder, pid:g71e2e673f7e87b14db10beae0d35d2b459375df */

/* Started by AICoder, pid:s88905030av273a1428a082600da122e16b21761 */
STATIC int load_bcmu_alm_data(void)
{
    // 使用静态局部变量来存储映射信息，确保只初始化一次
    static const sig_2_sid_info_t sig_2_sid_map[] = {
        {&s_bcmu_alm_data.module_chg_vol_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_MODULE_CHARGE_VOLTAGE_DIFFERENCE_END_ALARM, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.module_dischg_vol_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_MODULE_DISCHARGE_VOLTAGE_DIFFERENCE_ALARM_END, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.module_chg_temp_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_MODULE_CHARGE_TEMPERATURE_DIF_ALARM_END, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.module_dischg_temp_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_MODULE_DISCHARGE_TEMPERATURE_DIF_ALARM_END, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.cell_chg_vol_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_CELL_CHARGE_VOLTAGE_EXTREMELY_DIFF_END_ALARM, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.cell_dischg_vol_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_CELL_DISCHARGE_VOLTAGE_EXTREMELY_DIFF_ALARM_END, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.chg_vol_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_CLUSTER_CHARGE_VOLTAGE_CUT_OFF_ALARM, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.dischg_vol_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_CLUSTER_DISCHARGE_VOLTAGE_CUT_OFF_ALARM, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.chg_curr_alm_lv1, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_CHARGE_CURRENT_ALARM_LEVEL1, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.chg_curr_alm_lv2, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_CHARGE_CURRENT_ALARM_LEVEL2, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.chg_curr_alm_lv3, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_CHARGE_CURRENT_ALARM_LEVEL3, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.chg_curr_alm_end, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_CHARGE_CURRENT_ALARM_END, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.dischg_curr_alm_lv1, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_DISCHARGE_CURRENT_ALARM_LEVEL1, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.dischg_curr_alm_lv2, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_DISCHARGE_CURRENT_ALARM_LEVEL2, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.dischg_curr_alm_lv3, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_DISCHARGE_CURRENT_ALARM_LEVEL3, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.dischg_curr_alm_end, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_DISCHARGE_CURRENT_ALARM_END, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.ems_comm_fail, SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_EMS_COMMUNICATION_FAIL, 1, sizeof(int), type_int},
        {&s_bcmu_alm_data.module_term_temp_diff_lv1_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_MODULE_TERMINAL_TEMPERATURE_DIFFERENCE_HIGH_ALARM_LEVEL1, 1, sizeof(int), type_int},
        {NULL, 0, 0, 0, 0} // 确保所有字段都正确初始化
    };

    // 静态设备到SID信息映射结构体
    static dev_2_sid_info_t dev_2_sid_info = {
        sig_2_sid_map,
        1,
        (sizeof(sig_2_sid_map) / sizeof(sig_2_sid_info_t)) - 1,
        sizeof(bcmu_alm_data_t),
        NULL
    };

    // 调用函数获取数据并填充到自定义结构体中
    get_sm_data_to_custom_struct_by_dev(&dev_2_sid_info);

    return SUCCESS;
}
/* Ended by AICoder, pid:s88905030av273a1428a082600da122e16b21761 */

/// @brief 更新BCMU相关状态
/// @return 更新成功
STATIC int update_bcmu_state(void)
{
    update_cut_off_status();
    update_prt_status();
    update_bcmu_battery_status();
    return SUCCESS;
}

STATIC int get_cutoff_status_by_sid( sid cutoff_sta_sid) {
    int cutoff_sta = 0;

    pdt_get_data(cutoff_sta_sid, &cutoff_sta, sizeof(int));
    return cutoff_sta;
}

/* Started by AICoder, pid:29d06zd28bv852c14322093cd02b175749749b39 */
STATIC float calc_discharging_rate(float temp, float soc)
{
    int i = 0;
    float discharge_max_current = 0;

    // 检查温度是否在有效范围内
    if (!(temp >= 0 && temp <= 60))
    {
        return discharge_max_current; // 温度无效时，设置最大放电电流为0
    }

    // 遍历表格以找到合适的最大放电电流
    for (i = 0; i < 110; i++)
    {
        if (soc >= DisChargeMaxCurrent_Table[i][0] && temp >= DisChargeMaxCurrent_Table[i][1])
        {
            discharge_max_current = TOTAL_CAPACITY * DisChargeMaxCurrent_Table[i][2]; // 计算最大放电电流
            break;
        }
    }

    return discharge_max_current;
}

STATIC int update_bcmu_discharge_max_current(void)
{
    sid sid_cell_tem, mod_soc_sid;
    int i = 0, j = 0, cell_tem_ret = 0;
    float current = 0.0f,  cell_tem = 0.0f;
    int mod_soc = 0;
    float discharge_max_current = TOTAL_CAPACITY;
    float Tmax = MIN_BATTERY_MODEL_CELL_TEMP, Tmin = MAX_BATTERY_MODEL_CELL_TEMP;

    for (i = 0; i < BMU_NUM; i++)
    {
        if (is_bmu_comm_normal(i + 1) == NORMAL) 
        {
            for (j = 0; j < BMU_CELL_TEMP_NUM; j++)
            {
                sid_cell_tem = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CELL_TEMPERATURE, i + 1, j + 1);
                cell_tem_ret = pdt_get_data(sid_cell_tem, &cell_tem, sizeof(cell_tem));
                if (cell_tem_ret != SUCCESS)
                {
                    continue;
                }
                Tmax = MAX(Tmax, cell_tem);
                Tmin = MIN(Tmin, cell_tem);
            }
            mod_soc_sid = SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOC,  1, i + 1);
            pdt_get_data(mod_soc_sid, &mod_soc, sizeof(mod_soc));
            current = MIN(calc_discharging_rate(Tmin, mod_soc), calc_discharging_rate(Tmax, mod_soc));
        }
        else
        {
            current = TOTAL_CAPACITY * 0.5f;
        }

        discharge_max_current = MIN(discharge_max_current, current);
    }

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_DISCHARGE_MAX_CURRENT, &discharge_max_current, sizeof(float), type_float);
    return SUCCESS;
}
/* Ended by AICoder, pid:29d06zd28bv852c14322093cd02b175749749b39 */

/* Started by AICoder, pid:c55d0s7a46r93cd140b00913b04b46454c523d3d */
STATIC int update_cut_off_status(void)
{
    int i = 0;
    bmu_alm_data_t bmu_alm_data[BMU_NUM];
    bcmu_cut_off_sta_t bcmu_cut_off_sta;

    plat_memset_s(&bcmu_cut_off_sta, sizeof(bcmu_cut_off_sta_t), 0x00, sizeof(bcmu_cut_off_sta_t));

    // 获取BMU告警数据
    get_bmu_alm_data(&bmu_alm_data[0]);

    for (i = 0; i < BMU_NUM; ++i)
    {
        if (is_bmu_exist(i + 1) == SUCCESS)
        {
            bcmu_cut_off_sta.module_vol_high_cut_off_sta.i_value |= bmu_alm_data[i].bmu_vol_high_end_alarm.i_value;
            bcmu_cut_off_sta.module_vol_low_cut_off_sta.i_value |= bmu_alm_data[i].bmu_vol_low_end_alarm.i_value;
            bcmu_cut_off_sta.cell_vol_high_cut_off_sta.i_value |= judge_cell_cutoff_status(bmu_alm_data[i].cell_volt_high_cutoff_alm);
            bcmu_cut_off_sta.cell_vol_low_cut_off_sta.i_value |= judge_cell_cutoff_status(bmu_alm_data[i].cell_volt_low_cutoff_alm);
            bcmu_cut_off_sta.cell_chg_high_temp_cut_off_sta.i_value |= judge_cell_temp_cutoff_status(bmu_alm_data[i].cell_charge_temp_high_cutoff_alm);
            bcmu_cut_off_sta.cell_chg_low_temp_cut_off_sta.i_value |= judge_cell_temp_cutoff_status(bmu_alm_data[i].cell_charge_temp_low_cutoff_alm);
            bcmu_cut_off_sta.cell_dischg_high_temp_cut_off_sta.i_value |= judge_cell_temp_cutoff_status(bmu_alm_data[i].cell_discharge_temp_high_cutoff_alm);
            bcmu_cut_off_sta.cell_dischg_low_temp_cut_off_sta.i_value |= judge_cell_temp_cutoff_status(bmu_alm_data[i].cell_discharge_temp_low_cutoff_alm);
        }
    }

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_VOLT_HIGH_CUT_OFF_STATUS, &bcmu_cut_off_sta.module_vol_high_cut_off_sta.i_value, sizeof(int), type_int); 
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_VOLTAGE_LOW_CUT_OFF_STATUS, &bcmu_cut_off_sta.module_vol_low_cut_off_sta.i_value, sizeof(int), type_int); 
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_VOLT_HIGH_CUT_OFF_STATUS, &bcmu_cut_off_sta.cell_vol_high_cut_off_sta.i_value, sizeof(int), type_int); 
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_VOLT_LOW_CUT_OFF_STATUS, &bcmu_cut_off_sta.cell_vol_low_cut_off_sta.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_CHARGE_HIGH_TEMPERATURE_CUT_OFF_STATUS, &bcmu_cut_off_sta.cell_chg_high_temp_cut_off_sta.i_value, sizeof(int), type_int); 
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_CHARGE_LOW_TEMPERATURE_CUT_OFF_STATUS, &bcmu_cut_off_sta.cell_chg_low_temp_cut_off_sta.i_value, sizeof(int), type_int); 
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_DISCHARGE_HIGH_TEMPERATURE_CUT_OFF_STATUS, &bcmu_cut_off_sta.cell_dischg_high_temp_cut_off_sta.i_value, sizeof(int), type_int); 
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_DISCHARGE_LOW_TEMPERATURE_CUT_OFF_STATUS, &bcmu_cut_off_sta.cell_dischg_low_temp_cut_off_sta.i_value, sizeof(int), type_int);   


    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_CHARGE_VOLTAGE_EXTREMELY_DIFF_CUT_OFF_STATUS, &s_bcmu_alm_data.module_chg_vol_diff_end_alarm.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_DISCHARGE_VOLTAGE_EXTREMELY_DIFF_CUT_OFF_STATUS, &s_bcmu_alm_data.module_dischg_vol_diff_end_alarm.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CHARGE_TEMPERATURE_EXTREMELY_DIFF_CUT_OFF_STATUS, &s_bcmu_alm_data.module_chg_temp_diff_end_alarm.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_DISCHARGE_TEMPERATURE_EXTREMELY_DIFF_CUT_OFF_STATUS, &s_bcmu_alm_data.module_dischg_temp_diff_end_alarm.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_CHARGE_VOLTAGE_EXTREMELY_DIFF_CUT_OFF_STATUS, &s_bcmu_alm_data.cell_chg_vol_diff_end_alarm.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_DISCHARGE_VOLTAGE_EXTREMELY_DIFF_CUT_OFF_STATUS, &s_bcmu_alm_data.cell_dischg_vol_diff_end_alarm.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_VOLTAGE_HIGH_CUT_OFF_STATUS, &s_bcmu_alm_data.chg_vol_end_alarm.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_VOLTAGE_LOW_CUT_OFF_STATUS, &s_bcmu_alm_data.dischg_vol_end_alarm.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CHARGE_CURRENT_CUT_OFF_STATUS, &s_bcmu_alm_data.chg_curr_alm_end.i_value, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_DISCHARGE_CURRENT_CUT_OFF_STATUS, &s_bcmu_alm_data.dischg_curr_alm_end.i_value, sizeof(int), type_int);
    return SUCCESS;
}
/* Ended by AICoder, pid:c55d0s7a46r93cd140b00913b04b46454c523d3d */

/* Started by AICoder, pid:bc403h27575063414bf209d43085232074c52807 */
STATIC int judge_cell_cutoff_status(int_type_t* cutoff_alm_val) {
    int i = 0;
    int cutoff_sta = NORMAL;

    for (i = 0; i < BMU_CELL_NUM; i++) {
        if (cutoff_alm_val[i].i_value == ABNORMAL) {
            cutoff_sta = ABNORMAL;
            break;
        }
    }
    return cutoff_sta;
}

STATIC int judge_cell_temp_cutoff_status(int_type_t* cell_cutoff_alm_val) {
    int i = 0;
    int cutoff_sta = NORMAL;

    for (i = 0; i < BMU_CELL_TEMP_NUM; i++) {
        if (cell_cutoff_alm_val[i].i_value == ABNORMAL) {
            cutoff_sta = ABNORMAL;
            return cutoff_sta;
        }
    }
    return cutoff_sta;
}
/* Ended by AICoder, pid:bc403h27575063414bf209d43085232074c52807 */

/* Started by AICoder, pid:hea5d9f98e30d2b149a90a3030e8cd31ce985a19 */
STATIC int update_prt_status(void)
{
    int i = 0;
    bmu_alm_data_t bmu_alm_data[BMU_NUM];
    int prt_status = NORMAL;
    
    // 获取BMU告警数据
    get_bmu_alm_data(&bmu_alm_data[0]);
 
    for (i = 0; i < BMU_NUM; ++i)
    {
        if (is_bmu_exist(i + 1) == FAULT) {
            continue;
        }

        prt_status |= bmu_alm_data[i].bmu_comm_fail_alm.i_value;

        if (is_bmu_comm_normal(i + 1) == FAULT) {
            pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_CHARGE_PROTECT_STATUS, &prt_status, sizeof(int), type_int);
            pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_DISCHARGE_PROTECT_STATUS, &prt_status, sizeof(int), type_int);
            return SUCCESS;
        }

        prt_status |= bmu_alm_data[i].bmu_term_high_alm.i_value;
        prt_status |= if_exist_one_alarm(bmu_alm_data[i].cell_temp_sample_fault, BMU_CELL_TEMP_NUM);

        if (prt_status & ABNORMAL) {
            pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_CHARGE_PROTECT_STATUS, &prt_status, sizeof(int), type_int);
            pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_DISCHARGE_PROTECT_STATUS, &prt_status, sizeof(int), type_int);
            return SUCCESS;
        }
    }

    update_chg_prt_status();
    update_dischg_prt_status();
    return SUCCESS;
}
/* Ended by AICoder, pid:hea5d9f98e30d2b149a90a3030e8cd31ce985a19 */

STATIC int update_chg_prt_status(void)
{
    int prt_status = NORMAL;
    //截止状态
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_CHARGE_HIGH_TEMPERATURE_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_CHARGE_LOW_TEMPERATURE_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_VOLT_HIGH_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_VOLT_HIGH_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_CHARGE_VOLTAGE_EXTREMELY_DIFF_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CHARGE_TEMPERATURE_EXTREMELY_DIFF_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_CHARGE_VOLTAGE_EXTREMELY_DIFF_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_VOLTAGE_HIGH_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CHARGE_CURRENT_CUT_OFF_STATUS);
    prt_status |= s_bcmu_alm_data.ems_comm_fail.i_value;
    prt_status |= get_cutoff_status_by_sid(SID_POWER_CONVERSION_SYSTEM_ALM_PCS_COMMFAIL);
    prt_status |= s_bcmu_alm_data.module_term_temp_diff_lv1_alarm.i_value;

    /* Started by AICoder, pid:n8f4akee804a51f14dc4094330b2780baf9733d4 */
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_FLOOD_ALARM);  // 水浸禁充
    prt_status |= get_cutoff_status_by_sid(SID_TONG_FEI_LIQUID_COOLING_ALM_TF_COMMFAIL_ALARM);  // 液冷通讯断禁充
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_ATRS_SENSOR_COMM_FAILED_ALARM);  // 电池柜气体传感器通讯断禁充
    // 消防三级告警禁充
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_FIRE_ALARM_LEVEL1);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_FIRE_ALARM_LEVEL2);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_FIRE_ALARM_LEVEL3);
    /* Ended by AICoder, pid:n8f4akee804a51f14dc4094330b2780baf9733d4 */
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_EOL_LOCK);   //储能系统EOL锁定禁充

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_CHARGE_PROTECT_STATUS, &prt_status, sizeof(int), type_int);

    return SUCCESS;
}

STATIC int update_dischg_prt_status(void)
{
    int prt_status = NORMAL;

    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_VOLTAGE_LOW_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_VOLT_LOW_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_DISCHARGE_TEMPERATURE_EXTREMELY_DIFF_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_MODULE_DISCHARGE_VOLTAGE_EXTREMELY_DIFF_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_DISCHARGE_VOLTAGE_EXTREMELY_DIFF_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_VOLTAGE_LOW_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_DISCHARGE_HIGH_TEMPERATURE_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_CELL_DISCHARGE_LOW_TEMPERATURE_CUT_OFF_STATUS);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_DISCHARGE_CURRENT_CUT_OFF_STATUS);
    prt_status |= s_bcmu_alm_data.ems_comm_fail.i_value;
    prt_status |= get_cutoff_status_by_sid(SID_POWER_CONVERSION_SYSTEM_ALM_PCS_COMMFAIL);
    prt_status |= s_bcmu_alm_data.module_term_temp_diff_lv1_alarm.i_value;

    /* Started by AICoder, pid:m1ed89487dt908314d9d0ba2e0c4590aceb737a8 */
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_FLOOD_ALARM);  // 水浸禁放
    prt_status |= get_cutoff_status_by_sid(SID_TONG_FEI_LIQUID_COOLING_ALM_TF_COMMFAIL_ALARM);  // 液冷通讯断禁放
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_ATRS_SENSOR_COMM_FAILED_ALARM);  // 电池柜气体传感器通讯断禁放
    // 消防三级告警禁放
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_FIRE_ALARM_LEVEL1);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_FIRE_ALARM_LEVEL2);
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_SYSTEM_MANAGEMENT_UNIT_ALM_BATTERY_CABINET_FIRE_ALARM_LEVEL3);
    /* Ended by AICoder, pid:m1ed89487dt908314d9d0ba2e0c4590aceb737a8 */
    prt_status |= get_cutoff_status_by_sid(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_EOL_LOCK);   //储能系统EOL锁定禁放

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_DISCHARGE_PROTECT_STATUS, &prt_status, sizeof(int), type_int);

    return SUCCESS;
}

//BCMU软件重启充电保护和放电保护均置起
STATIC int set_bcmu_prt_status(void)
{
    int prt_status = ABNORMAL;

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_CHARGE_PROTECT_STATUS, &prt_status, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_DISCHARGE_PROTECT_STATUS, &prt_status, sizeof(int), type_int);
    return SUCCESS;
}

/*===================================================================
 函数名称 : update_bcmu_battery_status
 函数功能 : 判断电池状态
 实现算法 :
 全局变量 : 无
 输入参数 :
 返 回 值 : SUCCESS 
 修改记录:
===================================================================*/
STATIC int update_bcmu_battery_status(void)
{ 
    int bcmu_battery_status = 0;
    float current = 0;

    CHECK_FUN_RET_INT(pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT, &current, sizeof(float)),0);

    if(current < -CURRENT_DETECTION_ERROR)
    {
        bcmu_battery_status = BCMU_BAT_STATUS_CHARGE;
    }
    else if(current > CURRENT_DETECTION_ERROR)
    {
        bcmu_battery_status = BCMU_BAT_STATUS_DISCHARGE;
    }
    else
    {
        bcmu_battery_status = BCMU_BAT_STATUS_STANDY;
    }

    CHECK_FUN_RET_INT(pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(int), type_int),0);
    return SUCCESS;
}

/* Started by AICoder, pid:qfe25t87d9kcb6b146ef09e320028c22b329f757 */
STATIC int restore_bcmu_no_backlash_alm(void) {
    int i;
    static boolean settimer_flag = FALSE;
    static int count = 0;    
    int_type_t *last_p = (int_type_t *)&s_last_bcmu_alm_data;
    int_type_t *p = (int_type_t *)&s_bcmu_alm_data;

    for (i = 8; i <= 15; i++) {
        if (last_p[i].i_value != p[i].i_value) {
            if (p[i].i_value != 0) {
                settimer_flag = TRUE;
                kill_timer(PCHAR_TO_MID(MID_BCMUAPP_BCMU), TIMER3);
                set_timer(PCHAR_TO_MID(MID_BCMUAPP_BCMU), TIMER3, BCMUAPP_BCMU_ALM_RESTORE_TIMER);
            }
            last_p[i].i_value = p[i].i_value;
        }

        if (p[i].i_value == 0) {
            count++;
        }
    }

    if (settimer_flag && count == 8) {
        count = 0;
        settimer_flag = FALSE;
        kill_timer(PCHAR_TO_MID(MID_BCMUAPP_BCMU), TIMER3);
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:qfe25t87d9kcb6b146ef09e320028c22b329f757 */

/** 
 * @brief BCMU初始化.  
 * @details BCMU消息初始化 
 * @retval  OK  成功  
 * @note  注解
 * @par 其它
 *      无
 * @par 
 * @code 
 * @endcode 
 * @par 修改日志 
 *       
 */ 
int bcmuapp_bcmu_init(void) {
    set_bcmu_prt_status();
    init_sox();
    /* init timers */
    // if (set_timer(PCHAR_TO_MID(MID_BCMUAPP_BCMU), TIMER1, BCMUAPP_BCMU_TIMER) != SUCCESS) {
    //     return FAILURE;
    // }
    if (set_timer(PCHAR_TO_MID(MID_BCMUAPP_BCMU), TIMER2, BCMUAPP_BCMU_ALM_RESTORE_PERIOD) != SUCCESS) {
        return FAILURE;
    }
    if (set_timer(PCHAR_TO_MID(MID_BCMUAPP_BCMU), TIMER4, BCMUAPP_BCMU_OUTLIERS_TIMER) != SUCCESS) {
        return FAILURE;
    }
    if (set_timer(PCHAR_TO_MID(MID_BCMUAPP_BCMU), TIMER5, BCMUAPP_BCMU_DELAY_TIMER) != SUCCESS) {
        return FAILURE;
    }
    
    return SUCCESS;
}

STATIC int set_bcmu_business_period(void){
    kill_timer(PCHAR_TO_MID(MID_BCMUAPP_BCMU), TIMER5);

    if (set_timer(PCHAR_TO_MID(MID_BCMUAPP_BCMU), TIMER1, BCMUAPP_BCMU_TIMER) != SUCCESS) {
        return FAILURE;
    }
    return SUCCESS;
}

STATIC int judge_sys_time_valid(void)
{
    struct tm stm;
    time_t time_int;
    history_event_t hisevent;
    time_int = time(NULL);
    localtime_r(&time_int, &stm);

    if (!check_common_time(&stm)) {
        system_s("date -s \"2025-01-01 00:00:00\"");
        plat_memset_s(&hisevent, sizeof(history_event_t), 0, sizeof(history_event_t));
        hisevent.type = HISTORY_EVENT_TYPE_OPERATE;
        write_event_content(&hisevent.content_info, "init system time", "2025-01-01 00:00:00",
                        NULL, NULL, NULL, NULL, NULL);
        set_history_event_2_db(&hisevent);
        send_asy_msg(NORTH_POS_CLOCK_SYNC_MSG, NULL, 0, PCHAR_TO_MID(MID_BCMUAPP_BCMU), PCHAR_TO_MID(MID_NORTHPRO_MGR));
        return FAILURE;
    }
    return SUCCESS;
}



STATIC int load_bcmu_data(void)
{
    load_bcmu_ana_data();
    load_bcmu_dig_data();
    load_bcmu_alm_data();
    load_sox_data();
    return SUCCESS;
}


STATIC int deal_bcmu_business(void)
{
    load_bcmu_data();
    update_bcmu_state();
    update_bcmu_discharge_max_current();
    do_replace_finished_judge();
    check_serial_number_changes(); // 检查备件替换并更新同步列表和标志
    calculate_sox();
    calc_max_charge_current();
    update_power_data();
    return SUCCESS;
}

int bcmuapp_bcmu_main(msgid msg_id, msgsn msg_sn, const void *msg, unsigned int msg_len, const mid sender)
{
    switch (msg_id) {
        case TIMER1_MSG:
            deal_bcmu_business();
            break;
        case TIMER2_MSG:
            restore_bcmu_no_backlash_alm();//恢复非回差恢复的过流类告警
            break;
        case TIMER3_MSG:
            send_asy_msg(ALM_TIMEOUT_RESTORE_MSG, NULL, 0,
                     PCHAR_TO_MID(MID_ANONYMOUS), PCHAR_TO_MID(MID_PRODUCTAPP_BCMU));
            break;
        case TIMER4_MSG:
            update_cell_outliers_status();
            break;
        case TIMER5_MSG:
            set_bcmu_business_period();
            break;
        case BCMU_CHECK_SYS_TIME:
            judge_sys_time_valid();
            break;
        case BCMU_SUCC_GET_SOH_DATA_MSG: // 处理从BMU成功获取SOH基准数据的消息
            handle_bmu_soh_data_ready(msg);
            break;
        default:
            break; 
    }
    return SUCCESS;
}

STATIC float update_charging_rate(float current_charge_rate) {
    int cell_max_voltage = 0;
    float charge_rate = current_charge_rate;
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_CELL_VOLTAGE_MAX, &cell_max_voltage, sizeof(int));

    if (cell_max_voltage >= 3500) {
        if (fabs(current_charge_rate - 0.5) < EPSILON) {
            charge_rate = 0.2;
        } else if (fabs(current_charge_rate - 0.2) < EPSILON || fabs(current_charge_rate - 0.25) < EPSILON) {
            charge_rate = 0.1;
        } else if (fabs(current_charge_rate - 0.1) < EPSILON) {
            charge_rate = 0.05;
        }else{
            charge_rate = current_charge_rate;
        }
    }
    return charge_rate;
} 


STATIC int is_valid(float value) {
    return fabs(CHARGE_RATE_INVALID - value) > EPSILON;
}

/* Started by AICoder, pid:47a36g9272l1f9914e5c0ac010fb50204f59fc0f */
STATIC float get_charge_rate_for_temp_and_soc(int i, float SOC) {
    int j;
    for (j = 0; j < NUM_SOC_RANGES; j++) {
        if (SOC >= temp_soc_map[i][j].SOCMin && SOC < temp_soc_map[i][j].SOCMax) {
            return temp_soc_map[i][j].ChargeRateC;
        }
    }
    if (fabs(SOC - 100.0f) < EPSILON) {
        return temp_soc_map[i][NUM_SOC_RANGES - 1].ChargeRateC;
    }
    return CHARGE_RATE_INVALID;
}

STATIC float calc_charging_rate(float Temp, float SOC) {
    float charging_rate = CHARGE_RATE_INVALID;
    int i;

    for (i = 0; i < NUM_TEMP_RANGES; i++) {
        if (Temp >= temp_soc_map[i][0].Tmin && Temp < temp_soc_map[i][0].Tmax) {
            if (fabs(Temp - 60.0f) < EPSILON && i >= 1) {
                charging_rate = get_charge_rate_for_temp_and_soc(i - 1, SOC);
            } else {
                charging_rate = get_charge_rate_for_temp_and_soc(i, SOC);
            }
            break;
        }
    }
    return charging_rate;
}
/* Ended by AICoder, pid:47a36g9272l1f9914e5c0ac010fb50204f59fc0f */

STATIC float calc_charging_rate_by_temp_and_soc(int dev_sn, float Tmin, float Tmax, float DEFAULT_CHARGING_RATE){
    float charging_rate_min = CHARGE_RATE_INVALID;
    float charging_rate_max = CHARGE_RATE_INVALID;
    sid mod_soc_sid = 0ll;
    float mod_soc = 0;
    int ret;

    mod_soc_sid = SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOC, 1, dev_sn);
    ret = pdt_get_data(mod_soc_sid, &mod_soc, sizeof(mod_soc));

    if (ret != SUCCESS || (fabs(Tmax - FLT_MIN) < EPSILON && fabs(Tmin - FLT_MAX) < EPSILON))
    {
        pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &DEFAULT_CHARGING_RATE, sizeof(DEFAULT_CHARGING_RATE), type_float);
        return FAILURE;
    }
    charging_rate_min = calc_charging_rate(Tmin, mod_soc);
    charging_rate_max = calc_charging_rate(Tmax, mod_soc);
    //补充判断是否为有效值
    return MIN(is_valid(charging_rate_min) ? charging_rate_min : CHARGE_RATE_INVALID,
                        is_valid(charging_rate_max) ? charging_rate_max : CHARGE_RATE_INVALID);
}

STATIC int exe_opt_para_asy_cmd(sid sig_id)
{
    cmd_exe_req_msg_t ctrl_msg;
    devices_e dev_type;

    /* 入参检查 */
    plat_memset_s(&ctrl_msg, sizeof(cmd_exe_req_msg_t), 0x00, sizeof(cmd_exe_req_msg_t));
    ctrl_msg.sig_id = sig_id;
    plat_strcpy_s(STR_TO_PCHAR(ctrl_msg.cmd_id), sizeof(str32), "set para", plat_strlen_s("set para", STR_LEN_32));
    dev_type = SID_GET_DEV_TYPE(sig_id);
    if (dev_type != device_optimizer)
        return FAILURE;

    send_asy_msg(SPS_CMD_EXE_REQ_MSG,
                       &ctrl_msg,
                       (unsigned int)sizeof(ctrl_msg),
                       PCHAR_TO_MID(MID_ANONYMOUS),
                       PCHAR_TO_MID(MID_CAND_COMM));
    return SUCCESS;
}

STATIC float optimizer_on_update_charge_rate(int dev_sn, float charging_rate_tmp){
    sid sid_temp = 0ll;
    sid para_sid = 0ll;
    float opt_output_current = 0.0f;
    int ret;
    float diff_curr = 0.0f;

    sid_temp = SID_SET_DEV_SN_SIG_VINDEX(SID_OPTIMIZER_ANA_OPTIMIZER_OUTPUT_CURRENT, dev_sn, 1);
    ret = pdt_get_data(sid_temp, &opt_output_current, sizeof(opt_output_current));
    //直接通过数据是否获取得到来判断优化器是否开启
    if(ret == SUCCESS){
        diff_curr = charging_rate_tmp * DEFAULT_CAPACITY - opt_output_current;
        if(diff_curr > CONST_PCS_MAX_CURRENT){
            return (charging_rate_tmp - opt_output_current / DEFAULT_CAPACITY);
        }else{
            opt_output_current = charging_rate_tmp * DEFAULT_CAPACITY -CONST_PCS_MAX_CURRENT;
            para_sid = SID_SET_DEV_SN(dev_sn, SID_OPTIMIZER_PARA_OPTIMIZER_OUTPUT_SET_CURRENT);
            pdt_set_data(para_sid, &opt_output_current, sizeof(opt_output_current), type_float);
            exe_opt_para_asy_cmd(para_sid);
            return CONST_PCS_MAX_CURRENT / DEFAULT_CAPACITY;
        }
    }
    return charging_rate_tmp;
}

STATIC int step_constant_power_charging(float charging_rate, float charging_current_pcs){
    STATIC float s_init_charging_rate = CHARGE_RATE_INVALID;
    STATIC float s_updated_charging_rate = CHARGE_RATE_INVALID;
    float charging_max_current = CHARGE_RATE_INVALID;

    if (!is_valid(s_init_charging_rate)) {
        s_init_charging_rate = charging_rate;
    }

    if (is_valid(s_updated_charging_rate) && fabs(s_updated_charging_rate - charging_rate) > EPSILON && fabs(s_init_charging_rate - charging_rate) < EPSILON) {
        s_updated_charging_rate = fmin(s_updated_charging_rate, charging_rate);
    } else {
        s_init_charging_rate = charging_rate;
        s_updated_charging_rate = charging_rate;
    }

    s_updated_charging_rate = update_charging_rate(s_updated_charging_rate);


    if(is_valid(charging_current_pcs) &&  charging_current_pcs < s_updated_charging_rate){
        s_updated_charging_rate = charging_current_pcs;
    }

    charging_max_current = s_updated_charging_rate * DEFAULT_CAPACITY;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &charging_max_current, sizeof(charging_max_current), type_float);
    return SUCCESS;
}

// 获取最大充电电流
STATIC int calc_max_charge_current(void) {
    int ret;
    int charge_map_enabled = 1;
    float Tmin = FLT_MAX;
    float Tmax = FLT_MIN;
    float charging_rate = CHARGE_RATE_INVALID;
    float charging_rate_tmp = CHARGE_RATE_INVALID;
    float charging_current_pcs = CHARGE_RATE_INVALID;
    float charging_max_current = CHARGE_RATE_INVALID;
    int i = 0, j = 0;
    sid sid_bmu_state = 0ll;
    sid sid_cell_tem = 0ll;
    float cell_tem = 0.0;
    int cell_tem_ret;
    int charging_rate_min_index;
    STATIC float s_init_charging_rate = CHARGE_RATE_INVALID;
    STATIC float s_updated_charging_rate = CHARGE_RATE_INVALID;
    float DEFAULT_CHARGING_RATE = 0.5f * DEFAULT_CAPACITY; // 默认充电率

    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CHARGE_MAP_ENABLE, &charge_map_enabled, type_int);
    if (!charge_map_enabled) {
        pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &DEFAULT_CHARGING_RATE, sizeof(DEFAULT_CHARGING_RATE), type_float);
        return SUCCESS;
    }

    for (i = 0; i < BMU_NUM; i++)
    {
        if (is_bmu_comm_normal(i + 1) == FAULT)
        {
            continue;
        }
        for(j = 0; j < BMU_CELL_TEMP_NUM; j++)
        {
            sid_cell_tem = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CELL_TEMPERATURE, i + 1, j + 1);
            cell_tem_ret = pdt_get_data(sid_cell_tem, &cell_tem, sizeof(cell_tem));
            if (cell_tem_ret != SUCCESS)
            {
                continue;
            }
            Tmax = MAX(Tmax, cell_tem);
            Tmin = MIN(Tmin, cell_tem);
        }
        charging_rate_tmp = calc_charging_rate_by_temp_and_soc(i + 1, Tmin, Tmax, DEFAULT_CHARGING_RATE);
        if (!is_valid(charging_rate_tmp))
        {
            continue;
        }
        charging_current_pcs = optimizer_on_update_charge_rate(i + 1, charging_rate_tmp);
        if(!is_valid(charging_rate) || charging_rate_tmp < charging_rate) // 相差小于1e-6
        {
            charging_rate = charging_rate_tmp;
        }
    }

    if(!is_valid(charging_rate)){
        pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &DEFAULT_CHARGING_RATE, sizeof(DEFAULT_CHARGING_RATE), type_float);
        return FAILURE;
    }
    return step_constant_power_charging(charging_rate, charging_current_pcs);
}

STATIC int calc_cell_outliers_status(void) {
    int ret = SUCCESS;
    int plan_config = STATISTICAL_OUTLIER_CALCULATION;

    if (!should_calculate_outliers()) {
        return FAILURE;
    }

    ret = load_bmu_cell_volt(s_cell_volt);
    RETURN_VAL_IF_FAIL(ret == SUCCESS, FAILURE);

    // 读取配置
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_PLAN_CONFIG, &plan_config, sizeof(int));
    if (TABLE_LOOKUP_OUTLIER_CALCULATION == plan_config)
    {
        ret = judge_cell_outliers2(s_cell_volt);
        RETURN_VAL_IF_FAIL(ret == SUCCESS, FAILURE);
    }
    else
    {
        ret = judge_cell_outliers1(s_cell_volt);
        RETURN_VAL_IF_FAIL(ret == SUCCESS, FAILURE);
    }
    return ret;
}

/*
**更新离群信息
*/
STATIC int update_cell_outliers_status(void) {
    int ret = SUCCESS;

    ret = calc_cell_outliers_status();
    
    if (ret != SUCCESS) {
        s_outlier_count = 0;
        plat_memset_s(s_outliers,sizeof(s_outliers), 0, sizeof(s_outliers));
    }

    save_outliers_info();

    return ret;
}




/*计算电芯离群前置条件：
**1.所有BMU通讯正常
**2.簇SOC处于非平台期
**3.没有配置优化器或配置了未开启
*/

STATIC int should_calculate_outliers(void) {
    int ret = SUCCESS;
    int cluster_soc = 0;
    float soc_nonplat_high_thre = 0.0;
    float soc_nonplat_low_thre = 0.0;
    int opt_run_state = 0;
    int opt_exit_state = 0;
    int opt_comm_state = 0;
    sid sid_temp = 0;
    int i = 0;


    for (i = 0; i < BMU_NUM; ++i)
    {
        if (is_bmu_exist(i + 1) == FAULT || is_bmu_comm_normal(i + 1) == FAULT) {
            return FALSE;
        }
    }

    //if (if_config_opt()) {  //暂时未看到配置优化器变量的赋值
        for (i = 0; i < OPT_NUM; ++i)
        {
            sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_EXIST_STATE);
            ret = pdt_get_data(sid_temp, &opt_exit_state, sizeof(opt_exit_state));
            //不在位直接跳过
            if (ret != SUCCESS || opt_exit_state == 0) {
                continue;
            }

            sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_COMMUNICATION_STATUS);
            ret = pdt_get_data(sid_temp, &opt_comm_state, sizeof(opt_comm_state));

            //通讯失败直接跳过
            if (ret != SUCCESS || opt_comm_state == 1) {
                continue;
            }
            
            sid_temp = SID_SET_DEV_SN(i+1, SID_OPTIMIZER_DIG_OPTIMIZER_RUN_STATUS);
            ret = pdt_get_data(sid_temp, &opt_run_state, sizeof(opt_run_state));
            //开启优化器
            if (ret == SUCCESS && opt_run_state == 1) {
                return FALSE;
            }
        //}    
    }

    ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &cluster_soc, sizeof(cluster_soc));
    ret |= pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_HIGH_THRESHOLD, &soc_nonplat_high_thre, sizeof(soc_nonplat_high_thre));
    ret |= pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_LOW_THRESHOLD, &soc_nonplat_low_thre, sizeof(soc_nonplat_low_thre));

    if (ret != SUCCESS || (cluster_soc < soc_nonplat_high_thre && cluster_soc > soc_nonplat_low_thre)) {
        return FALSE;
    }

    return TRUE;
}

/*计算电芯离群方案2前置补充条件：
**1.满足准静态条件下：充放电流为0持续30分钟以上
**2.且电芯平均温度大于阈值（缺省：10℃）
*/
/* Started by AICoder, pid:c0c2bu859cs9a8414ca50919c0befd22deb3c154 */
STATIC int should_calculate_outliers2(void)
{
    int bcmu_battery_status = 0;
    float avg_temp = 0.0f;
    float outlier_avg_temp_thre = 10.0f;
    static int keep_count = 0;

    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(bcmu_battery_status));
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BCMU_CELL_AVERAGE_TEMPERATURE, &avg_temp, sizeof(avg_temp));
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_AVERAGE_TEMPERATURE_THRESHOLD, &outlier_avg_temp_thre, sizeof(outlier_avg_temp_thre));

    if ((BCMU_BAT_STATUS_STANDY == bcmu_battery_status) &&
        (avg_temp > outlier_avg_temp_thre))
    {
        if (keep_count <= BCMUAPP_OUTLIERS_STANDY_KEEP_COUNT)
        {
            ++keep_count;
        }

        if (keep_count > BCMUAPP_OUTLIERS_STANDY_KEEP_COUNT)
        {
            return TRUE;
        }
    }
    else
    {
        keep_count = 0;
    }

    return FALSE;
}
/* Ended by AICoder, pid:c0c2bu859cs9a8414ca50919c0befd22deb3c154 */

STATIC int load_bmu_cell_volt(float (*voltages)[BMU_CELL_NUM])
{
    int ret = SUCCESS;
    int i = 0;
    int j = 0;

    for(i = 0; i < BMU_NUM; i++) {
        for (j = 0; j < BMU_CELL_NUM; j++) {
            ret |= pdt_get_data(SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE,i+1,j+1),&voltages[i][j],sizeof(float));
        }
    }

    return ret;
}

/* Started by AICoder, pid:e69ceef28aw57d414c4d094130f36107fc84dbe2 */
STATIC int judge_cell_outliers1(float (*voltages)[BMU_CELL_NUM]) {
    float valid_mean = 0.0;
    float std_dev = 0.0;
    float z_scores[TOTAL_CELLS];
    int valid_cells[TOTAL_CELLS];
    int ret = SUCCESS;
    int i = 0;
    float mean = 0.0;

    for (i = 0; i < TOTAL_CELLS; ++i) {
        valid_cells[i] = i;
    }

    mean = calculate_mean(voltages, valid_cells);
    
    //移除压差前10的电芯
    remove_extreme_voltages(voltages, valid_cells, mean, 10);
    
    // Calculate mean using only valid cells
    valid_mean = calculate_mean(voltages, valid_cells);

    // Calculate standard deviation using only valid cells
    std_dev = calculate_std_dev(voltages, valid_cells, valid_mean);

    // Calculate Z-scores 
    ret = calculate_z_scores(voltages, valid_mean, std_dev, z_scores);
    RETURN_VAL_IF_FAIL(ret == SUCCESS, ret);

    // Update outliers based on Z-scores
    ret = update_outliers(voltages, mean, z_scores, s_outlier_history, s_outliers, &s_outlier_count); // Default values for periods and thresholds

    return ret;
}

/* Started by AICoder, pid:zff4eec7f395a5b145c2092e409f4e375e8476b4 */
STATIC int judge_cell_outliers2(float (*voltages)[BMU_CELL_NUM])
{
    int i = 0;
    int j = 0;
    int index = 0;
    int count = 0;
    float soc_dev_thre = 10.0f;
    int sorted_indices[TOTAL_CELLS] = {0};

    RETURN_VAL_IF_FAIL(voltages != NULL, FAILURE);
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_DEVIATION_THRESHOLD, &soc_dev_thre, sizeof(soc_dev_thre));

    if (!should_calculate_outliers2()) {
        return FAILURE;
    }

    calculate_cell_soc_volt_dev(voltages);
    sort_soc_dev_fabs(sorted_indices);

    for (i = 0; i < MAX_OUTLIERS; i++) {
        index = sorted_indices[i];
        if (s_cell_soc_dev_fabs[index] > soc_dev_thre) {
            s_outliers[i].pack = index / BMU_CELL_NUM + 1;
            s_outliers[i].cell = index % BMU_CELL_NUM + 1;
            s_outliers[i].volt_diff = s_cell_volt_dev[index] * 1000;
            s_outliers[i].z_score = 0;
            count++;
        }
        else
        {
            s_outliers[i].pack = 0;
            s_outliers[i].cell = 0;
            s_outliers[i].volt_diff = 0;
            s_outliers[i].z_score = 0;
        }
    }
    s_outlier_count = count;

    return SUCCESS;
}
/* Ended by AICoder, pid:zff4eec7f395a5b145c2092e409f4e375e8476b4 */

/**
 * @brief 对s_cell_soc_dev_fabs数组中的值按从大到小排序
 * @param sorted_indices 排序后的索引数组，大小为TOTAL_CELLS
 * @return 成功返回SUCCESS，失败返回FAILURE
 */
/* Started by AICoder, pid:s7c88d65e9sa4d01486709c580a66c1bbd05eef5 */
STATIC int sort_soc_dev_fabs(int *sorted_indices)
{
    int i = 0;
    int j = 0;
    int temp = 0;

    // 参数检查
    RETURN_VAL_IF_FAIL(sorted_indices != NULL, FAILURE);

    // 初始化索引数组
    for (i = 0; i < TOTAL_CELLS; i++) {
        sorted_indices[i] = i;
    }

    // 使用冒泡排序对索引进行排序，按照s_cell_soc_dev_fabs值从大到小排序
    for (i = 0; i < TOTAL_CELLS - 1; i++) {
        for (j = 0; j < TOTAL_CELLS - i - 1; j++) {
            if (s_cell_soc_dev_fabs[sorted_indices[j]] < s_cell_soc_dev_fabs[sorted_indices[j + 1]]) {
                // 交换索引
                temp = sorted_indices[j];
                sorted_indices[j] = sorted_indices[j + 1];
                sorted_indices[j + 1] = temp;
            }
        }
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:s7c88d65e9sa4d01486709c580a66c1bbd05eef5 */

/* Started by AICoder, pid:ra15dsad4e3b79e14cdd0b4ff03cbc358b83e203 */
STATIC int calculate_cell_soc_volt_dev(float (*voltages)[BMU_CELL_NUM])
{
    float soc_sum = 0.0f;
    float volt_sum = 0.0f;
    int i = 0;
    int j = 0;
    float avg_temp = 0.0f;

    RETURN_VAL_IF_FAIL(voltages != NULL, FAILURE);
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BCMU_CELL_AVERAGE_TEMPERATURE, &avg_temp, sizeof(avg_temp));

    for (i = 0; i < BMU_NUM; i++)
    {
        for (j = 0; j < BMU_CELL_NUM; j++)
        {
            ocv_area_t result = {0};
            get_soc_from_ocv_table(avg_temp, voltages[i][j], &result);
            s_cell_soc[i][j] = result.soc;
            soc_sum += result.soc;
            volt_sum += voltages[i][j];
        }
    }
    s_cell_avg_soc = soc_sum / TOTAL_CELLS;
    s_cell_avg_volt = volt_sum / TOTAL_CELLS;

    // 计算每个电池单元的SOC与平均SOC的绝对差值
    for (i = 0; i < BMU_NUM; i++)
    {
        for (j = 0; j < BMU_CELL_NUM; j++)
        {
            s_cell_soc_dev_fabs[i * BMU_CELL_NUM + j] = fabs(s_cell_soc[i][j] - s_cell_avg_soc);
            s_cell_volt_dev[i * BMU_CELL_NUM + j] = voltages[i][j] - s_cell_avg_volt;
        }
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:ra15dsad4e3b79e14cdd0b4ff03cbc358b83e203 */

STATIC int remove_extreme_voltages(float (*voltages)[BMU_CELL_NUM], int *valid_cells, float mean, int remove_count) {
    int i = 0;
    int j = 0;
    int temp = 0;
    int pack = 0;
    int cell = 0;
    // Find the indices of the extreme voltages based on the difference from the mean
    float diffs[TOTAL_CELLS];
    for (i = 0; i < TOTAL_CELLS; ++i) {
        pack = i / BMU_CELL_NUM;
        cell = i % BMU_CELL_NUM;
        diffs[i] = fabs(voltages[pack][cell] - mean);
    }
    // Sort the differences and find the indices of the largest differences
    int sorted_indices[TOTAL_CELLS];
    for (i = 0; i < TOTAL_CELLS; ++i) {
        sorted_indices[i] = i;
    }
    for (i = 0; i < TOTAL_CELLS - 1; ++i) {
        for (j = 0; j < TOTAL_CELLS - i - 1; ++j) {
            if (diffs[sorted_indices[j]] < diffs[sorted_indices[j + 1]]) {
                temp = sorted_indices[j];
                sorted_indices[j] = sorted_indices[j + 1];
                sorted_indices[j + 1] = temp;
            }
        }
    }

    // Mark the cells with the largest differences as invalid
    for (i = 0; i < remove_count; ++i) {
        valid_cells[sorted_indices[i]] = -1;
    }

    return SUCCESS;
}

STATIC float calculate_mean(float (*voltages)[BMU_CELL_NUM], int *valid_cells) {
    float sum = 0;
    int count = 0;
    int pack = 0;
    int cell = 0;
    int i = 0;
    for (i = 0; i < TOTAL_CELLS; ++i) {
        if (valid_cells[i] != -1) {
            pack = i / BMU_CELL_NUM;
            cell = i % BMU_CELL_NUM;
            sum += voltages[pack][cell];
            count++;
        }
    }
    if (count == 0) {
        return 0;
    }else {
        return round((sum/count) * 1000.0f)/1000;
    } 
}

STATIC float calculate_std_dev(float (*voltages)[BMU_CELL_NUM], int *valid_cells, float mean) {
    float sum_squared_diff = 0.0;
    int count = 0;
    int i = 0;
    int pack = 0;
    int cell = 0;
    for (i = 0; i < TOTAL_CELLS; ++i) {
        if (valid_cells[i] != -1) {
            pack = i / BMU_CELL_NUM;
            cell = i % BMU_CELL_NUM;
            sum_squared_diff += pow(voltages[pack][cell] - mean, 2);
            count++;
        }
    }
    if (count > 1) {
        return sqrt(sum_squared_diff / (count - 1));
    }else {
        return 0;
    } 
}

STATIC int calculate_z_scores(float (*voltages)[BMU_CELL_NUM], float mean, float std_dev, float *z_scores) {
    int i = 0;
    int pack = 0;
    int cell = 0;

    if(if_float_equal(std_dev, 0.0)) {
        return FAILURE;
    }

    for (i = 0; i < TOTAL_CELLS; ++i) {
        pack = i / BMU_CELL_NUM;
        cell = i % BMU_CELL_NUM;
        z_scores[i] = (voltages[pack][cell] - mean) / std_dev;
    }
    return SUCCESS;
}
/* Ended by AICoder, pid:369ce8f28ae57d414c4d094131f36127fc86dbe2 */

STATIC int update_outliners_info(float (*voltages)[BMU_CELL_NUM], float mean, float *z_scores, outlier_info_t *outliers, int outlier_count) {
    int i = 0;
    int pack = 0;
    int cell = 0;
    int total_cell_index = 0;
    for(i = 0; i < outlier_count; i++) {
        pack = outliers[i].pack - 1;
        cell = outliers[i].cell - 1;
        total_cell_index = pack * BMU_CELL_NUM + cell;

        RETURN_VAL_IF_FAIL(total_cell_index >= 0 && pack >= 0 && cell >= 0, FAILURE);

        outliers[i].z_score = z_scores[total_cell_index];
        outliers[i].volt_diff = (int)round((voltages[pack][cell] - mean)*1000.0f);
    }
    return SUCCESS;
}

/* Started by AICoder, pid:x0a32s6123xace3143650bee40e37154baa1a911 */
STATIC int update_outliers(float (*voltages)[BMU_CELL_NUM], float mean, float *z_scores, cell_history_t *outlier_history, outlier_info_t *outliers, int *outlier_count) {
    int min_idx = 0;
    int i = 0;
    int j = 0;
    int k = 0;
    float threshold = 0;
    int period = 0;
    float clear_threshold = 0;
    int clear_period = 0;
    int ret = SUCCESS;
    int pack = 0;
    int cell = 0;

    ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SET_THRESHOLD, &threshold, sizeof(threshold));
    ret |= pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SET_CONFIRMATION_PERIOD, &period, sizeof(period));
    ret |= pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_RECOVERY_THRESHOLD, &clear_threshold, sizeof(clear_threshold));
    ret |= pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_RECOVERY_CONFIRMATION_PERIOD, &clear_period, sizeof(clear_period));

    RETURN_VAL_IF_FAIL(ret == SUCCESS, FAILURE);

    update_outliners_info(voltages, mean, z_scores, outliers, *outlier_count);

    for (i = 0; i < TOTAL_CELLS; ++i) {
        if (fabs(z_scores[i]) > threshold && outlier_history[i].state == NOT_OUTLINER) {
            outlier_history[i].count++;
            if (outlier_history[i].count >= period) {
                // Add to outliers list
                if (*outlier_count < MAX_OUTLIERS) {
                    pack = i / BMU_CELL_NUM;
                    cell = i % BMU_CELL_NUM;
                    outliers[*outlier_count].pack = pack + 1;
                    outliers[*outlier_count].cell = cell + 1;
                    outliers[*outlier_count].z_score = z_scores[i];
                    outliers[*outlier_count].volt_diff = (int)round((voltages[pack][cell] - mean)*1000.0f);
                    (*outlier_count)++;
                } else {
                    // Replace the smallest Z-score outlier
                    min_idx = 0;
                    for (j = 1; j < MAX_OUTLIERS; ++j) {
                        if (fabs(outliers[j].z_score) < fabs(outliers[min_idx].z_score)) {
                            min_idx = j;
                        }
                    }
                    pack = i / BMU_CELL_NUM;
                    cell = i % BMU_CELL_NUM;
                    outliers[min_idx].pack = pack + 1;
                    outliers[min_idx].cell = cell + 1;
                    outliers[min_idx].z_score = z_scores[i];
                    outliers[min_idx].volt_diff = (int)round((voltages[pack][cell] - mean)*1000.0f);
                }
                outlier_history[i].state = OUTLINER; // Mark as outlier
                outlier_history[i].count = 0;
            }
        } else if (fabs(z_scores[i]) < clear_threshold && outlier_history[i].state == OUTLINER) {
            outlier_history[i].count++;
            if (outlier_history[i].count >= clear_period) {
                // Clear from outliers list
                for (j = 0; j < *outlier_count; ++j) {
                    if (outliers[j].pack == i / BMU_CELL_NUM + 1 && outliers[j].cell == i % BMU_CELL_NUM + 1) {
                        for (k = j; k < *outlier_count - 1; ++k) {
                            outliers[k] = outliers[k + 1];
                        }
                        plat_memset_s(&outliers[*outlier_count-1], sizeof(outlier_info_t), 0, sizeof(outlier_info_t));
                        (*outlier_count)--;
                        break;
                    }
                }
                outlier_history[i].state = NOT_OUTLINER; // Mark as not outlier
                outlier_history[i].count = 0;
            }
        } else {
            outlier_history[i].count = 0; // Reset counter
        }
    }
    return SUCCESS;
}
/* Ended by AICoder, pid:x0a32s6123xace3143650bee40e37154baa1a911 */

/* Started by AICoder, pid:wab8br99bb68d51149f40a1de007cd25eaa5e7c9 */
STATIC int save_outliers_info(void) {
    int i = 0;
    const int pack_num = 0;
    const int cell_num = 0;
    const int volt_diff = 0;
    const float z_score = 0.0f;
    sid sid_id_set = 0ll;

    for (i = 0; i < MAX_OUTLIERS; i++) {
        if (i < s_outlier_count) {
            sid_id_set = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_CELL_VOLTAGE_OUTLIER_PACK_NUMBER, 1, i+1);
            set_data_valid(sid_id_set, TYPE_VALUE);
            pdt_set_data(sid_id_set, &s_outliers[i].pack, sizeof(s_outliers[i].pack), type_int);

            sid_id_set = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_CELL_VOLTAGE_OUTLIER_CELL_NUMBER, 1, i+1);
            set_data_valid(sid_id_set, TYPE_VALUE);
            pdt_set_data(sid_id_set, &s_outliers[i].cell, sizeof(s_outliers[i].cell), type_int);

            sid_id_set = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_CELL_VOLTAGE_OUTLIER_DEVIATION_VALUE, 1, i+1);
            set_data_valid(sid_id_set, TYPE_VALUE);
            pdt_set_data(sid_id_set, &s_outliers[i].volt_diff, sizeof(s_outliers[i].volt_diff), type_int);
        }
        else
        {
            sid_id_set = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_CELL_VOLTAGE_OUTLIER_PACK_NUMBER, 1, i+1);
            pdt_set_data(sid_id_set, &pack_num, sizeof(pack_num), type_int);
            set_data_invalid(sid_id_set, TYPE_VALUE);

            sid_id_set = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_CELL_VOLTAGE_OUTLIER_CELL_NUMBER, 1, i+1);
            pdt_set_data(sid_id_set, &cell_num, sizeof(cell_num), type_int);
            set_data_invalid(sid_id_set, TYPE_VALUE);

            sid_id_set = PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_CELL_VOLTAGE_OUTLIER_DEVIATION_VALUE, 1, i+1);
            pdt_set_data(sid_id_set, &volt_diff, sizeof(volt_diff), type_int);
            set_data_invalid(sid_id_set, TYPE_VALUE);
        }
    }

    if ((s_outlier_count > 0) && (TRUE == is_cell_volt_outlier_pos_change())) {
        send_asy_msg(BCMU_CELL_VOLT_OUTLIER_REC_MSG, NULL, 0, PCHAR_TO_MID(MID_BCMUAPP_BCMU), PCHAR_TO_MID(MID_PRODUCTAPP_SYSTEM));
        plat_memcpy_s(&s_last_outliers, sizeof(s_last_outliers), &s_outliers, sizeof(s_outliers));
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:wab8br99bb68d51149f40a1de007cd25eaa5e7c9 */

/* Started by AICoder, pid:c6c53s1bc3f10bc140530956a066e80125c9a8db */
STATIC int is_cell_volt_outlier_pos_change(void) {
    int i = 0;
    for (i = 0; i < MAX_OUTLIERS; i++) {
        if (s_outliers[i].pack != s_last_outliers[i].pack ||
            s_outliers[i].cell != s_last_outliers[i].cell) {
                return TRUE;
        }
    }
    return FALSE;
}
/* Ended by AICoder, pid:c6c53s1bc3f10bc140530956a066e80125c9a8db */


/* Started by AICoder, pid:j7fbcgd497s783814443084cd08c854aa052cd6a */
// 模拟获取当前充电最大电流和电池直流侧电压
// 计算最大充电功率并进行平滑处理
STATIC float calculate_max_power(enum OperationType op_type) {
    float max_current;
    float dc_voltage;
    float pcs_max_power;
    int ret = SUCCESS;

    // 根据操作类型获取不同的数据
    if (op_type == CHARGE) {
        ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_CURRENT, &max_current, type_float);
    } else{
        ret = pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_DISCHARGE_MAX_CURRENT, &max_current, type_float);
    }
    ret |= pdt_get_data(SID_POWER_CONVERSION_SYSTEM_ANA_PCS_DC_VOLTAGE, &dc_voltage, type_float);
    ret |= pdt_get_data(SID_POWER_CONVERSION_SYSTEM_PARA_PCS_ACTIVE_POWER_MAX, &pcs_max_power, type_float);
    RETURN_VAL_IF_FAIL(ret == SUCCESS, 0.0f);

    float battery_max_power = max_current * dc_voltage / 1000.0f;  // kW
    // 取较小值
    float max_power = fmin(battery_max_power, pcs_max_power);

    // 平滑处理（这里使用指数平滑）
    STATIC float prev_max_charge_power = 0.0f;
    STATIC float prev_max_discharge_power = 0.0f;
    const float alpha = 0.5f;  // 平滑因子

    if (op_type == CHARGE) {
        max_power = alpha * max_power + (1.0f - alpha) * prev_max_charge_power;
        prev_max_charge_power = max_power;
    } else{
        max_power = alpha * max_power + (1.0f - alpha) * prev_max_discharge_power;
        prev_max_discharge_power = max_power;
    }

    // 设置精度
    return roundf(max_power * powf(10, MAX_POWER_PRECISION)) / powf(10, MAX_POWER_PRECISION);
}
/* Ended by AICoder, pid:j7fbcgd497s783814443084cd08c854aa052cd6a */

/* Started by AICoder, pid:ka17fg84dcz5e59145a0080c210703060781c0e9 */
// 获取储能柜容量
STATIC int get_battery_capacity() {
    return BATTERY_CAPACITY;
}
/* Ended by AICoder, pid:ka17fg84dcz5e59145a0080c210703060781c0e9 */

/* Started by AICoder, pid:u0177aac75d860c142660856b0606f1e1f29dced */

STATIC int update_power_data() {
    /* Started by AICoder, pid:d7fbcod497h783814443084cd08c850aa052cd6a */
    float max_charge_power = calculate_max_power(CHARGE);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_CHARGE_MAX_POWER, &max_charge_power, sizeof(max_charge_power), type_float);

    float max_discharge_power = calculate_max_power(DISCHARGE);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_DISCHARGE_MAX_POWER, &max_discharge_power, sizeof(max_discharge_power), type_float);
    /* Ended by AICoder, pid:d7fbcod497h783814443084cd08c850aa052cd6a */

    // 获取并打印储能柜容量
    int battery_capacity = get_battery_capacity();
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_ENERGY_STORAGE_CABINET_CAPACITY, &battery_capacity, sizeof(battery_capacity), type_int);

    return SUCCESS;
}
/* Ended by AICoder, pid:u0177aac75d860c142660856b0606f1e1f29dced */

STATIC int handle_bmu_soh_data_ready(void *msg)
{
    int bmu_dev_sn = *(int *)msg;
    bmu_soh_data_syned[bmu_dev_sn - 1] = TRUE;
    return SUCCESS;
}

STATIC int do_replace_finished_judge(void)
{
    int i;
    int bmu_index[BMU_NUM] = {0};
    // 如果备件替换正在进行中，则判断SOH数据是否同步上来
    if(get_backup_status() == enum_backup_doing)
    {
        get_bmu_index_pending_backup(bmu_index);
        for(i = 0; i < BMU_NUM; i++)
        {
            // 如果待同步的BMU，对应SOH数据还没有同步上来,
            if((bmu_index[i] != -1) && (bmu_soh_data_syned[i]==FALSE))
            {
                return FAILURE;
            }
        }
        set_soh_sync_finish_flag(TRUE);
    }
    return SUCCESS;
}

/* Started by AICoder, pid:k74d2gad7d8fd5c145b50b5580ac3545942289fa */
STATIC int get_soc_from_ocv_table(float temperature, float volt, ocv_area_t *result) {
    unsigned int temp_index = 0;
    unsigned int temp_match_index = 0;
    unsigned int soc_index = 0;
    float mid_temp = 0;
    float soc_match = 0;
    float soc_low = 0;
    float soc_high = 0;

    if (NULL == result) {
        return FAILURE;
    }

    // 温度区间映射
    if (temperature < s_ocv_temp_map[0]) {
        temp_index = 0;
    } else if (temperature >= s_ocv_temp_map[OCV_TAB_SIZE_TEMP_NUM - 1] - FLOAT_EPSILON) {
        temp_index = OCV_TAB_SIZE_TEMP_NUM - 2;
    } else {
        temp_index = get_point_index_by_median_lookup(&s_ocv_temp_map[0], OCV_TAB_SIZE_TEMP_NUM, temperature);
    }

    // 区间温度中值
    mid_temp = (s_ocv_temp_map[temp_index] + s_ocv_temp_map[temp_index + 1]) / 2;
    if (temperature >= mid_temp - FLOAT_EPSILON) {
        temp_match_index = temp_index + 1;
    } else {
        temp_match_index = temp_index;
    }

    // 电芯电压区间映射
    if (volt < s_ocv_temp_soc_tab[temp_match_index][0]) {
        soc_index = 0;
    } else if (volt >= s_ocv_temp_soc_tab[temp_match_index][OCV_TAB_SIZE_SOC_NUM - 1] - FLOAT_EPSILON) {
        soc_index = OCV_TAB_SIZE_SOC_NUM - 2;
    } else {
        soc_index = get_point_index_by_median_lookup(&s_ocv_temp_soc_tab[temp_match_index][0], OCV_TAB_SIZE_SOC_NUM, volt);
    }

    soc_low = soc_index * SOC_INTERVAL;
    soc_high = (soc_index + 1) * SOC_INTERVAL;
    if (volt <= s_ocv_temp_soc_tab[temp_match_index][soc_index] + FLOAT_EPSILON) {
        soc_match = soc_low;
    } else if (volt >= s_ocv_temp_soc_tab[temp_match_index][soc_index + 1] - FLOAT_EPSILON) {
        soc_match = soc_high;
    } else {
        get_linear_point(s_ocv_temp_soc_tab[temp_match_index][soc_index], soc_low, s_ocv_temp_soc_tab[temp_match_index][soc_index + 1], soc_high, volt, &soc_match);
    }

    // 提取数据
    result->temp_match = s_ocv_temp_map[temp_match_index];
    result->volt_area[0] = s_ocv_temp_soc_tab[temp_match_index][soc_index];
    result->volt_area[1] = s_ocv_temp_soc_tab[temp_match_index][soc_index + 1];

    result->soc_area[0] = soc_low;
    result->soc_area[1] = soc_high;
    result->soc = soc_match;

    return SUCCESS;
}
/* Ended by AICoder, pid:k74d2gad7d8fd5c145b50b5580ac3545942289fa */

/* Started by AICoder, pid:a5da6v63de653b414f9c095b20d15d216ec797c4 */
STATIC unsigned int get_point_index_by_median_lookup(float* array, unsigned int array_size, float value) {
    unsigned int low = 0;
    unsigned int high = array_size;
    unsigned int mid = 0;
    unsigned point_index = 0;

    if (NULL == array || array_size < 1) {
        return point_index;
    }

    high = array_size - 1;

    while (low < high) {
        mid = low + (high - low) / 2;
        if ((value >= array[mid] - FLOAT_EPSILON) && value < array[mid + 1]) {
            point_index = mid;
            break;
        } else if (value < array[mid]) {
            high = mid;
        } else {
            low = mid + 1;
        }
    }

    return point_index;
}
/* Ended by AICoder, pid:a5da6v63de653b414f9c095b20d15d216ec797c4 */
