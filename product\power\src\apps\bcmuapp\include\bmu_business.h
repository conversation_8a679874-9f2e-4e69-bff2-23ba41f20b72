/*****************************************************************************
 文件名      : bmu_business.h
 内容摘要    : BMU业务.h文件
 版本        : V1.0
*****************************************************************************/

#ifndef _DEV_BMU_BUSINESS_H
#define _DEV_BMU_BUSINESS_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

/*  Include   */
#include "data_type.h"
#include "daapi/sids.h"
#include "data_access.h"
#include "MIBTable.h"

/* defines  */
#define BCMUAPP_BMU_TIMER  4000    ///<  bcmuapp bmu 4 seconds
#define BMU_BALANCE_TIMER  120000   ///<  BMU均衡控制计算周期：两分钟

#define NOT_EXIST 0 // 不在位
#define EXIST 1     // 在位

#define NORMAL                  0               ///<    正常状态值
#define ABNORMAL                1               ///<    异常状态值

#define CHG_PRT_POS 0 //充电保护状态
#define CELL_END_VOLT_STA_POS 1 //单体电压高截止状态
#define MOD_END_VOLT_STA_POS 2 //模块电压高截止状态
#define CELL_CHG_TEMP_HIGH_END_STA_POS 3 //单体充电高温截止状态
#define CELL_CHG_TEMP_LOW_END_STA_POS 4 //单体充电低温截止状态
#define CELL_DISCHG_TEMP_HIGH_END_STA_POS 5 //单体放电高温截止状态
#define CELL_DISCHG_TEMP_LOW_END_STA_POS 6 //单体放电低温截止状态
#define BCMU_CHARGE_TEM_EXT_DIF_CUT_OFF_STU_POS 5 //BCMU充电电池温度极差截止状态

#define DISCHG_PRT_POS 0 //放电保护状态
#define MOD_VOLT_LOW_END_STA_POS 1 //模块电压低截止状态
#define BCMU_DISCHARGE_TEM_EXT_DIF_CUT_OFF_STU_POS 2 //BCMU放电电池温度极差截止状态
#define EQUAL_INTERVAL_MAX_NUM 6 // 最大均衡区间数量
#define IMPOSSIBLE_DIFF_VALUE 5000.0f // 不可能的阈值差值5000mv(5V)

#define DEFAULT_CAPACITY 314.0f // 默认容量为314Ah
#define EPSILON       0.00001
#define CHARGE_RATE_INVALID -1.0f
#define CONST_PCS_MAX_CURRENT 5.0f

#define FLT_MAX 125.0f
#define FLT_MIN -40.0f

enum OperationType {
    CHARGE,
    DISCHARGE
};

typedef struct
{
    float_type_t cell_volt[BMU_CELL_NUM]; //单体电压
    float_type_t cell_temperature[BMU_CELL_TEMP_NUM]; //单体温度
    float_type_t module_volt;//电池模块电压
}bmu_ana_data_t;

typedef struct
{
    int_type_t bmu_exist_sta; //BMU在位状态
    int_type_t bmu_comm_sta; //BMU通讯状态
    int_type_t bmu_cell_balance_sta[BMU_CELL_NUM]; //BMU电芯均衡状态
}bmu_dig_data_t;

typedef struct
{
    int_type_t cell_over_volt_lvl1_alm[BMU_CELL_NUM]; //单体过压一级告警
    int_type_t cell_over_volt_lvl2_alm[BMU_CELL_NUM]; //单体过压二级告警
    int_type_t cell_over_volt_lvl3_alm[BMU_CELL_NUM]; //单体过压三级告警
    int_type_t cell_charge_temp_high_cutoff_alm[BMU_CELL_TEMP_NUM]; //单体充电高温截止告警
    int_type_t cell_charge_temp_low_cutoff_alm[BMU_CELL_TEMP_NUM]; //单体充电低温截止告警
    int_type_t cell_discharge_temp_high_cutoff_alm[BMU_CELL_TEMP_NUM]; //单体放电高温截止告警
    int_type_t cell_discharge_temp_low_cutoff_alm[BMU_CELL_TEMP_NUM]; //单体放电低温截止告警
    int_type_t bmu_vol_high_end_alarm; //电池模块电压高截止告警
    int_type_t bmu_vol_low_end_alarm; //电池模块电压低截止告警
    int_type_t cell_volt_high_cutoff_alm[BMU_CELL_NUM]; //单体电压高截止告警
    int_type_t cell_volt_low_cutoff_alm[BMU_CELL_NUM]; //单体电压低截止告警
    int_type_t bmu_cg_vol_diff_end_alarm; //电池模块充电电压极差截止告警
    int_type_t bmu_dischg_vol_diff_end_alarm; //电池模块放电电压极差截止告警
    int_type_t bmu_comm_fail_alm; //BMU通讯断告警
    int_type_t bmu_term_high_alm; //电池模块端子温度高告警
    int_type_t cell_temp_sample_fault[BMU_CELL_TEMP_NUM]; //BMU单体温度采样异常
    int_type_t bcmu_charge_Temperature_dif_alarm_end; //BCMU充电温度极差截止告警
    int_type_t bcmu_discharge_Temperature_dif_alarm_end; //BCMU放电温度极差截止告警
    int_type_t cell_cg_vol_diff_end_alarm; //充电单体电压极差截止告警
    int_type_t cell_dischg_vol_diff_end_alarm; //放电单体电压极差截止告警
}bmu_alm_data_t;

typedef struct
{
    float upper_bound;
    float lower_bound;
    int threshold;
} Balance_Para_Struct;

typedef struct
{
    unsigned char actual_equal_interval_num;
	float equal_interval1_upper_limit;
    float equal_interval1_lower_limit;
    int equal_interval1_thre;
    float equal_interval2_upper_limit;
    float equal_interval2_lower_limit;
    int equal_interval2_thre;
    float equal_interval3_upper_limit;
    float equal_interval3_lower_limit;
    int equal_interval3_thre;
    float equal_interval4_upper_limit;
    float equal_interval4_lower_limit;
    int equal_interval4_thre;
    float equal_interval5_upper_limit;
    float equal_interval5_lower_limit;
    int equal_interval5_thre;
    float equal_interval6_upper_limit;
    float equal_interval6_lower_limit;
    int equal_interval6_thre;
}bmus_para_t;

/*  函数定义   */
int bmu_battery_object_init(void);
int bmu_battery_object_main(msgid msg_id, msgsn msg_sn, const void *msg, unsigned int msg_len, const mid sender);
int is_bmu_comm_normal(int dev_sn);
int is_bmu_exist(int dev_sn);
int get_bmu_alm_data(bmu_alm_data_t* bmu_alm_data);
int if_exist_one_alarm(int_type_t* alm_val, int alarm_num);
#ifdef UNITTEST
    STATIC int deal_bmu_business(void);
    STATIC int load_bmu_ana_data(void);
    STATIC int load_bmu_dig_data(void);
    STATIC int load_bmu_alm_data(void);
    STATIC int load_bmus_para(void);
    STATIC int is_all_optimizer_work_well(void);
    STATIC int load_bmu_data(void);
    STATIC int calc_balance_ctrl_status(void);
    STATIC int bmu_balance_process(void);
    STATIC int start_cell_balance_ctrl(void);
    STATIC int calc_intervals(Balance_Para_Struct *balance_para_t);
    STATIC int check_balance_ctrl_result(void);
    STATIC float get_interval_threshold(float voltage, Balance_Para_Struct balance_para_t[], int num_intervals);
    STATIC float get_min_voltage(float_type_t *cell_volt, int count);
    STATIC int check_balance(float cell_volt, float bmu_min_cell_volt, Balance_Para_Struct balance_para_t[], int num_intervals, int *balance_res);
#endif

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif

