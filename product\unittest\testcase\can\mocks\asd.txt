[WATCHDOG DEBUG] Feed watchdog initialize failed: -1, time: 1578114849
[WATCHDOG DEBUG] feed_watchdog called, count: 1, time: 1578114854
[WATCHDOG DEBUG] initialize_watchdog: current fd=-1, pid=474
[WATCHDOG DEBUG] Current SysMonitor processes:
  474 root      0:00 /root/power/bin/SysMonitor_run
[WATCHDOG DEBUG] /dev/watchdog exists, mode=020660
[WATCHDOG DEBUG] Before opening, checking device usage...
fuser: invalid option -- 'v'
BusyBox v1.36.1 (2025-04-08 13:54:51 HKT) multi-call binary.

Usage: fuser [-msk46] [-SIGNAL] FILE or PORT/PROTO

Find processes which use FILEs or PORTs

        -m      Find processes which use same fs as FILEs
        -4,-6   Search only IPv4/IPv6 space
        -s      Don't display PIDs
        -k      Kill found processes
        -SIGNAL Signal to send (default: KILL)
fuser command failed
1       /bin/busybox    0       /dev/console
1       /bin/busybox    1       /dev/console
1       /bin/busybox    2       /dev/console
98      /bin/busybox    0       /dev/console
98      /bin/busybox    1       /dev/console
98      /bin/busybox    2       /dev/console
98      /bin/busybox    10      /etc/init.d/rcS
174     /bin/busybox    0       socket:[13311]
174     /bin/busybox    1       /dev/null
174     /bin/busybox    2       /dev/null
174     /bin/busybox    3       /tmp/log/message
174     /bin/busybox    4       /tmp/log/auth.log
246     /usr/bin/dbus-daemon    0       /dev/null
246     /usr/bin/dbus-daemon    1       /dev/null
246     /usr/bin/dbus-daemon    2       /dev/null
246     /usr/bin/dbus-daemon    3       anon_inode:[eventpoll]
246     /usr/bin/dbus-daemon    4       anon_inode:inotify
246     /usr/bin/dbus-daemon    5       socket:[27655]
246     /usr/bin/dbus-daemon    6       socket:[1740]
246     /usr/bin/dbus-daemon    7       socket:[1741]
246     /usr/bin/dbus-daemon    8       socket:[32054]
246     /usr/bin/dbus-daemon    9       socket:[29311]
246     /usr/bin/dbus-daemon    10      socket:[36111]
246     /usr/bin/dbus-daemon    11      socket:[36117]
246     /usr/bin/dbus-daemon    12      socket:[32084]
246     /usr/bin/dbus-daemon    13      socket:[32095]
246     /usr/bin/dbus-daemon    14      socket:[29397]
246     /usr/bin/dbus-daemon    15      socket:[32118]
246     /usr/bin/dbus-daemon    16      socket:[32119]
246     /usr/bin/dbus-daemon    17      socket:[32120]
246     /usr/bin/dbus-daemon    18      socket:[36200]
246     /usr/bin/dbus-daemon    19      socket:[30361]
246     /usr/bin/dbus-daemon    20      socket:[36201]
246     /usr/bin/dbus-daemon    21      socket:[36217]
246     /usr/bin/dbus-daemon    22      socket:[30366]
246     /usr/bin/dbus-daemon    23      socket:[30367]
246     /usr/bin/dbus-daemon    24      socket:[30368]
246     /usr/bin/dbus-daemon    25      socket:[30369]
246     /usr/bin/dbus-daemon    26      socket:[32189]
246     /usr/bin/dbus-daemon    27      socket:[36221]
246     /usr/bin/dbus-daemon    28      socket:[36222]
246     /usr/bin/dbus-daemon    29      socket:[32192]
246     /usr/bin/dbus-daemon    30      socket:[37239]
246     /usr/bin/dbus-daemon    31      socket:[37247]
246     /usr/bin/dbus-daemon    32      socket:[37256]
246     /usr/bin/dbus-daemon    33      socket:[30403]
308     /sbin/sshd      0       /dev/null
308     /sbin/sshd      1       /dev/null
308     /sbin/sshd      2       /dev/null
308     /sbin/sshd      3       socket:[28640]
308     /sbin/sshd      4       socket:[28642]
312     /root/power/bin/comdmgr 0       /dev/null
312     /root/power/bin/comdmgr 1       /dev/console
312     /root/power/bin/comdmgr 2       /dev/console
312     /root/power/bin/comdmgr 3       anon_inode:[eventfd]
312     /root/power/bin/comdmgr 4       socket:[36199]
312     /root/power/bin/comdmgr 5       /root/power/etc/shm/value.attr.bin
312     /root/power/bin/comdmgr 6       /mnt/data/para_and_config/para/classify.sqlite
312     /root/power/bin/comdmgr 7       /mnt/data/para_and_config/para/dict_attr_rw.sqlite
312     /root/power/bin/comdmgr 8       /mnt/data/para_and_config/para/dict_para.sqlite
312     /root/power/bin/comdmgr 9       /mnt/data/subdev_diagnosis_data/nfbbms/nfbbms_history_alarm.sqlite
312     /root/power/bin/comdmgr 10      /mnt/data/subdev_diagnosis_data/nfbbms/nfbbms_history_data.sqlite
312     /root/power/bin/comdmgr 11      /mnt/data/subdev_diagnosis_data/nfbbms/nfbbms_operate_record.sqlite
312     /root/power/bin/comdmgr 12      /mnt/data/subdev_diagnosis_data/fbbms/fbbms_history_alarm.sqlite
312     /root/power/bin/comdmgr 13      /mnt/data/subdev_diagnosis_data/fbbms/fbbms_history_data.sqlite
312     /root/power/bin/comdmgr 14      /mnt/data/subdev_diagnosis_data/fbbms/fbbms_operate_record.sqlite
312     /root/power/bin/comdmgr 15      anon_inode:[eventfd]
312     /root/power/bin/comdmgr 16      anon_inode:[eventfd]
312     /root/power/bin/comdmgr 17      socket:[29400]
312     /root/power/bin/comdmgr 18      /mnt/data/database/history_event.sqlite
312     /root/power/bin/comdmgr 19      anon_inode:[eventfd]
312     /root/power/bin/comdmgr 20      socket:[32156]
312     /root/power/bin/comdmgr 21      socket:[30362]
312     /root/power/bin/comdmgr 22      /dev/ttyS1
312     /root/power/bin/comdmgr 23      /dev/ttyS2
328     /bin/busybox    0       /dev/null
328     /bin/busybox    1       /dev/console
328     /bin/busybox    2       /dev/console
328     /bin/busybox    6       /dev/watchdog
328     /bin/busybox    10      /dev/console
332     /sbin/sshd      0       /dev/null
332     /sbin/sshd      1       /dev/null
332     /sbin/sshd      2       /dev/null
332     /sbin/sshd      3       /dev/ptmx
332     /sbin/sshd      4       socket:[31819]
332     /sbin/sshd      6       socket:[30060]
332     /sbin/sshd      7       /dev/ptmx
332     /sbin/sshd      8       /dev/ptmx
334     /bin/busybox    0       /dev/pts/0
334     /bin/busybox    1       /dev/pts/0
334     /bin/busybox    2       /dev/pts/0
334     /bin/busybox    10      /dev/tty
339     /root/power/bin/productapp      0       /dev/null
339     /root/power/bin/productapp      1       /dev/console
339     /root/power/bin/productapp      2       /dev/console
339     /root/power/bin/productapp      3       anon_inode:[eventfd]
339     /root/power/bin/productapp      4       socket:[32184]
339     /root/power/bin/productapp      5       /root/power/etc/shm/value.attr.bin
339     /root/power/bin/productapp      6       /mnt/data/para_and_config/para/classify.sqlite
339     /root/power/bin/productapp      7       /mnt/data/para_and_config/para/dict_attr_rw.sqlite
339     /root/power/bin/productapp      8       /mnt/data/para_and_config/para/dict_para.sqlite
339     /root/power/bin/productapp      9       socket:[32185]
339     /root/power/bin/productapp      10      /mnt/data/database/mains_on_rec.sqlite
339     /root/power/bin/productapp      11      /mnt/data/database/mains_off_rec.sqlite
339     /root/power/bin/productapp      12      /mnt/data/database/batt_chg_rec.sqlite
339     /root/power/bin/productapp      13      /mnt/data/database/batt_test_rec.sqlite
339     /root/power/bin/productapp      14      /mnt/data/database/batt_dischg_rec.sqlite
339     /root/power/bin/productapp      15      /mnt/data/database/batt_equ_rec.sqlite
339     /root/power/bin/productapp      16      /mnt/data/database/solar_work_rec.sqlite
339     /root/power/bin/productapp      17      /mnt/data/database/dg_run_rec.sqlite
339     /root/power/bin/productapp      18      /mnt/data/database/dg_refuel_rec.sqlite
339     /root/power/bin/productapp      19      /mnt/data/database/dg_leak_rec.sqlite
339     /root/power/bin/productapp      20      /mnt/data/database/month_energy_rec.sqlite
339     /root/power/bin/productapp      21      /mnt/data/database/day_energy_rec.sqlite
339     /root/power/bin/productapp      22      /mnt/data/database/csu_ref_rec.sqlite
339     /root/power/bin/productapp      23      /mnt/data/database/fbbms_batt_ref_rec.sqlite
339     /root/power/bin/productapp      24      /mnt/data/database/nfbbms_batt_ref_rec.sqlite
339     /root/power/bin/productapp      25      /mnt/data/database/history_event.sqlite
339     /root/power/bin/productapp      26      /mnt/data/database/history_alarm.sqlite
339     /root/power/bin/productapp      27      anon_inode:[eventfd]
339     /root/power/bin/productapp      28      anon_inode:[eventfd]
339     /root/power/bin/productapp      29      socket:[30365]
339     /root/power/bin/productapp      30      anon_inode:[eventfd]
339     /root/power/bin/productapp      31      anon_inode:[eventfd]
339     /root/power/bin/productapp      32      anon_inode:[eventfd]
339     /root/power/bin/productapp      33      anon_inode:[eventfd]
339     /root/power/bin/productapp      34      anon_inode:[eventfd]
339     /root/power/bin/productapp      35      anon_inode:[eventfd]
339     /root/power/bin/productapp      36      socket:[32186]
339     /root/power/bin/productapp      37      socket:[29416]
339     /root/power/bin/productapp      38      socket:[36220]
339     /root/power/bin/productapp      40      socket:[32188]
339     /root/power/bin/productapp      41      socket:[32190]
339     /root/power/bin/productapp      42      socket:[32191]
339     /root/power/bin/productapp      43      socket:[36223]
345     /root/power/etc/lighttpd/bin/lighttpd   0       /dev/null
345     /root/power/etc/lighttpd/bin/lighttpd   1       /dev/null
345     /root/power/etc/lighttpd/bin/lighttpd   2       /dev/null
345     /root/power/etc/lighttpd/bin/lighttpd   3       socket:[31851]
345     /root/power/etc/lighttpd/bin/lighttpd   4       socket:[31852]
345     /root/power/etc/lighttpd/bin/lighttpd   5       anon_inode:[eventpoll]
345     /root/power/etc/lighttpd/bin/lighttpd   6       socket:[37262]
345     /root/power/etc/lighttpd/bin/lighttpd   8       socket:[30414]
345     /root/power/etc/lighttpd/bin/lighttpd   10      socket:[30434]
345     /root/power/etc/lighttpd/bin/lighttpd   11      socket:[30441]
345     /root/power/etc/lighttpd/bin/lighttpd   14      socket:[30444]
345     /root/power/etc/lighttpd/bin/lighttpd   16      socket:[30450]
377     /root/power/bin/aidido  0       /dev/null
377     /root/power/bin/aidido  1       /dev/console
377     /root/power/bin/aidido  2       /dev/console
377     /root/power/bin/aidido  3       anon_inode:[eventfd]
377     /root/power/bin/aidido  4       socket:[29355]
377     /root/power/bin/aidido  5       /root/power/etc/shm/value.attr.bin
377     /root/power/bin/aidido  6       /mnt/data/para_and_config/para/classify.sqlite
377     /root/power/bin/aidido  7       /mnt/data/para_and_config/para/dict_attr_rw.sqlite
377     /root/power/bin/aidido  8       /mnt/data/para_and_config/para/dict_para.sqlite
377     /root/power/bin/aidido  9       /dev/ioctl
391     /root/power/bin/netmgr  0       /dev/null
391     /root/power/bin/netmgr  1       /dev/console
391     /root/power/bin/netmgr  2       /dev/console
391     /root/power/bin/netmgr  3       anon_inode:[eventfd]
391     /root/power/bin/netmgr  4       socket:[36141]
391     /root/power/bin/netmgr  5       /root/power/etc/shm/value.attr.bin
391     /root/power/bin/netmgr  6       /mnt/data/para_and_config/para/classify.sqlite
391     /root/power/bin/netmgr  7       /mnt/data/para_and_config/para/dict_attr_rw.sqlite
391     /root/power/bin/netmgr  8       /mnt/data/para_and_config/para/dict_para.sqlite
391     /root/power/bin/netmgr  9       socket:[36142]
391     /root/power/bin/netmgr  11      anon_inode:[eventfd]
391     /root/power/bin/netmgr  12      socket:[30299]
391     /root/power/bin/netmgr  13      socket:[29385]
391     /root/power/bin/netmgr  15      anon_inode:[eventfd]
391     /root/power/bin/netmgr  16      socket:[29384]
391     /root/power/bin/netmgr  17      anon_inode:[eventfd]
399     /root/power/bin/main.fcgi       0       socket:[36225]
399     /root/power/bin/main.fcgi       1       /dev/null
399     /root/power/bin/main.fcgi       2       /dev/null
399     /root/power/bin/main.fcgi       3       anon_inode:[eventfd]
399     /root/power/bin/main.fcgi       4       socket:[32193]
399     /root/power/bin/main.fcgi       5       /mnt/data/database/mains_on_rec.sqlite
399     /root/power/bin/main.fcgi       6       /mnt/data/database/mains_off_rec.sqlite
399     /root/power/bin/main.fcgi       7       /mnt/data/database/batt_chg_rec.sqlite
399     /root/power/bin/main.fcgi       8       /mnt/data/database/batt_test_rec.sqlite
399     /root/power/bin/main.fcgi       9       /mnt/data/database/batt_dischg_rec.sqlite
399     /root/power/bin/main.fcgi       10      /mnt/data/database/batt_equ_rec.sqlite
399     /root/power/bin/main.fcgi       11      /mnt/data/database/solar_work_rec.sqlite
399     /root/power/bin/main.fcgi       12      /mnt/data/database/dg_run_rec.sqlite
399     /root/power/bin/main.fcgi       13      /mnt/data/database/dg_refuel_rec.sqlite
399     /root/power/bin/main.fcgi       14      /mnt/data/database/dg_leak_rec.sqlite
399     /root/power/bin/main.fcgi       15      /mnt/data/database/month_energy_rec.sqlite
399     /root/power/bin/main.fcgi       16      /mnt/data/database/day_energy_rec.sqlite
399     /root/power/bin/main.fcgi       17      /mnt/data/database/csu_ref_rec.sqlite
399     /root/power/bin/main.fcgi       18      /mnt/data/database/fbbms_batt_ref_rec.sqlite
399     /root/power/bin/main.fcgi       19      /mnt/data/database/nfbbms_batt_ref_rec.sqlite
399     /root/power/bin/main.fcgi       20      /root/power/etc/shm/value.attr.bin
399     /root/power/bin/main.fcgi       21      /mnt/data/para_and_config/para/classify.sqlite
399     /root/power/bin/main.fcgi       22      /mnt/data/para_and_config/para/dict_attr_rw.sqlite
399     /root/power/bin/main.fcgi       23      /mnt/data/para_and_config/para/dict_para.sqlite
399     /root/power/bin/main.fcgi       24      /mnt/data/database/history_alarm.sqlite
399     /root/power/bin/main.fcgi       25      /mnt/data/database/history_data.sqlite
399     /root/power/bin/main.fcgi       26      /mnt/data/database/history_event.sqlite
399     /root/power/bin/main.fcgi       27      socket:[32194]
399     /root/power/bin/main.fcgi       28      /mnt/data/database/login_log.sqlite
401     /root/power/bin/candmgr 0       /dev/null
401     /root/power/bin/candmgr 1       /dev/console
401     /root/power/bin/candmgr 2       /dev/console
401     /root/power/bin/candmgr 3       anon_inode:[eventfd]
401     /root/power/bin/candmgr 4       socket:[30298]
401     /root/power/bin/candmgr 5       /root/power/etc/shm/value.attr.bin
401     /root/power/bin/candmgr 6       /mnt/data/para_and_config/para/classify.sqlite
401     /root/power/bin/candmgr 7       /mnt/data/para_and_config/para/dict_attr_rw.sqlite
401     /root/power/bin/candmgr 8       /mnt/data/para_and_config/para/dict_para.sqlite
401     /root/power/bin/candmgr 9       /mnt/data/database/history_event.sqlite
401     /root/power/bin/candmgr 10      /mnt/data/database/fault_record_first_frame.sqlite
401     /root/power/bin/candmgr 11      /mnt/data/database/fault_record_data.sqlite
401     /root/power/bin/candmgr 12      socket:[36184]
401     /root/power/bin/candmgr 13      socket:[32144]
401     /root/power/bin/candmgr 14      /mnt/data/database/history_event.sqlite
401     /root/power/bin/candmgr 15      socket:[32153]
436     /root/power/bin/bcmuapp 0       /dev/null
436     /root/power/bin/bcmuapp 1       /dev/console
436     /root/power/bin/bcmuapp 2       /dev/console
436     /root/power/bin/bcmuapp 3       anon_inode:[eventfd]
436     /root/power/bin/bcmuapp 4       socket:[30394]
436     /root/power/bin/bcmuapp 5       /root/power/etc/shm/value.attr.bin
436     /root/power/bin/bcmuapp 6       /mnt/data/para_and_config/para/classify.sqlite
436     /root/power/bin/bcmuapp 7       /mnt/data/para_and_config/para/dict_attr_rw.sqlite
436     /root/power/bin/bcmuapp 8       /mnt/data/para_and_config/para/dict_para.sqlite
436     /root/power/bin/bcmuapp 9       /mnt/data/database/history_event.sqlite
436     /root/power/bin/bcmuapp 10      socket:[30395]
441     /root/power/bin/bsmuapp 0       /dev/null
441     /root/power/bin/bsmuapp 1       /dev/console
441     /root/power/bin/bsmuapp 2       /dev/console
441     /root/power/bin/bsmuapp 3       anon_inode:[eventfd]
441     /root/power/bin/bsmuapp 4       socket:[30396]
441     /root/power/bin/bsmuapp 5       /root/power/etc/shm/value.attr.bin
441     /root/power/bin/bsmuapp 6       /mnt/data/para_and_config/para/classify.sqlite
441     /root/power/bin/bsmuapp 7       /mnt/data/para_and_config/para/dict_attr_rw.sqlite
441     /root/power/bin/bsmuapp 8       /mnt/data/para_and_config/para/dict_para.sqlite
441     /root/power/bin/bsmuapp 9       /mnt/data/database/history_event.sqlite
442     /bin/busybox    0       /dev/console
442     /bin/busybox    1       /dev/console
442     /bin/busybox    2       /dev/console
442     /bin/busybox    10      /dev/tty
447     /root/power/bin/northpro        0       /dev/null
447     /root/power/bin/northpro        1       /dev/console
447     /root/power/bin/northpro        2       /dev/console
447     /root/power/bin/northpro        3       anon_inode:[eventfd]
447     /root/power/bin/northpro        4       socket:[37259]
447     /root/power/bin/northpro        5       /root/power/etc/shm/value.attr.bin
447     /root/power/bin/northpro        6       /mnt/data/para_and_config/para/classify.sqlite
447     /root/power/bin/northpro        7       /mnt/data/para_and_config/para/dict_attr_rw.sqlite
447     /root/power/bin/northpro        8       /mnt/data/para_and_config/para/dict_para.sqlite
447     /root/power/bin/northpro        9       /dev/ttyS3
447     /root/power/bin/northpro        10      socket:[36233]
447     /root/power/bin/northpro        11      socket:[36234]
447     /root/power/bin/northpro        12      /mnt/data/database/mains_on_rec.sqlite
447     /root/power/bin/northpro        13      /mnt/data/database/mains_off_rec.sqlite
447     /root/power/bin/northpro        14      /mnt/data/database/batt_chg_rec.sqlite
447     /root/power/bin/northpro        15      /mnt/data/database/batt_test_rec.sqlite
447     /root/power/bin/northpro        16      /mnt/data/database/batt_dischg_rec.sqlite
447     /root/power/bin/northpro        17      /mnt/data/database/batt_equ_rec.sqlite
447     /root/power/bin/northpro        18      /mnt/data/database/solar_work_rec.sqlite
447     /root/power/bin/northpro        19      /mnt/data/database/dg_run_rec.sqlite
447     /root/power/bin/northpro        20      /mnt/data/database/dg_refuel_rec.sqlite
447     /root/power/bin/northpro        21      /mnt/data/database/dg_leak_rec.sqlite
447     /root/power/bin/northpro        22      /mnt/data/database/month_energy_rec.sqlite
447     /root/power/bin/northpro        23      /mnt/data/database/day_energy_rec.sqlite
447     /root/power/bin/northpro        24      /mnt/data/database/csu_ref_rec.sqlite
447     /root/power/bin/northpro        25      /mnt/data/database/fbbms_batt_ref_rec.sqlite
447     /root/power/bin/northpro        26      /mnt/data/database/nfbbms_batt_ref_rec.sqlite
447     /root/power/bin/northpro        27      /mnt/data/database/history_alarm.sqlite
447     /root/power/bin/northpro        28      /mnt/data/database/history_data.sqlite
447     /root/power/bin/northpro        29      /mnt/data/database/history_event.sqlite
447     /root/power/bin/northpro        30      socket:[32211]
474     /root/power/bin/SysMonitor_run  0       /dev/null
474     /root/power/bin/SysMonitor_run  1       /dev/console
474     /root/power/bin/SysMonitor_run  2       /dev/console
474     /root/power/bin/SysMonitor_run  3       anon_inode:[eventfd]
474     /root/power/bin/SysMonitor_run  4       socket:[29293]
474     /root/power/bin/SysMonitor_run  5       socket:[29300]
474     /root/power/bin/SysMonitor_run  6       /dev/watchdog
486     /bin/busybox    0       /dev/null
486     /bin/busybox    1       /dev/console
486     /bin/busybox    2       /dev/console
486     /bin/busybox    3       socket:[30552]
486     /bin/busybox    5       /root/power/etc/shm/value.attr.bin
498     /root/power/bin/mainapp 0       /root/power/etc/shm/value.attr.bin
498     /root/power/bin/mainapp 1       /dev/console
498     /root/power/bin/mainapp 2       /dev/console
498     /root/power/bin/mainapp 3       anon_inode:[eventfd]
498     /root/power/bin/mainapp 4       socket:[32077]
498     /root/power/bin/mainapp 5       socket:[32078]
498     /root/power/bin/mainapp 6       /mnt/data/para_and_config/para/classify.sqlite
498     /root/power/bin/mainapp 7       /mnt/data/para_and_config/para/dict_attr_rw.sqlite
498     /root/power/bin/mainapp 8       /mnt/data/para_and_config/para/dict_para.sqlite
498     /root/power/bin/mainapp 9       /mnt/data/database/history_alarm.sqlite
498     /root/power/bin/mainapp 10      /mnt/data/database/real_alarm.sqlite
498     /root/power/bin/mainapp 11      /mnt/data/database/history_data.sqlite
498     /root/power/bin/mainapp 12      /mnt/data/database/history_event.sqlite
498     /root/power/bin/mainapp 14      anon_inode:[eventfd]
498     /root/power/bin/mainapp 15      socket:[29349]
