/*****************************************************************************
 文件名      : bcmu_business.h
 内容摘要    : BCMU业务.h文件
 版本        : V1.0
*****************************************************************************/

#ifndef _DEV_BCMU_BUSINESS_H
#define _DEV_BCMU_BUSINESS_H

#ifdef __cplusplus
extern "C" {
#endif   //  __cplusplus

/*  Include   */
#include "data_type.h"
#include "daapi/sids.h"
#include "data_access.h"
#include "MIBTable.h"
#include "pdt_path.h"
#include "fapi_bmu_comm.h"

#define BCMUAPP_BCMU_TIMER  1000    ///<  bcmuapp bcmu 1 seconds
#define BCMUAPP_BCMU_ALM_RESTORE_PERIOD  500    ///<  轮询判断周期500ms
#define BCMUAPP_BCMU_ALM_RESTORE_TIMER  60000*30   ///<  30min恢复告警
#define BCMUAPP_BCMU_OUTLIERS_TIMER  12000    ///<  bcmuapp bcmu 离群计算周期 12 seconds  （不小于获取电芯电压周期的10倍）
#define BCMUAPP_OUTLIERS_STANDY_KEEP_COUNT      150     ///< 离群计算方案2准静态条件位置计数，即（30分钟/BCMUAPP_BCMU_OUTLIERS_TIMER）
#define BCMUAPP_BCMU_DELAY_TIMER  6000    ///<  充放电置位10S（进行充放电延时）

#define MAX_BATTERY_MODEL_CELL_TEMP  (125.0f)
#define MIN_BATTERY_MODEL_CELL_TEMP  (-40.0f)
#define TOTAL_CELLS (BMU_CELL_NUM*BMU_NUM)
#define MAX_OUTLIERS 10     ///<  最大离群保存数
#define NOT_OUTLINER 0      ///<  非离群
#define OUTLINER 1          ///<  离群

#define STATISTICAL_OUTLIER_CALCULATION    1   // 统计离群计算方式：z分数
#define TABLE_LOOKUP_OUTLIER_CALCULATION   2   // 查表离群计算方式：soc


/* Started by AICoder, pid:ja17fl84dcu5e59145a0080c200703060783c0e9 */
#define BATTERY_CAPACITY   261  // 储能柜容量 (kWh)
#define MAX_POWER_PRECISION 2    // 功率精度

/* Ended by AICoder, pid:ja17fl84dcu5e59145a0080c200703060783c0e9 */

#define OCV_TAB_SIZE_TEMP_NUM  11
#define OCV_TAB_SIZE_SOC_NUM   21
#define SOC_INTERVAL           5

typedef struct {
    float temp_match;
    float volt_area[2];
    float soc_area[2];
    float soc;
} ocv_area_t;

typedef struct
{
    float_type_t cell_volt[BMU_CELL_NUM]; //单体电压
}cell_volt_data_t;

/* Started by AICoder, pid:01b65vfe99jf3b5143bb0965c06f3e0e6205526c */
typedef struct {
    int pack;
    int cell;
    float z_score;
    int volt_diff;
}outlier_info_t;
/* Ended by AICoder, pid:01b65vfe99jf3b5143bb0965c06f3e0e6205526c */

/* Started by AICoder, pid:30a32b61236ace3143650bee40e37104baa4a911 */
typedef struct {
    int count; // Counter for consecutive periods
    int state; // 0: not outlier, 1: outlier
} cell_history_t;
/* Ended by AICoder, pid:30a32b61236ace3143650bee40e37104baa4a911 */

typedef struct
{
    float_type_t bcmu_volt;//电池柜电压
}bcmu_ana_data_t;

typedef struct
{
    int_type_t module_vol_high_cut_off_sta;       //电池模块电压高截止状态
    int_type_t cell_chg_high_temp_cut_off_sta;    //单体充电高温截止状态
    int_type_t cell_chg_low_temp_cut_off_sta;     //单体充电低温截止状态
    int_type_t module_vol_low_cut_off_sta;        //放电低压截止状态
    int_type_t cell_vol_high_cut_off_sta;         //单体电压高截止状态
    int_type_t cell_vol_low_cut_off_sta;          //单体电压低截止状态
}bcmu_dig_data_t;

typedef struct
{
    int_type_t module_chg_vol_diff_end_alarm;     //充电电池模块电压极差截止告警
    int_type_t module_dischg_vol_diff_end_alarm;  //放电电池模块电压极差截止告警
    int_type_t module_chg_temp_diff_end_alarm;    //充电电池模块温度极差截止告警
    int_type_t module_dischg_temp_diff_end_alarm; //放电电池模块温度极差截止告警
    int_type_t cell_chg_vol_diff_end_alarm;       //充电电芯电压极差截止告警
    int_type_t cell_dischg_vol_diff_end_alarm;    //放电电芯电压极差截止告警
    int_type_t chg_vol_end_alarm;                 //充电电压截止告警
    int_type_t dischg_vol_end_alarm;              //放电电压截止告警
    int_type_t chg_curr_alm_lv1;                  //充电电流一级告警
    int_type_t chg_curr_alm_lv2;                  //充电电流二级告警
    int_type_t chg_curr_alm_lv3;                  //充电电流三级告警
    int_type_t chg_curr_alm_end;                  //充电电流截止告警
    int_type_t dischg_curr_alm_lv1;               //放电电流一级告警
    int_type_t dischg_curr_alm_lv2;               //放电电流二级告警
    int_type_t dischg_curr_alm_lv3;               //放电电流三级告警
    int_type_t dischg_curr_alm_end;               //放电电流截止告警
    int_type_t ems_comm_fail;                     //ems通讯中断
    int_type_t module_term_temp_diff_lv1_alarm;   //电池模块端子温度极差一级告警
}bcmu_alm_data_t;

typedef struct
{
    int_type_t module_vol_high_cut_off_sta;       //电池模块电压高截止状态
    int_type_t cell_chg_high_temp_cut_off_sta;    //单体充电高温截止状态
    int_type_t cell_chg_low_temp_cut_off_sta;     //单体充电低温截止状态
    int_type_t module_vol_low_cut_off_sta;        //放电低压截止状态
    int_type_t cell_vol_high_cut_off_sta;         //单体电压高截止状态
    int_type_t cell_vol_low_cut_off_sta;          //单体电压低截止状态
    int_type_t cell_dischg_high_temp_cut_off_sta; //单体放电高温截止状态
    int_type_t cell_dischg_low_temp_cut_off_sta;  //单体放电低温截止状态
}bcmu_cut_off_sta_t;


/*  函数定义   */
int bcmuapp_bcmu_init(void);
int bcmuapp_bcmu_main(msgid msg_id, msgsn msg_sn, const void *msg, unsigned int msg_len, const mid sender);


#ifdef UNITTEST
    STATIC bcmu_ana_data_t s_bcmu_ana_data;
    STATIC bcmu_dig_data_t s_bcmu_dig_data;
    STATIC int deal_bcmu_business(void);
    STATIC int load_bcmu_ana_data(void);
    STATIC int load_bcmu_dig_data(void);
    STATIC int load_bcmu_alm_data(void);
    STATIC int update_prt_status(void);
    STATIC int update_chg_prt_status(void);
    STATIC int update_dischg_prt_status(void);
    STATIC int get_cutoff_status_by_sid( sid cutoff_sta_sid);
    STATIC int update_bcmu_state(void);
    STATIC int update_cut_off_status(void);
    STATIC int judge_cell_cutoff_status(int_type_t* cutoff_alm_val);
    STATIC int judge_cell_temp_cutoff_status(int_type_t* cutoff_alm_val);
    STATIC int set_bcmu_prt_status(void);
    STATIC int update_bcmu_battery_status(void);
    STATIC int update_cell_outliers_status(void);
    STATIC int should_calculate_outliers(void);
    STATIC int load_bmu_cell_volt(float (*voltages)[BMU_CELL_NUM]);
    STATIC int judge_cell_outliers1(float (*voltages)[BMU_CELL_NUM]);
    STATIC int remove_extreme_voltages(float (*voltages)[BMU_CELL_NUM], int *valid_cells, float mean, int remove_count);
    STATIC float calculate_std_dev(float (*voltages)[BMU_CELL_NUM], int *valid_cells, float mean);
    STATIC float calculate_mean(float (*voltages)[BMU_CELL_NUM], int *valid_cells);
    STATIC int calculate_z_scores(float (*voltages)[BMU_CELL_NUM], float mean, float std_dev, float *z_scores);
    STATIC int update_outliers(float (*voltages)[BMU_CELL_NUM], float mean, float *z_scores, cell_history_t *outlier_history, outlier_info_t *outliers, int *outlier_count);
    STATIC int save_outliers_info(outlier_info_t *outliers, int *outlier_count);
    STATIC int judge_sys_time_valid(void);
    STATIC int do_replace_finished_judge(void);
#endif

#ifdef __cplusplus
}
#endif  //  __cplusplus

#endif

