/**
 * @file     sysmonitor_proc_manage.c
 * @brief    进程管理模块
 * @details  负责进程启动、监听、重启
 * <AUTHOR> 
 * @date     2017-04-19
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 

/*  includes  */
#include <libxml/xpath.h>
#include <libxml/parser.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include "sysmonitor.h"
#include "plat_utils_string.h"
#include "plat_fapi_reset_events.h"
#include "plat_fapi_para_import_export.h"
#include "plat_utils_system.h"
#include "plat_utils_pipe.h"
#include "plat_fapi_crypt_key.h"
#include "plat_fapi_energy_crypt.h"
#include "plat_utils_file.h"
#include "plat_files.h"
#include "plat_utils_safe_inf.h"
#include "plat_hal.h"

/* defines  */
#define TIMER1_OUT          1000    ///<  定时器1定时时间
#define TIMER2_OUT          1000    ///<  定时器2定时时间
#define TIMER4_OUT          60000   ///<  定时器3定时时间(检测data分区擦写异常,1分钟定时器)

#define DIE_TIMEOUT         90      ///<  进程死机超时时间
#define DIE_CNT_MAX         3       ///<  死机最大次数
#define KILL_RETRY_TIMES    3       ///<  杀死进程重试次数

#define PROC_NORMAL_CNT     10      ///<  程序正常运行心跳计数
#define PROC_START_CNT      3       ///<  程序启动心跳计数

#define HIGH_RESOURCE_USAGE_TIME 60         ///<  高资源使用持续时间，单位秒
#define HIGH_CPU_THRESHOLD  99              ///<  CPU占用率高阈值
#define HIGH_MEM_THRESHOLD  95               ///<  MEM占用率高阈值，可利用MEM小于5%




#define PROC_ATTR_PATH      "path"          ///<  进程属性——进程路径
#define PROC_ATTR_NAME      "name"          ///<  进程属性——进程名
#define PROC_ATTR_TIMEOUT   "timeout"       ///<  进程属性——死机超时
#define PROC_ATTR_MSG       "wait_msg"      ///<  进程属性——是否影响后续进程启动
#define PROC_ATTR_ARG       "arg"           ///<  进程属性——进程启动所需的参数
#define PROC_ATTR_IS_PULSE  "is_pulse"      ///<  进程属性——进程是否支持心跳机制




/*  typedefs  */




/* globals  */
/**
 * 进程配置信息链表
 */
GList *proc_start_list = NULL;

static int init_db_flag = TRUE;
static int proc_status = PROC_START;

static int s_slWdt_fd = -1; // 看门狗文件指针
static int s_high_cpu_times = 0; // 高cpu次数, 1s检测一次
static int s_high_mem_times = 0; // 高mem次数, 1s检测一次
static boolean s_begin_check_system_resources = FALSE; // 是否已经启动资源检测


/* locals   */
static void start_all_process(const void *msg, unsigned int msg_len);
STATIC void start_process(process_info_t *app);
static void child_proc_handle(process_info_t *proc);
static void grandchild_proc_handle(process_info_t *proc);
static void listen_process(const void *msg, unsigned int msg_len);
static void check_process_die(void);
static int kill_all_process(msgid msg_id, msgsn msg_sn);
static int init_kill_all_process();
STATIC int kill_process(process_info_t *proc);
static int init_process_info(void);
static int get_file_path(char *file_path, unsigned int path_len);
static int get_one_proc_conf(xmlNodePtr conf_info, process_info_t *proc);
static int get_proc_base_info(xmlNodePtr xml_node, process_info_t *proc);
static gint proc_cmp(gconstpointer list_data, gconstpointer usr_data);
static boolean send_msg_2update(int threshold, int msg_type);
static void check_prog_status(void);
static void manage_process(msgid msg_id, msgsn msg_sn, const void *msg,
                                 unsigned int msg_len, const mid sender);
static void generate_crypt_key_to_shm(void);

static void manage_process_common(msgid msg_id, msgsn msg_sn, const void *msg,
                                 unsigned int msg_len, const mid sender);
static xmlNodePtr get_node_by_path(const xmlChar *xpath, xmlDocPtr doc);
static int reload_proc_start_info(char* proc_name, process_info_t* process_info);
static int handle_start_proccess_req(char* proc_name);
static int handle_stop_proccess_req(char* proc_name);
static void sync_snmp_enable_flag(void);
static int handle_restart_proccess_req(char* proc_name);
static void deliver_proc_status(msgid msg_id, msgsn msg_sn);
static int software_restart(msgid msg_id, msgsn msg_sn);


#ifndef UNITEST
STATIC int initialize_watchdog(void);
STATIC int feed_watchdog(void);
STATIC int set_watchdog_timeout(int timeout);
STATIC int check_system_resource_usage(void);
STATIC int restart_all_apps(void);
STATIC int save_high_resources_event(int resource_id);
STATIC boolean is_start_system_resource_check(void);
#endif
/* forward declarations  */

/** 
 * @brief 进程管理模块初始化.  
 * @details 进程管理模块初始化..
 * @retval  SUCCESS  成功  
 * @note  注解
 * @par 其它
 *      无
 * @par sample code 
 * @code 
 * @endcode 
 * @par 修改日志 
 *      2017-04-28, created by longmingxing 
 */ 
int sysmonitor_proc_manage_init(void) {
    zx_update_startup_status_setok(); // sysmonitor程序运行起来第一设置标志为OK
    process_info_t *mainapp = NULL;

    generate_crypt_key_to_shm();
    init_syslog(log_plat);
    init_process_info();

    init_kill_all_process();
    mainapp = (process_info_t *)g_list_nth_data(proc_start_list, 0);
    start_process(mainapp);
    CHECK_FUN_RET_INT(set_timer(PCHAR_TO_MID(MID_SYSMONITOR_PROC_MANAGE), TIMER1, TIMER1_OUT), 0);
    CHECK_FUN_RET_INT(set_timer(PCHAR_TO_MID(MID_SYSMONITOR_PROC_MANAGE), TIMER2, TIMER2_OUT), 0);
    CHECK_FUN_RET_INT(set_timer(PCHAR_TO_MID(MID_SYSMONITOR_PROC_MANAGE), TIMER4, TIMER4_OUT), 0);
    set_watchdog_timeout(WATCHDOG_TIMEOUT);
    CHECK_FUN_RET_INT(set_timer(PCHAR_TO_MID(MID_SYSMONITOR_PROC_MANAGE), TIMER3, TIMER3_OUT), 0);
    return SUCCESS;
}

static int judge_data_erased_blocks(void) {
    FILE *file = NULL;
    boolean device_found = FALSE;
    char line[256] = {0};
    int n_erased_blocks = 100; //默认不为0，确保读取到0才算异常
    static int erased_blocks_zero_count = 0;

    if (is_ti_cpu() != SUCCESS) {
        return FAILURE;
    }

    system_s("cp -f /proc/yaffs /var");

    file = open_file_stream("/var/yaffs", "r");
    if (file == NULL) {
        erased_blocks_zero_count = 0;
        return FAILURE;
    }

    while (plat_fgets_s(line, sizeof(line), sizeof(line), file) == SUCCESS) {
        if (strstr(line, "Device 6 \"Data\"")) {
            device_found = TRUE;
            continue;
        }

        if (device_found) {
            if (strstr(line, "n_erased_blocks")) {
                sscanf_s(line, "%*[^0-9]%d", &n_erased_blocks);
                if(n_erased_blocks == 0) {
                    erased_blocks_zero_count++;
                } else {
                    erased_blocks_zero_count = 0;
                }
                break;
            }
        }
    }

    close_file_stream(file);

    if(device_found != 1) {
        erased_blocks_zero_count = 0;
    }

    // 1分钟定时器，触发5次后，执行data分区擦写后重启
    if (erased_blocks_zero_count >= 5) {
        SYSLOG_NOTICE("data n_erased_blocks = 0, we will erased data block!");
        system_s("umount -fl  /dev/mtdblock10");
        system_s("flash_erase /dev/mtd10 0 0");
        system_s("reboot -f");
        return SUCCESS;
    }

    return FAILURE;
}

static int proc_timer_msg(msgid msg_id) {
    switch (msg_id) {
        case TIMER1_MSG:
            check_process_die();
            check_system_resource_usage();
            break;
        case TIMER2_MSG:
            check_prog_status();
            break;
        case TIMER3_MSG:
            feed_watchdog();
            break;
        case TIMER4_MSG:
            judge_data_erased_blocks();
            break;
        default:
            SYSLOG_ERR("proc_manage:Unknown msg!");
            break;
    }

    return SUCCESS;
}

static int proc_ipc_msg(msgid msg_id,
                        msgsn msg_sn,
                        const void *msg,
                        unsigned int msg_len,
                        const mid sender) {
    switch (msg_id) {
        case SYS_PROC_START_MSG:
            start_all_process(msg, msg_len);
            break;

        case SYS_PROC_KILL_MSG:
            kill_all_process(msg_id, msg_sn);
            break;

        case SYS_PROC_ALIVE_MSG:
            listen_process(msg, msg_len);
            break;

        case SYS_PROC_MANAGE_WEB_MSG:
            manage_process(msg_id, msg_sn, msg, msg_len, sender);
            break;

        case SYS_PROC_MANAGE_COMMON_MSG:
            manage_process_common(msg_id, msg_sn, msg, msg_len, sender);
            break;

        case SYS_PROC_GET_RUNNING_STATE_MSG:
            deliver_proc_status(msg_id, msg_sn);
            break;

        case SYS_PROC_SOFTWARE_RESTART:
            software_restart(msg_id, msg_sn);
            break;

        default:
            SYSLOG_ERR("proc_manage:Unknown msg!");
            break;
    }

    return SUCCESS;
}


/** 
 * @brief 进程管理模块主函数.  
 * @details 进程管理模块主函数.
 * @retval  SUCCESS  成功  
 * @note  注解
 * @par 其它
 *      无
 * @par sample code 
 * @code 
 * @endcode 
 * @par 修改日志 
 *      2017-04-28, created by xxx 
 */ 
int sysmonitor_proc_manage_main(msgid msg_id,
                                             msgsn msg_sn,
                                             const void *msg,
                                             unsigned int msg_len,
                                             const mid sender) {
    if (msg_id >= (msgid)TIMER1_MSG && msg_id <= (msgid)TIMER10_MSG) {
        return proc_timer_msg(msg_id);
    } else {
        return proc_ipc_msg(msg_id, msg_sn, msg, msg_len, sender);
    }
}


static void manage_process(msgid msg_id, msgsn msg_sn, const void *msg,
                                 unsigned int msg_len, const mid sender) {
    process_info_t *proc = NULL;
    GList *         element_proc   = NULL;
    manage_ack_t    ack_msg;
    int             ret = 0;
    FILE*           fp;

    RETURN_IF_FAIL(msg != NULL);
    RETURN_IF_FAIL(msg_len == sizeof(manage_req_t));
    const manage_req_t* manage_req = (const manage_req_t *)msg;

    plat_memset_s(&ack_msg, sizeof(ack_msg), 0, sizeof(ack_msg));
    memcpy_s(&ack_msg.req, msg_len, manage_req, msg_len);
    ack_msg.ret = SUCCESS;
    send_syn_ack(msg_id, msg_sn, &ack_msg, sizeof(ack_msg));

    element_proc = g_list_find_custom(proc_start_list, manage_req->proc_name, proc_cmp);
    if (element_proc == NULL) {
        SYSLOG_WARNING("process not found in the proc_list!");
        return;
    }

    proc = (process_info_t *)g_list_nth_data(element_proc, (guint)0);
    RETURN_IF_FAIL(proc != NULL);

    if (manage_req->set_mode == set_start_process &&
         SUCCESS != if_process_running(STR_TO_PCHAR(proc->name))) {
         //  printf("rcv start web req, starting...\n");  //  debug
        start_process(proc);
        /* 启动进程，如果标志文件存在，则删除 */
        fp = fopen(STR_TO_PCHAR(PORC_HIS_STATE_FILE), "r");
        if (fp != NULL) {
            RETURN_IF_FAIL((fclose(fp)) == 0);
            ret = remove(STR_TO_PCHAR(PORC_HIS_STATE_FILE));
            RETURN_IF_FAIL(ret == 0);
        }

    } else if (manage_req->set_mode == set_stop_process &&
               SUCCESS == if_process_running(STR_TO_PCHAR(proc->name))) {
         //  printf("rcv stop web req, stopping...\n");  //  debug
         kill_process(proc);
         /* 关闭进程，如果标志文件不存在，则创建 */
         fp = fopen(STR_TO_PCHAR(PORC_HIS_STATE_FILE), "wr+");
         if (fp != NULL) {
            RETURN_IF_FAIL((fclose(fp)) == 0);
        }
    }

    return;
}

static int software_restart(msgid msg_id, msgsn msg_sn) {
    int                 ack = SUCCESS;
    unsigned int        ack_len;
    int                 ret;

    ret = send_syn_req(SYS_PROC_RELEASE_RESOURCE, 
                        NULL, 
                        0,
                        &ack,
                        &ack_len,
                        10000,
                        PCHAR_TO_MID(MID_ANONYMOUS), 
                        PCHAR_TO_MID(MID_NETMGR_VPN_PROTO));

    if ( ret != SUCCESS || ack != SUCCESS ) {
        ack = FAILURE;
        send_syn_ack(msg_id, msg_sn, &ack, sizeof(ack));
        return SUCCESS;
    }

    ret = system_s("/root/power/etc/sys/procs_reset.sh");
    if ( ret != SUCCESS ) {
        ack = FAILURE;
        send_syn_ack(msg_id, msg_sn, &ack, sizeof(ack));
        return SUCCESS;
    }

    ack = SUCCESS;
    send_syn_ack(msg_id, msg_sn, &ack, sizeof(ack));
    return SUCCESS;
}

static void start_all_process(const void *msg, unsigned int msg_len) {
    int proc_num = 0;
    unsigned int pos      = 0;
    process_info_t *proc = NULL;
    GList *remain_proc   = NULL;

    RETURN_IF_FAIL(msg != NULL);
    RETURN_IF_FAIL(msg_len != 0);
    RETURN_IF_FAIL(msg_len < sizeof(str32));

    //  搜索已启动进程在链表中的位置
    remain_proc = g_list_find_custom(proc_start_list, msg, proc_cmp);
    if (remain_proc == NULL) {
        SYSLOG_WARNING("process not found in the proc_list!");
        return;
    }

    //  继续启动后面的进程
    proc_num = (int)g_list_length(remain_proc);
    for (pos = 1; pos < proc_num; pos++) {
        proc = (process_info_t *)g_list_nth_data(remain_proc, (guint)pos);
        if (NULL == proc) { continue; }
        if (strstr(proc->name, WEB_SERVER_PROCESS) != NULL &&
            access(STR_TO_PCHAR(PORC_HIS_STATE_FILE), F_OK) == 0) {
            plat_printf_s("start_all_process: web state is stopped, not restart!\n");
            continue;
        }
        start_process(proc);
        if (proc->wait_msg) { break; }
    }
}

static gint proc_cmp(gconstpointer list_data,
                        gconstpointer usr_data) {
    process_info_t* proc = (process_info_t*)list_data;
    char* proc_name      = (char*)usr_data;

    return (gint)strcmp(STR_TO_PCHAR(proc->name), proc_name);
}

STATIC void start_process(process_info_t *proc) {
    pid_t tpid;

    RETURN_IF_FAIL(proc != NULL);
    if (FAILURE == kill_process(proc)) { return; }

    tpid = fork();
    if ( -1 == tpid ) {
        SYSLOG_ERR("1st fork %s Fail", proc->full_name);
    } else if ( 0 == tpid ) {  //子进程执行此内容
        child_proc_handle(proc);
    } else {                   //父进程执行此内容
        wait(NULL);
        sleep(1);
        int ttpid = -1;
        if (!judge_process_alive(STR_TO_PCHAR(proc->name), &ttpid)) {
            SYSLOG_ERR("start %s failure", proc->name);
        } else {
            SYSLOG_NOTICE("start %s %d", proc->name, ttpid);
        }
    }
}


static void child_proc_handle(process_info_t *proc) {
    int i;
    pid_t tpid;

    for (i = 3; i < sysconf(_SC_OPEN_MAX); i++) {
        close((int)i);
    }

    tpid = fork();
    if ( -1 == tpid ) {
        SYSLOG_ERR("2rd fork %s Fail", proc->name);
        exit(-1);
    } else if ( 0 == tpid ) {  //  子进程执行下面语句
        grandchild_proc_handle(proc);
    } else {
        exit(0);
    }
}

static void grandchild_proc_handle(process_info_t *proc) {
    int retval = FAILURE;
    unsigned int i, num = 0;
    str256 cmd = {0};
    str256 arg_t = {0};
    char* arg_p;
    char* exe_arg[10] = {0};
    gchar** arg_sp = NULL;


    snprintf_s_void(STR_TO_PCHAR(cmd), sizeof(cmd), "chmod 755 %s", proc->full_name);
    retval = system_s(STR_TO_PCHAR(cmd));
    if ( -1 == retval ) {  //  检查chmod返回结果
        SYSLOG_ERR("chmod %s fail before execl", proc->name);
        exit(-1);
    }

    if (strcmp(STR_TO_PCHAR(proc->arg), "") == 0) {
        num = num +1;
        exe_arg[0] = STR_TO_PCHAR(proc->name);
    } else {
        snprintf_s_void(STR_TO_PCHAR(arg_t), sizeof(arg_t), "%s %s", proc->name, proc->arg);
        arg_p = arg_t;
        arg_sp = g_strsplit(arg_p, " ", -1);
        /*获取以 NULL结尾的参数数组*/
        while (NULL != strsep(&arg_p, " ")) {
            num++;
        }

        for (i = 0; i < num; i++) {
            exe_arg[i] = arg_sp[i];
        }
    }

    exe_arg[num] = NULL;
    execv(STR_TO_PCHAR(proc->full_name), exe_arg);

    g_strfreev(arg_sp);
    SYSLOG_ERR("execv %s fail", proc->name);
}

/** 
 * @brief 杀死所有进程.  
 * @details 杀死所有进程.
 * @retval  SUCCESS  成功    FAILURE 失败
 * @note  注解
 * @par 其它
 *      无
 * @par sample code 
 * @code 
 * @endcode 
 * @par 修改日志 
 *      2017-04-28, created by longmingxing 
 */
static int kill_all_process(msgid msg_id,
                                      msgsn msg_sn) {
    int proc_num = 0;
    unsigned int pos;
    process_info_t *proc = NULL;
    int rtn = SUCCESS;

    proc_num = (int)g_list_length(proc_start_list);
    for (pos = 0; pos < proc_num; pos++) {
        proc = (process_info_t *)g_list_nth_data(proc_start_list, (guint)pos);
        rtn = kill_process(proc);
        if (FAILURE == rtn) {
            send_syn_ack(msg_id, msg_sn, &rtn, (unsigned int)(sizeof(rtn)));
            return rtn;
        }
    }

    send_syn_ack(msg_id, msg_sn, &rtn, (unsigned int)(sizeof(rtn)));
    return rtn;
}

static int init_kill_all_process() {
    int proc_num = 0;
    unsigned int pos;
    process_info_t *proc = NULL;
    int rtn = SUCCESS;

    proc_num = (int)g_list_length(proc_start_list);
    for (pos = 0; pos < proc_num; pos++) {
        proc = (process_info_t *)g_list_nth_data(proc_start_list, (guint)pos);
        rtn = kill_process(proc);
        RETURN_VAL_IF_FAIL(rtn == SUCCESS, rtn);
    }

    return rtn;
}


/** 
 * @brief 杀死单个进程.  
 * @details 杀死单个进程.
 * @param[in]   proc 进程配置信息指针.
 * @retval  SUCCESS  成功    FAILURE 失败
 * @note  注解
 * @par 其它
 *      无
 * @par sample code 
 * @code 
 * @endcode 
 * @par 修改日志 
 *      2017-04-28, created by longmingxing 
 */ 
STATIC int kill_process(process_info_t *proc) {
    int kill_times;
    int retval;
    str256 cmd = {0};
    str256 kill_cgi_cmd = {0};

    RETURN_VAL_IF_FAIL(proc != NULL, FAILURE);

    snprintf_s_void(STR_TO_PCHAR(cmd), sizeof(cmd), "killall %s %s", "-9", proc->name);
    snprintf_s_void(STR_TO_PCHAR(kill_cgi_cmd), sizeof(kill_cgi_cmd), "pkill %s", WEB_CGI_PROCESS);
    for (kill_times = 0; kill_times < KILL_RETRY_TIMES; kill_times++) {
        if (!judge_process_alive(STR_TO_PCHAR(proc->name), NULL)) { return SUCCESS; }

        retval = system_s(STR_TO_PCHAR(cmd));
        if (g_strrstr_len(proc->name, sizeof(proc->name), WEB_SERVER_PROCESS) != NULL) {
            system_s(STR_TO_PCHAR(kill_cgi_cmd));
        }
        if ((retval == SUCCESS) && !judge_process_alive(STR_TO_PCHAR(proc->name), NULL)) {
            return SUCCESS;
        }
        sleep(1);
    }

    SYSLOG_NOTICE("kill %s fail!", proc->name);
    return FAILURE;
}


boolean judge_process_alive(char *proc_name, int *pid) {
    FILE *fstream = NULL;
    str256 buff = {0};
    boolean ret_val = FALSE;

    RETURN_VAL_IF_FAIL(proc_name != NULL, TRUE);

    snprintf_s_void(STR_TO_PCHAR(buff), sizeof(buff), "pidof %s", proc_name);
    fstream = open_pipe_stream(STR_TO_PCHAR(buff), "r");
    RETURN_VAL_IF_FAIL(fstream != NULL, ret_val);

    plat_memset_s((STR_TO_PCHAR(buff)), sizeof(buff), 0, sizeof(buff));
    if ((plat_fgets_s(STR_TO_PCHAR(buff), (int)(sizeof(buff)), (int)(sizeof(buff)), fstream) == SUCCESS)
       && (strnlen_s(STR_TO_PCHAR(buff), STR_LEN_32) > 0)) {
        if (pid) {
            sscanf_s(buff, "%d\n", pid);
        }
        ret_val = TRUE;
    }

    pclose(fstream);
    return ret_val;
}


/** 
 * @brief 进程配置信息初始化.  
 * @details 从xml配置文件读取进程配置信息到链表中.
 * @retval  SUCCESS  成功    FAILURE 失败
 * @note  注解
 * @par 其它
 *      无
 * @par sample code 
 * @code 
 * @endcode 
 * @par 修改日志 
 *      2017-04-28, created by longmingxing 
 */ 
static int init_process_info(void) {
    xmlDocPtr xml_doc = NULL;
    xmlNodePtr xml_node = NULL;
    str128 file_path = {0};
    process_info_t proc;
    gpointer data;

    sync_snmp_enable_flag();

    get_file_path(STR_TO_PCHAR(file_path), (unsigned int)(sizeof(file_path)));

    xml_doc = xmlParseFile((const char*)file_path);
    xml_node = xmlDocGetRootElement(xml_doc);
    if (xml_node == NULL) {
        xmlFreeDoc(xml_doc);
        return FAILURE;
    }
    xml_node = xml_node->xmlChildrenNode;

    while (xml_node != NULL) {
        plat_memset_s(&proc, sizeof(proc), 0, sizeof(proc));
        if (SUCCESS == get_one_proc_conf(xml_node, &proc)) {
            data = g_malloc0((gsize)(sizeof(process_info_t)));
            g_assert(data != NULL);
            memcpy_s(data, sizeof(process_info_t), &proc, sizeof(process_info_t));
            proc_start_list = g_list_append(proc_start_list, data);
        }
        xml_node = xml_node->next;
    }
    xmlFreeDoc(xml_doc);

    if (NULL == proc_start_list) {
        SYSLOG_NOTICE("%s is empty!", PROC_CONF_FILE);
        return FAILURE;
    }

    return SUCCESS;
}

/** 
 * @brief 获取配置文件路径.  
 * @details 获取配置文件路径.
 * @param[in]   path_len 文件路径最大长度.
 * @param[out]  file_path 文件路径. 
 * @retval  SUCCESS  成功    FAILURE 失败  
 * @note  注解
 * @par 其它
 *      无
 * @par sample code 
 * @code 
 * @endcode 
 * @par 修改日志 
 *      2017-04-28, created by longmingxing 
 */ 
static int get_file_path(char *file_path, unsigned int path_len) {
    RETURN_VAL_IF_FAIL(file_path != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(path_len != 0, FAILURE);

    snprintf_s_void(STR_TO_PCHAR(file_path), (size_t)path_len, "%s", PROC_CONF_FILE);
    return SUCCESS;
}

static int get_one_proc_conf(xmlNodePtr xml_node, process_info_t *proc) {
    char *timeout = NULL;
    char *wait_msg = NULL;

    RETURN_VAL_IF_FAIL(xml_node != NULL, FAILURE);
    RETURN_VAL_IF_FAIL(proc != 0, FAILURE);

    //  不是process节点 或者 获取进程基本信息(名称和路径及参数和心跳标识)失败
    if ((XML_ELEMENT_NODE != xml_node->type)
        || (get_proc_base_info(xml_node, proc))) { return FAILURE; }

    //  获取进程超时
    timeout = (char *)xmlGetProp(xml_node, (const xmlChar*)PROC_ATTR_TIMEOUT);
    if ((timeout != NULL) && (strnlen_s(timeout, STR_LEN_MAX) != 0)) {
        proc->die_timeout = atoi(timeout);
    } else {
        proc->die_timeout = DIE_TIMEOUT;
    }

    //  获取启动等待标志
    wait_msg = (char *)xmlGetProp(xml_node, (const xmlChar*)PROC_ATTR_MSG);
    if ((wait_msg != NULL) && (0 == strcmp(wait_msg, "yes"))) {
        proc->wait_msg = TRUE;
    } else {
        proc->wait_msg = FALSE;
    }

    return SUCCESS;
}

static int get_proc_base_info(xmlNodePtr xml_node, process_info_t *proc) {
    char *name = NULL;
    char *path = NULL;
    char *arg  = NULL;
    char *is_pulse = NULL;

    name = (char *)xmlGetProp(xml_node, (const xmlChar*)PROC_ATTR_NAME);
    if ((NULL == name) || (0 == strnlen_s(name, STR_LEN_MAX))) { return FAILURE; }
    snprintf_s_void(STR_TO_PCHAR(proc->name), sizeof(proc->name), "%s", name);

    path = (char *)xmlGetProp(xml_node, (const xmlChar*)PROC_ATTR_PATH);
    if ((path != NULL) && (strnlen_s(path, STR_LEN_MAX) != 0)) {
        snprintf_s_void(STR_TO_PCHAR(proc->full_name), sizeof(proc->full_name), "%s/%s", path, name);
    } else {
        snprintf_s_void(STR_TO_PCHAR(proc->full_name), sizeof(proc->full_name), "%s/%s", PATH_ROOT_BIN, name);
    }

    arg = (char*)xmlGetProp(xml_node, (const xmlChar*)PROC_ATTR_ARG);
    snprintf_s_void(STR_TO_PCHAR(proc->arg), sizeof(proc->arg), "%s", arg);

    //获取心跳标识
    is_pulse = (char*)xmlGetProp(xml_node, (const xmlChar*)PROC_ATTR_IS_PULSE);
    if ((is_pulse != NULL) && (0 == strcmp(is_pulse, "1"))) {
        proc->is_pulse = TRUE;
    } else {
        proc->is_pulse = FALSE;
    }

    return SUCCESS;
}
static void listen_process(const void *msg, unsigned int msg_len) {
    unsigned int proc_num = 0, pos;
    process_info_t *proc = NULL;
    str32 name = {0};

    RETURN_IF_FAIL(msg != NULL);
    RETURN_IF_FAIL(msg_len != 0);
    RETURN_IF_FAIL(msg_len < sizeof(name));

    memcpy_s(STR_TO_PCHAR(name), (size_t)msg_len, msg, (size_t)msg_len);

    /* 如果监听进程为web，但当前web被手动关闭，则不进行监听 */
    if (strcmp(STR_TO_PCHAR(name), WEB_SERVER_PROCESS) == 0 &&
        access(STR_TO_PCHAR(PORC_HIS_STATE_FILE), F_OK) == 0) {
        //  printf("listen_process: web state is stopped, not listen!\n");  //  debug
        return;
    }

    proc_num = g_list_length(proc_start_list);

    /// 只初始化一次
    if ((init_db_flag) && (0 == strcmp(MAINAPP, STR_TO_PCHAR(name)))) {
        open_history_event_db();
        init_db_flag = FALSE;
    }

    /* 进程心跳监听、计数 */
    for (pos = 0; pos < proc_num; pos++) {
        proc = (process_info_t *)g_list_nth_data(proc_start_list, (guint)pos);
        if ((proc != NULL) && (0 == strcmp(STR_TO_PCHAR(proc->name), STR_TO_PCHAR(name)))) {
            proc->die_time = 0;
            proc->die_cnt = 0;
            proc->pulse_cnt++;
            break;
        }
    }
}

static void maneger_other_process_behind_die_proc(process_info_t *die_proc) {
    int proc_num = 0;
    unsigned int pos     = 0;
    process_info_t *proc = NULL;
    GList *remain_proc   = NULL;
    char log_msg[STR_LEN_512] = {'\0'};

    RETURN_IF_FAIL(NULL != die_proc);
    RETURN_IF_FAIL(die_proc->wait_msg);     //   非依赖等待进程则直接返回

    //  搜索死亡进程在链表中的位置
    remain_proc = g_list_find_custom(proc_start_list, die_proc->name, proc_cmp);
    if (remain_proc == NULL) {
        SYSLOG_WARNING("process not found in the proc_list!");
        return;
    }

    //  杀掉死亡进程后面的进程
    proc_num = (int)g_list_length(remain_proc);
    for (pos = 1; pos < proc_num; pos++) {
        proc = (process_info_t *)g_list_nth_data(remain_proc, (guint)pos);
        if (NULL == proc) { continue; }
        kill_process(proc);
        strlcat_s(log_msg, proc->name, sizeof(log_msg));
        strlcat_s(log_msg, " ", sizeof(log_msg));
    }
    SYSLOG_NOTICE("Because the process[%s] is dead, so kill the process behind him.[%s]", die_proc->name, log_msg);
}

static int process_exception_reset(void) {
    event_operator_t process_exception = {
        .media = "System",
        .operator_ = "",
        .identifier = "",
    };
    save_reset_reason_to_file((int)RESET_POWERDOWN, &process_exception);

    sync();
    sleep(3);
    return system_s("reboot -f");
}

static int check_process_d_status(char *proc_name) {
    int ttpid = -1;
    str128 path = {0};
    FILE *file = NULL;
    str256 line = {0};
    char *state = NULL;
    int ret = FALSE;

    if (judge_process_alive(proc_name, &ttpid)) {
        snprintf_s_void(path, sizeof(path), "/proc/%d/status", ttpid);

        file = fopen(path, "r");
        if (file == NULL) {
            SYSLOG_ERR("Failed to open file: %s\n", path);
            return ret;
        }

        
        while (plat_fgets_s(line, sizeof(line), sizeof(line), file) == SUCCESS) {
            if (strncmp(line, "State:", 6) == 0) {
                state = strchr(line, ':') + 2;
                break;
            }
        }

        if (state != NULL && strstr(state, "D") != NULL) {
            SYSLOG_NOTICE("Process %d is in state '%s'\n", ttpid, state);
            ret = TRUE;
        }

        fclose(file);
    }

    return ret;
}

/** 
 * @brief This is a brief description.  
 * @details This is a detailed description.
 * @param[out]  file_path 复位文件绝对路径. 
 * @note  注解
 * @par 其它
 *      无
 * @par sample code 
 * @code 
 * @endcode 
 * @par 修改日志 
 *      2017-08-28, created by xxx 
 */ 
static void get_proc_d_reset_file_path(char* file_path) {
    snprintf_s_void(file_path, sizeof(str128), "%s%s", PATH_DATA_TMP, PROC_D_RESET_REASON_FILE);
}

/** 
 * @brief 创建进程D状态CSU复位标志文件.  
 * @details 创建进程D状态CSU复位标志文件.
 * @param[in]   
 * @param[in]
 * @retval  SUCCESS  成功    FAILURE  失败
 * @note  注解
 * @par 其它
 *      无
 * @par sample code 
 * @code 
 * @endcode 
 * @par 修改日志 
 *      2024-04-12, created by zlj 
 */ 
static  int creat_proc_d_status_reset_file(void) {
    FILE*  reset_file;
    str128 file_path = {0};

    get_proc_d_reset_file_path(STR_TO_PCHAR(file_path));
    reset_file = open_file_stream(STR_TO_PCHAR(file_path), "w");
    if ( reset_file == NULL ) {
        return FAILURE;
    }

    RETURN_VAL_IF_FAIL((fclose(reset_file)) == 0, FAILURE);
    return SUCCESS;
}

int del_proc_d_status_reset_file(void) {
    str128 file_name = {0};
    char cmd[1024] = {0};

    get_proc_d_reset_file_path(file_name);
    if (is_file_exist(file_name)) {
        snprintf_s(cmd, sizeof(cmd), "rm -rf %s", STR_TO_PCHAR(file_name));
        system_s(cmd);
    }
    return SUCCESS;
}

/** 
 * @brief 进程D状态CSU复位处理.  
 * @details 进程D状态CSU复位处理.
 * @param[in]   die_cnt:进程死掉次数
 * @param[in]   proc_name:进程名称
 * @retval  SUCCESS  成功    FAILURE  失败
 * @note  注解
 * @par 其它
 *      无
 * @par sample code 
 * @code 
 * @endcode 
 * @par 修改日志 
 *      2024-04-12, created by zlj 
 */ 
int handle_process_exception(unsigned int die_cnt, char *proc_name) {
    str128 file_name = {0};

    /*进程卡死次数为1，并且进度D状态CSU复位标志文件不存在且进程状态为D则创建标志文件，文件创建成功才复位，否则不做操作*/
    get_proc_d_reset_file_path(file_name);
    if (die_cnt == 1 && (!is_file_exist(file_name))) {
        if (check_process_d_status(proc_name) == TRUE) {
            if (creat_proc_d_status_reset_file() == SUCCESS)
                process_exception_reset();
        }
    }

    return SUCCESS;
}

static void check_process_die(void) {
    unsigned int pos, proc_num = 0;
    process_info_t *proc = NULL;
    event_operator_t process_exception = {
        .media = "System",
        .operator_ = "",
        .identifier = "",
    };
    proc_num = g_list_length(proc_start_list);
    for (pos = 0; pos < proc_num; pos++) {
        proc = (process_info_t *)g_list_nth_data(proc_start_list, (guint)pos);
        if (proc == NULL) return;

        if (strstr(proc->name, WEB_SERVER_PROCESS) != NULL &&
            access(STR_TO_PCHAR(PORC_HIS_STATE_FILE), F_OK) == 0) {
            //  printf("TIME_MSG: check_process_die, web state is stopped, not restart!\n");
            continue;
        }

        /*不支持心跳机制的进程若在线，则初始化心跳数据*/
        if (!proc->is_pulse && proc->die_time >= proc->die_timeout) {
            if (strstr(proc->name, WEB_SERVER_PROCESS) != NULL) {
                if (SUCCESS == if_process_running(STR_TO_PCHAR(proc->name)) &&
                    SUCCESS == if_process_running(WEB_CGI_PROCESS)) {
                    proc->die_time = 0;
                    proc->die_cnt = 0;
                    proc->pulse_cnt++;
                }
            } else if (SUCCESS == if_process_running(STR_TO_PCHAR(proc->name))) {
                proc->die_time = 0;
                proc->die_cnt = 0;
                proc->pulse_cnt++;
            }
        }
        if ( proc->die_time < proc->die_timeout ) {
            proc->die_time += TIMER1_OUT/1000;
        } else if (proc->die_cnt < DIE_CNT_MAX) {
            proc->die_time = 0;
            proc->pulse_cnt = 0;
            proc->die_cnt++;
            CHECK_FUN_RET_INT(handle_process_exception(proc->die_cnt, STR_TO_PCHAR(proc->name)), SUCCESS);
            if (strcmp(STR_TO_PCHAR(proc->name), MAINAPP) == 0) {
                save_reset_reason_to_file((int)RESET_EXCEPTION, &process_exception);
            }
            maneger_other_process_behind_die_proc(proc);
            start_process(proc);
        } else {
            CHECK_FUN_RET_INT(del_proc_d_status_reset_file(), SUCCESS); 
            proc_status = PROC_FAILURE;
            send_asy_msg(SYS_PROG_ROLLBACK_MSG,
                        NULL, 0,
                        PCHAR_TO_MID(MID_SYSMONITOR_PROC_MANAGE),
                        PCHAR_TO_MID(MID_SYSMONITOR_PROG_UPDATE));
        }
    }
}

static boolean send_msg_2update(int threshold, int msg_type) {
    unsigned int proc_num = 0, pos;
    process_info_t *proc = NULL;
    boolean status_ok = FALSE;

    /* 通过进程心跳，判断软件运行是否正常 */
    proc_num = g_list_length(proc_start_list);
    for (pos = 0; pos < proc_num; pos++) {
        proc = (process_info_t *)g_list_nth_data(proc_start_list, (guint)pos);
        if (proc == NULL) return FALSE;
        /* 判断软件运行状态 */
        if (proc->pulse_cnt < threshold) {
            break;
        }
    }

    /* 所有进程心跳计数大于阈值，状态正常 */
    if (pos >= proc_num) {
        status_ok = TRUE;
        if (PROC_NORMAL_CNT == threshold) {
            proc_status = PROC_SUCCESS;
        }

        send_asy_msg((msgid)msg_type, NULL, 0,
                     PCHAR_TO_MID(MID_SYSMONITOR_PROC_MANAGE),
                     PCHAR_TO_MID(MID_SYSMONITOR_PROG_UPDATE));
    }

    return status_ok;
}

static void check_prog_status(void) {
    static boolean prog_start_msg_send = FALSE;
    static boolean prog_normal_msg_send = FALSE;

    /* 消息发送过，不再重复发送*/
    if (prog_start_msg_send && prog_normal_msg_send) {
        s_begin_check_system_resources = TRUE; // 所有应用正常运行，cpu平稳下来，开启cpu、内存使用率监控
        kill_timer(PCHAR_TO_MID(MID_SYSMONITOR_PROC_MANAGE), TIMER2);
        return;
    }

    /* 检查所有进程是否均正常启动、软件是否运行正常*/
    if (!prog_start_msg_send) {
        prog_start_msg_send = send_msg_2update(PROC_START_CNT, SYS_PROG_START_MSG);
    } else if (!prog_normal_msg_send) {
        prog_normal_msg_send = send_msg_2update(PROC_NORMAL_CNT, SYS_PROG_BAKUP_MSG);
    }
}


static void generate_crypt_key_to_shm(void) {
    char csu_id[CSU_ID_LEN + 1] = {0};
    char *root_crypt_key = NULL;

    if (create_share_memory_crypt_key(TRUE) == FAILURE) {
        SYSLOG_NOTICE("creat share memory of crypt_key fail!");
    }
    if (get_csu_id(csu_id) == FAILURE) {
        SYSLOG_NOTICE("get csu_id fail!");
    }
    root_crypt_key = generate_root_crypt_key(csu_id, CSU_ID_LEN, KEY_PART_2, KEY_PART_2_LEN);
    if(root_crypt_key == NULL){
        return;
    }
    if (update_crypt_key(root_crypt_key) == FAILURE) {
        SYSLOG_NOTICE("write root_crypt_key to shm fail!");
    }
    g_free(root_crypt_key);
    root_crypt_key = NULL;
}


static void manage_process_common(msgid msg_id, msgsn msg_sn, const void *msg,
                                 unsigned int msg_len, const mid sender) {
    manage_ack_t    ack_msg;

    RETURN_IF_FAIL(msg != NULL);
    RETURN_IF_FAIL(msg_len == sizeof(manage_req_t));
    const manage_req_t* manage_req = (const manage_req_t *)msg;

    plat_memset_s(&ack_msg, sizeof(ack_msg), 0, sizeof(ack_msg));
    memcpy_s(&ack_msg.req, msg_len, manage_req, msg_len);
    switch (manage_req->set_mode) {
        case set_stop_process:
            ack_msg.ret = handle_stop_proccess_req(STR_TO_PCHAR(manage_req->proc_name));
            break;

        case set_start_process:
            ack_msg.ret = handle_start_proccess_req(STR_TO_PCHAR(manage_req->proc_name));
            break;

        case set_restart_process:
            ack_msg.ret = handle_restart_proccess_req(STR_TO_PCHAR(manage_req->proc_name));
            break;

        default:
            ack_msg.ret = FAILURE;
            break;
    }

    send_syn_ack(msg_id, msg_sn, &ack_msg, sizeof(ack_msg));
}



static int handle_start_proccess_req(char* proc_name) {
    GList *  proc_list = NULL;
    int rtn = SUCCESS;
    process_info_t* proc_info_old = NULL;
    process_info_t* proc_info_new = NULL;

    /* 进程正在运行，无需启动直接返回成功 */
    if (if_process_running(proc_name) == SUCCESS)
        return rtn;

    /* 进程未运行， 重新读取进程启动信息 */
    proc_info_new = (process_info_t*)g_malloc0((gsize)(sizeof(process_info_t)));
    g_assert(proc_info_new != NULL);
    if (reload_proc_start_info(proc_name, proc_info_new) != SUCCESS) {
        g_free(proc_info_new);
        return FAILURE;
    }

    /* 进程在启动列表中能找到，更新进程启动信息并启动进程 */
    proc_list = g_list_find_custom(proc_start_list, proc_name, proc_cmp);
    if (proc_list != NULL) {
        proc_info_old = (process_info_t *)g_list_nth_data(proc_list, (guint)0);
        plat_memcpy_s(proc_info_old, sizeof(process_info_t), proc_info_new, sizeof(process_info_t));
        start_process(proc_info_old);
        g_free(proc_info_new);
        return SUCCESS;
    }

    /* 进程在启动列表中找不到，加载启动信息加载到链表并启动进程 */
    proc_start_list = g_list_append(proc_start_list, proc_info_new);
    start_process(proc_info_new);

    return rtn;
}

static int handle_restart_proccess_req(char* proc_name) {
    process_info_t proc_info_local;

    /* 进程正在运行，先杀死 */
    if (if_process_running(proc_name) == SUCCESS) {
        snprintf_s_void(proc_info_local.name, sizeof(proc_info_local.name), "%s", proc_name);
        if (kill_process(&proc_info_local) != SUCCESS)
            return FAILURE;
    }

    return handle_start_proccess_req(proc_name);
}

static int handle_stop_proccess_req(char* proc_name) {
    GList *  proc_list = NULL;
    int rtn = SUCCESS;
    process_info_t* proc_info = NULL;
    process_info_t proc_info_local;

    /* 进程未运行，无需结束进程 */
    if (SUCCESS == if_process_running(proc_name)) {
        snprintf_s_void(proc_info_local.name, sizeof(proc_info_local.name), "%s", proc_name);
        rtn = kill_process(&proc_info_local);
    }

    /* 进程运行中，且在启动链表中找到，从链表中删除该进程 */
    proc_list = g_list_find_custom(proc_start_list, proc_name, proc_cmp);
    if (proc_list != NULL) {
        proc_info = (process_info_t *)g_list_nth_data(proc_list, (guint)0);
        proc_start_list = g_list_remove(proc_start_list, proc_info);
    }

    return rtn;
}


static int reload_proc_start_info(char* proc_name, process_info_t* process_info) {
    xmlDocPtr proc_xml_doc = NULL;
    xmlNodePtr proc_xml_node = NULL;
    char file_path[128] = {0};
    char xpath[128] = {0};
    int rtn = SUCCESS;

    get_file_path(STR_TO_PCHAR(file_path), (unsigned int)(sizeof(file_path)));

    proc_xml_doc = xmlParseFile((const char*)file_path);
    if (proc_xml_doc == NULL) {
        return FAILURE;
    }

    snprintf_s_void(xpath, sizeof(xpath), "/config/process[@name='%s']", proc_name);
    proc_xml_node = get_node_by_path((xmlChar *)xpath, proc_xml_doc);
    if (proc_xml_node == NULL) {
        xmlFreeDoc(proc_xml_doc);
        return FAILURE;
    }

    rtn = get_one_proc_conf(proc_xml_node, process_info);
    xmlFreeDoc(proc_xml_doc);
    return rtn;
}


static xmlNodePtr get_node_by_path(const xmlChar *xpath, xmlDocPtr doc) {
    xmlXPathContextPtr context;
    xmlXPathObjectPtr nodeset;
    xmlNodePtr paranode = NULL;

    context = xmlXPathNewContext(doc);
    RETURN_VAL_IF_FAIL(NULL != context, NULL);

    nodeset = xmlXPathEvalExpression(xpath, context);
    xmlXPathFreeContext(context);
    RETURN_VAL_IF_FAIL(NULL != nodeset, NULL);

    if (!xmlXPathNodeSetIsEmpty(nodeset->nodesetval)) {
        paranode = nodeset->nodesetval->nodeTab[0];
    }
    xmlXPathFreeObject(nodeset);

    return paranode;
}


static void sync_snmp_enable_flag(void) {
    char xpath[128] = {0};
    xmlDocPtr cfg_xml_doc = NULL;
    xmlNodePtr snmp_enable_node = NULL;
    xmlChar *xml_node_val = NULL;
    int enable = 0;
    char cmd[128] = {0};

    /* 运行态配置文件不存在，不作处理*/
    if (access(PATH_DATA_CONFIG"config.xml", F_OK) != 0) {
        snprintf_s_void(STR_TO_PCHAR(cmd), sizeof(cmd), "cp %s %s", PROC_CONF_EXCLUDE_SNMP_FILE, PROC_CONF_FILE);
        system_s(STR_TO_PCHAR(cmd));
        SYSLOG_NOTICE("running config file  not exist");
        return;
    }


    /* 运行态配置文件访问异常，不作处理*/
    cfg_xml_doc = xmlParseFile((const char*)PATH_DATA_CONFIG"config.xml");
    if (cfg_xml_doc == NULL) {
        return;
    }

    /* snmp使能标识读取异常，不作处理*/
    snprintf_s_void(xpath, sizeof(xpath), "/config/cfgobject[@id='plat.snmp']/para[@name='SNMP Enable']");
    snmp_enable_node = get_node_by_path((xmlChar *)xpath, cfg_xml_doc);
    if (!snmp_enable_node) {
        xmlFreeDoc(cfg_xml_doc);
        return;
    }
    xml_node_val = xmlNodeGetContent(snmp_enable_node);
    if (!xml_node_val) {
        xmlFreeDoc(cfg_xml_doc);
        return;
    }
    enable = atoi((char*)xml_node_val);
    xmlFree(xml_node_val);
    xmlFreeDoc(cfg_xml_doc);

    /* 根据snmp使能标识，同步进程启动文件*/
    if (enable) {
        snprintf_s_void(STR_TO_PCHAR(cmd), sizeof(cmd), "cp %s %s", PROC_CONF_INCLUDE_SNMP_FILE, PROC_CONF_FILE);
    } else {
        snprintf_s_void(STR_TO_PCHAR(cmd), sizeof(cmd), "cp %s %s", PROC_CONF_EXCLUDE_SNMP_FILE, PROC_CONF_FILE);
    }

    system_s(STR_TO_PCHAR(cmd));
}

static void deliver_proc_status(msgid msg_id, msgsn msg_sn) {
    int ret;
    char proc_stat_tmp = proc_status & 0xFF;
    ret = send_syn_ack(msg_id, msg_sn, &proc_stat_tmp, (unsigned int)sizeof(proc_stat_tmp));
    if (ret != SUCCESS) {
        SYSLOG_NOTICE("deliver process status [%d] failure!", proc_stat_tmp);
    }

    return;
}

/* Started by AICoder, pid:l2945pb63a503eb145ea0a9cb0d71a04b6c139cf */
STATIC boolean is_start_system_resource_check(void) {
    return s_begin_check_system_resources;
}
/* Ended by AICoder, pid:l2945pb63a503eb145ea0a9cb0d71a04b6c139cf */

/* Started by AICoder, pid:q3bf30d990le5ab1490108d8909ea8014a717448 */
STATIC int initialize_watchdog(void) {
    struct stat tStatBuf;
    if (s_slWdt_fd < 0 || fstat(s_slWdt_fd, &tStatBuf) < 0) {
        s_slWdt_fd = hal_open(WATCHDOG_DEV_PATH, O_WRONLY);
        if(s_slWdt_fd < 0 && errno == EBUSY) {
            /* 设备被占用，清理占用的看门狗进程 */
            SYSLOG_NOTICE("Watchdog device busy, clearing occupying process");
            system("fuser -k /dev/watchdog 2>/dev/null");
            sleep(1);

            /* 重新尝试打开 */
            s_slWdt_fd = hal_open(WATCHDOG_DEV_PATH, O_WRONLY);
        }

        if(s_slWdt_fd < 0) {
            SYSLOG_NOTICE("set_watchdog_timeout open %s faild %d\n", WATCHDOG_DEV_PATH, s_slWdt_fd);
            return FAILURE;
        }
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:q3bf30d990le5ab1490108d8909ea8014a717448 */

/* Started by AICoder, pid:z06a8s7da0743f814fd2083cc006550935d11f80 */
STATIC int set_watchdog_timeout(int timeout) {
    int s_watchdog_timeout = 0;
    int ret = SUCCESS;
    ret = initialize_watchdog();
    if (SUCCESS != ret) 
    {
        return ret;
    }

    ret = hal_ioctl(s_slWdt_fd, WDIOC_GETTIMEOUT, &s_watchdog_timeout); 
    if (SUCCESS != ret)
    {
        SYSLOG_NOTICE("Get watch dog timeout faild: %d", ret);
        return FAILURE;
    }

    SYSLOG_NOTICE("Get System Watchdog timeout is %d", s_watchdog_timeout);

    if (s_watchdog_timeout < timeout) {
        ret = hal_ioctl(s_slWdt_fd, WDIOC_SETTIMEOUT, &timeout);
    }
    
    return ret;
}
/* Ended by AICoder, pid:z06a8s7da0743f814fd2083cc006550935d11f80 */

/* Started by AICoder, pid:r1e86m8290k45a314ad90afc5036e209ac51f34b */
STATIC int feed_watchdog(void) {
    int ret = SUCCESS;

    ret = initialize_watchdog();
    if (SUCCESS != ret) {
        return ret;
    }

    ret = hal_ioctl(s_slWdt_fd, WDIOC_KEEPALIVE, NULL);
    return ret;
}
/* Ended by AICoder, pid:r1e86m8290k45a314ad90afc5036e209ac51f34b */

/* Started by AICoder, pid:a49e7156a19379d14ecf0bba60e71f427494bc2c */
STATIC int check_system_resource_usage(void)
{
    if (is_start_system_resource_check() == FALSE) 
    {
        return SUCCESS;
    }
    float cpu_usage = get_system_cpu_usage();
    float mem_usage = get_system_mem_usage();
    high_resource_usage_event_e usage_event = HIGH_CPU;

    if (cpu_usage < 0 || mem_usage < 0)
    {
        SYSLOG_NOTICE("Failed to get system resource usage");
        return FAILURE;
    }

    // Check CPU usage
    if (cpu_usage >= HIGH_CPU_THRESHOLD) 
    {
        s_high_cpu_times++;
    } else 
    {
        s_high_cpu_times = 0; // 重置防止CPU短时间占用过高，误判断为高资源占用
    }

    // Check memory usage
    if (mem_usage >= HIGH_MEM_THRESHOLD) 
    {
        s_high_mem_times++;
    } else 
    {
        s_high_mem_times = 0; // 重置防止内存短时间占用过高，误判断为高资源占用
    }

    // Handle high resource usage

    if (s_high_mem_times >= HIGH_RESOURCE_USAGE_TIME)
    {
       usage_event = HIGH_MEM;
    }

    if (s_high_cpu_times >= HIGH_RESOURCE_USAGE_TIME || s_high_mem_times >= HIGH_RESOURCE_USAGE_TIME)
    {
        save_high_resources_event(usage_event);
        hal_close(&s_slWdt_fd);
        restart_all_apps();
    }

    return SUCCESS;
}
/* Ended by AICoder, pid:a49e7156a19379d14ecf0bba60e71f427494bc2c */

 /* Started by AICoder, pid:dfd29j5766g8d8014ec50aa50049fc0f790156c2 */
STATIC int restart_all_apps(void)
{
    str256 cmd = {'\0'};
    snprintf_s_void(STR_TO_PCHAR(cmd), sizeof(str256), "%s", PROC_SOFTWARE_RESTART_SCRIPT);
    system_s(cmd);  
    return SUCCESS;
}
/* Ended by AICoder, pid:dfd29j5766g8d8014ec50aa50049fc0f790156c2 */

/* Started by AICoder, pid:1db87bb24ddae1114d6708a6e03d130add01d619 */
STATIC int save_high_resources_event(int resource_id)
{
    int set_sta = 0;
    str64 str_before = {'\0'}, str_now = {'\0'};   
    history_event_t hisevent;
    event_operator_t operator_info_system = {"System", "", ""};

    if (resource_id == HIGH_CPU)
    {
        snprintf_s_void(STR_TO_PCHAR(str_before), sizeof(str64), "%s", "cpu");
        snprintf_s_void(STR_TO_PCHAR(str_now), sizeof(str64), "%d%%", HIGH_CPU_THRESHOLD);
    }else if (resource_id == HIGH_MEM)
    {
        snprintf_s_void(STR_TO_PCHAR(str_before), sizeof(str64), "%s", "mem");
        snprintf_s_void(STR_TO_PCHAR(str_now), sizeof(str64), "%d%%", HIGH_MEM_THRESHOLD);
    }else 
    {
        return FAILURE;
    }

    // 初始化历史事件结构
    plat_memset_s(&hisevent, sizeof(history_event_t), 0, sizeof(history_event_t));
    hisevent.type = HISTORY_EVENT_TYPE_OPERATE;
    plat_memcpy_s(&hisevent.operator_info, sizeof(hisevent.operator_info), &operator_info_system, sizeof(hisevent.operator_info));

    // 写入事件内容
    write_event_content(&hisevent.content_info, "Restart all apps, ", str_before, " high ", str_now, NULL, NULL, NULL);

    // 保存事件到数据库
    set_sta = set_history_event_2_db(&hisevent);
    return set_sta;
}
/* Ended by AICoder, pid:1db87bb24ddae1114d6708a6e03d130add01d619 */