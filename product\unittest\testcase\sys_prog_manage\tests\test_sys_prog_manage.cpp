/**
 * @file     test_sys_prog_manage.cpp
 * @brief    test_sys_prog_manage.cpp
 * @details  test_sys_prog_manage.cpp
 * <AUTHOR> 
 * @date     2017-03-20
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 

/*  includes  */
#include <glib.h>
#include <mockcpp/mokc.h>
#include "gtest/gtest.h"
#include <libxml/xpath.h>
#include <libxml/parser.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include "mock_sys_prog_manage.h"

// xmlNodePtr xml_node_tmp = NULL;

class ENV : public testing::Environment {
 public:
    virtual void SetUp() {
        // SetUp run once before all testcases
        // you can add mock group here
    }
    virtual void TearDown() {
        // TearDown run once before all testcases
    }
};

class sys_prog_manage : public testing::Test {
 protected:
    static void SetUpTestCase() {
        // SetUpTestCase run once before testcase groups named by sys_prog_manage
    }

    static void TearDownTestCase() {
        // TearDownTestCase run once before testcase groups named by sys_prog_manage
    }

    virtual void SetUp() {
        // SetUp run before every testcase of group sys_prog_manage
    }

    virtual void TearDown() {
        // SetUp run before every testcase of group sys_prog_manage
    }

    // Some expensive resource shared by all tests.
    // int share_variable ;
};

// init class sys_prog_manage variable here
// int sys_prog_manage::share_variable = 0;

// add testcase below

TEST_F(sys_prog_manage, sysmonitor_proc_manage_init_tmp) {
    xmlNodePtr xml_node_tmp1 = NULL;
    xmlDocPtr xml_doc_tmp1 = NULL;
    manage_req_t msg_tmp;
    system("echo '<'\?xml version=\'\"\'1.0\'\"\' encoding=\'\"\'utf-8\'\"\'\?'>' > proc_conf1.xml");
    system("echo '<'config'>' >> proc_conf1.xml");
    system("echo '<'cfgobject id=\'\"\'plat.snmp\'\"\' name=\'\"\'SNMP Settings\'\"\' type=\'\"\'single\'\"\'"\
           "'>' >> proc_conf1.xml");
    system("echo '<'para id=\'\"\'snmp_enable\'\"\' name=\'\"\'SNMP Enable\'\"\' type=\'\"\'int\'\"\'"\
           "'>' >> proc_conf1.xml");
    system("echo '<'min'>'0'<'/min'>' >> proc_conf1.xml");
    system("echo '<'max'>'1'<'/max'>' >> proc_conf1.xml");
    system("echo '<'precision'>'0'<'/precision'>' >> proc_conf1.xml");
    system("echo '<'step'>''<'/step'>' >> proc_conf1.xml");
    system("echo '<'visible'>'YES'<'/visible'>' >> proc_conf1.xml");
    system("echo '<'full_name'>'SNMP Enable'<'/full_name'>' >> proc_conf1.xml");
    system("echo '<'short_name'>'SNMP Enable'<'/short_name'>' >> proc_conf1.xml");
    system("echo '<'convention'>'0:Disabled,1:Enabled'<'/convention'>' >> proc_conf1.xml");
    system("echo '<'default'>'0'<'/default'>' >> proc_conf1.xml");
    system("echo '<'step'>''<'/step'>' >> proc_conf1.xml");
    system("echo '<'/para'>' >> proc_conf1.xml");
    system("echo '<'/cfgobject'>' >> proc_conf1.xml");
    system("echo '<'/config'>' >> proc_conf1.xml");
    xml_doc_tmp1 = xmlParseFile("proc_conf1.xml");
    xml_node_tmp1 = xmlDocGetRootElement(xml_doc_tmp1);
    MOCKER(access)
        .stubs()
        .will(returnValue(FAILURE))
        .then(returnValue(SUCCESS));
    MOCKER(xmlParseFile)
        .stubs()
        .will(returnValue((xmlDocPtr)NULL))
        .then(returnValue((xmlDocPtr)NULL))
        .then(returnValue((xmlDocPtr)NULL))
        .then(returnValue(xml_doc_tmp1))
        .then(returnValue((xmlDocPtr)NULL));
    MOCKER(xmlDocGetRootElement)
        .stubs()
        .will(returnValue((xmlNodePtr)NULL));
    MOCKER(atoi)
        .stubs()
        .will(returnValue(SUCCESS));

    EXPECT_EQ(0, sysmonitor_proc_manage_init());
    EXPECT_EQ(0, sysmonitor_proc_manage_init());
    EXPECT_EQ(0, sysmonitor_proc_manage_init());
    system("rm -f proc_conf1.xml");
    GlobalMockObject::verify();
}

xmlNodePtr xml_node_tmp = NULL;
xmlDocPtr xml_doc_tmp = NULL;
TEST_F(sys_prog_manage, sysmonitor_proc_manage_init) {
    system("echo '<'\?xml version=\'\"\'1.0\'\"\' encoding=\'\"\'utf-8\'\"\'\?'>''<'config'>'"\
    "'<'process path=\'\"\'/root/power/bin\'\"\' name=\'\"\'mainapp\'\"\' timeout=\'\"\'300\'\"\' "\
            "wait_msg=\'\"\'yes\'\"\' is_pulse=\'\"\'1\'\"\' arg=\'\"\'\'\"\''>''<'/process'>' > proc_conf2.xml");
    system("echo '<'process path=\'\"\'/root/power/etc/lighttpd/bin\'\"\' name=\'\"\'lighttpd\'\"\' timeout=\'\"\'90\'\"\' "\
            "wait_msg=\'\"\'yes\'\"\' is_pulse=\'\"\'0\'\"\' arg=\'\"\'-f /root/power/etc/lighttpd/config/lighttpd.conf -m "\
            "/root/power/etc/lighttpd/lib\'\"\''>''<'/process'>' >> proc_conf2.xml");
    system("echo '<'process path=\'\"\'/root/power/bin\'\"\' name=\'\"\'netmgr\'\"\' timeout=\'\"\'120\'\"\' "\
            "wait_msg=\'\"\'\'\"\' is_pulse=\'\"\'1\'\"\' arg=\'\"\'\'\"\''>''<'/process'>' >> proc_conf2.xml");
    system("echo '<'process path=\'\"\'/root/power/bin\'\"\' name=\'\"\'productapp\'\"\' timeout=\'\"\'0\'\"\' "\
            "wait_msg=\'\"\'yes\'\"\' is_pulse=\'\"\'1\'\"\' arg=\'\"\'\'\"\''>''<'/process'>' >> proc_conf2.xml");
    system("echo '<'process path=\'\"\'/root/power/bin\'\"\' name=\'\"\'aidido\'\"\' timeout=\'\"\'120\'\"\' "\
            "wait_msg=\'\"\'yes\'\"\' is_pulse=\'\"\'1\'\"\' arg=\'\"\'\'\"\''>''<'/process'>' >> proc_conf2.xml");
    system("echo '<'/config'>' >> proc_conf2.xml");
    xml_doc_tmp = xmlParseFile("proc_conf2.xml");
    xml_node_tmp = xmlDocGetRootElement(xml_doc_tmp);
    EXPECT_EQ(0, sysmonitor_proc_manage_init());
    MOCKER(xmlDocGetRootElement)
        .stubs()
        .will(returnValue(xml_node_tmp));
    EXPECT_EQ(0, sysmonitor_proc_manage_init());
    system("rm -f proc_conf2.xml");
    GlobalMockObject::verify();
}

TEST_F(sys_prog_manage, sysmonitor_proc_manage_main) {
    char mainapp_msg[STR_LEN_16] = {0};
    manage_req_t msg_tmp;
    strlcpy_s(mainapp_msg, "mainapp", sizeof(mainapp_msg));
    msg_tmp.set_mode = 0;
    strlcpy_s(msg_tmp.proc_name, "mainapp", sizeof(msg_tmp.proc_name));
    MOCKER(fork)
        .stubs()
        .will(returnValue((int)TRUE))
        .then(returnValue((int)FALSE));
    MOCKER(access)
        .stubs()
        .will(returnValue(SUCCESS));
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_START_MSG,
                                             0,
                                             mainapp_msg,
                                             sizeof(str16),
                                             MAINAPP));

    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_KILL_MSG,
                                             0,
                                             mainapp_msg,
                                             sizeof(str16),
                                             MAINAPP));


    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_ALIVE_MSG,
                                             0,
                                             mainapp_msg,
                                             sizeof(str16),
                                             MAINAPP));

    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_WEB_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    msg_tmp.set_mode = 1;
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_WEB_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    msg_tmp.set_mode = 1;
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_COMMON_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    msg_tmp.set_mode = 2;
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_COMMON_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    msg_tmp.set_mode = 3;
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_COMMON_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    msg_tmp.set_mode = 0;
    MOCKER(if_process_running)
        .stubs()
        .will(returnValue(FAILURE))
        .then(returnValue(SUCCESS));
    MOCKER(xmlParseFile)
        .stubs()
        .will(returnValue(xml_doc_tmp));
    memset(msg_tmp.proc_name, 0, sizeof(msg_tmp.proc_name));
    strlcpy_s(msg_tmp.proc_name, "netmgr", sizeof(msg_tmp.proc_name));
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_COMMON_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    EXPECT_EQ(0, sysmonitor_proc_manage_main(TIMER1_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    EXPECT_EQ(0, sysmonitor_proc_manage_main(TIMER2_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_GET_RUNNING_STATE_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    EXPECT_EQ(0, sysmonitor_proc_manage_main(MSG_SYSMONITOR_PROC_MANAGE_START + 7,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));

    GlobalMockObject::reset();
    GList test_glist1 = {0};
    process_info_t  test_process_info1 = {0};

    MOCKER(fork).stubs().will(returnValue((int)TRUE)).then(returnValue((int)FALSE));
    MOCKER(access).stubs().will(returnValue(SUCCESS));
    MOCKER(if_process_running).stubs().will(returnValue(FAILURE));
    MOCKER(fopen).stubs().then(returnValue((FILE*)1));
    MOCKER(fclose).stubs().will(returnValue(0)).then(returnValue(-1));
    MOCKER(remove).stubs().will(returnValue(0));
    MOCKER(g_list_find_custom).stubs().will(returnValue(&test_glist1));
    MOCKER(g_list_nth_data).stubs().will(returnValue(((void *)&test_process_info1)));
    MOCKER(start_process).stubs().will(returnValue(1));
    msg_tmp.set_mode = 0;
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_WEB_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_WEB_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));
    GlobalMockObject::reset();

    MOCKER(fork).stubs().will(returnValue((int)TRUE)).then(returnValue((int)FALSE));
    MOCKER(access).stubs().will(returnValue(SUCCESS));
    MOCKER(if_process_running).stubs().will(returnValue(SUCCESS));
    MOCKER(fopen).stubs().then(returnValue((FILE*)1));
    MOCKER(fclose).stubs().will(returnValue(0)).then(returnValue(-1));
    MOCKER(remove).stubs().will(returnValue(0));
    MOCKER(g_list_find_custom).stubs().will(returnValue(&test_glist1));
    MOCKER(g_list_nth_data).stubs().will(returnValue(((void *)&test_process_info1)));
    MOCKER(kill_process).stubs().will(returnValue(1));
    msg_tmp.set_mode = 1;
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_WEB_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));
    EXPECT_EQ(0, sysmonitor_proc_manage_main(SYS_PROC_MANAGE_WEB_MSG,
                                             0,
                                             &msg_tmp,
                                             sizeof(manage_req_t),
                                             MAINAPP));
    GlobalMockObject::reset();
                                            
}

TEST_F(sys_prog_manage, handle_process_exception) {
    FILE *fd = NULL;
    char buff[256] = "State:  D";
    unsigned int die_cnt = 1;
    FILE fp;
    fd = fopen("./test.txt", "w+");
    fwrite(buff, sizeof(buff), 1, fd);
    MOCKER(fopen)
        .stubs()
        .will(returnValue(fd));
    MOCKER(fgets)
        .stubs()
        .will(invoke(fgets_stub));
    MOCKER(system_s)
        .stubs()
        .will(invoke(system_s_stub));
    MOCKER(fclose)
        .stubs()
        .will(returnValue((int)0));
    MOCKER(judge_process_alive)
        .stubs()
        .will(returnValue(boolean(TRUE)))
        .then(returnValue(boolean(FALSE)));
    MOCKER(is_file_exist)
        .stubs()
        .will(returnValue(boolean(TRUE)))
        .then(returnValue(boolean(FALSE)));


    EXPECT_EQ(0, handle_process_exception(die_cnt, "netmgr"));
    EXPECT_EQ(0, handle_process_exception(die_cnt, "netmgr"));

    GlobalMockObject::reset();
}

TEST_F(sys_prog_manage, del_proc_d_status_reset_file) {
    MOCKER(is_file_exist)
        .stubs()
        .will(returnValue(boolean(TRUE)))
        .then(returnValue(boolean(FALSE)));

    EXPECT_EQ(0, del_proc_d_status_reset_file());

    GlobalMockObject::verify();
}

TEST_F(sys_prog_manage, sysmonitor_proc_manage_main_TIMER4_MSG) {
    int i = 0;
    MOCKER(system_s)
    .stubs()
    .will(returnValue(FAILURE));
    EXPECT_EQ(SUCCESS, sysmonitor_proc_manage_main(TIMER4_MSG, 0, NULL, 0, MID_INVALID));
    MOCKER(open_file_stream)
    .stubs()
    .will(invoke(mock_open_yaffs_file));
    EXPECT_EQ(SUCCESS, sysmonitor_proc_manage_main(TIMER4_MSG, 0, NULL, 0, MID_INVALID));
    GlobalMockObject::verify();
    MOCKER(system_s)
    .stubs()
    .will(returnValue(FAILURE));
    MOCKER(open_file_stream)
    .stubs()
    .will(invoke(mock_open_yaffs_file_0));
    for (i = 0; i < 5; i++) {
        EXPECT_EQ(SUCCESS, sysmonitor_proc_manage_main(TIMER4_MSG, 0, NULL, 0, MID_INVALID));
    }
    GlobalMockObject::verify();
    MOCKER(system_s)
    .stubs()
    .will(returnValue(FAILURE));
    MOCKER(open_file_stream)
    .stubs()
    .will(invoke(mock_open_yaffs_file_no_data));
    EXPECT_EQ(SUCCESS, sysmonitor_proc_manage_main(TIMER4_MSG, 0, NULL, 0, MID_INVALID));
    MOCKER(is_ti_cpu)
    .stubs()
    .will(returnValue(FAILURE));
    EXPECT_EQ(SUCCESS, sysmonitor_proc_manage_main(TIMER4_MSG, 0, NULL, 0, MID_INVALID));
    GlobalMockObject::verify();
}

/* Started by AICoder, pid:wc64cc9d163c3ec14f6d09f7900c260401428d5d */
TEST_F(sys_prog_manage, initialize_watchdog) {
    MOCKER(fstat).stubs().will(returnValue(-1)).then(returnValue(1)).then(returnValue(-1)).then(returnValue(1));
    set_hal_open(0);
    EXPECT_EQ(initialize_watchdog(), FAILURE);
    EXPECT_EQ(initialize_watchdog(), FAILURE);
    set_hal_open(1);
    EXPECT_EQ(initialize_watchdog(), SUCCESS);
    EXPECT_EQ(initialize_watchdog(), SUCCESS);
    EXPECT_EQ(initialize_watchdog(), SUCCESS);
    GlobalMockObject::reset();
}
/* Ended by AICoder, pid:wc64cc9d163c3ec14f6d09f7900c260401428d5d */

/* Started by AICoder, pid:i591f49f84ob2ca14ee70afce06ccf1191f1f15e */
TEST_F(sys_prog_manage, set_watchdog_timeout) {
   MOCKER(initialize_watchdog).stubs().will(returnValue((int)FAILURE));
   EXPECT_EQ(set_watchdog_timeout(1), FAILURE); 
   GlobalMockObject::reset();
   MOCKER(initialize_watchdog).stubs().will(returnValue((int)SUCCESS)); 
   MOCKER(hal_ioctl).stubs().will(returnValue((int)FAILURE)).then(returnValue((int)FAILURE));
   EXPECT_EQ(set_watchdog_timeout(1), FAILURE); 
   GlobalMockObject::reset();
   MOCKER(initialize_watchdog).stubs().will(returnValue((int)SUCCESS)); 
   MOCKER(hal_ioctl).stubs().will(returnValue((int)SUCCESS)).then(returnValue((int)FAILURE));
   EXPECT_EQ(set_watchdog_timeout(10000), FAILURE); 
   GlobalMockObject::reset();
}
/* Ended by AICoder, pid:i591f49f84ob2ca14ee70afce06ccf1191f1f15e */

/* Started by AICoder, pid:64a5a2cd0c2767114c530be9b0b8961c0ee0b1a8 */
TEST_F(sys_prog_manage, feed_watchdog) {
    MOCKER(initialize_watchdog).stubs().will(returnValue((int)FAILURE)).then(returnValue((int)SUCCESS));
    MOCKER(hal_ioctl).stubs().will(returnValue(SUCCESS));
    EXPECT_EQ(feed_watchdog(), FAILURE);
    EXPECT_EQ(feed_watchdog(), SUCCESS);
    GlobalMockObject::reset();
}
/* Ended by AICoder, pid:64a5a2cd0c2767114c530be9b0b8961c0ee0b1a8 */

TEST_F(sys_prog_manage, close_watchdog) {
    MOCKER(fstat).stubs().will(returnValue(-1)).then(returnValue(1)).then(returnValue(-1)).then(returnValue(1));
    set_hal_open(0);
    EXPECT_EQ(close_watchdog(), FAILURE);
    EXPECT_EQ(close_watchdog(), FAILURE);
    set_hal_open(1);
    MOCKER(hal_ioctl).stubs().will(returnValue(SUCCESS)).then(returnValue(FAILURE)).then(returnValue(SUCCESS));
    EXPECT_EQ(close_watchdog(), SUCCESS);
    EXPECT_EQ(close_watchdog(), FAILURE);
    EXPECT_EQ(close_watchdog(), SUCCESS);
    set_hal_open(0);
    GlobalMockObject::reset();
}

TEST_F(sys_prog_manage, reg_sigterm) {
    EXPECT_NO_FATAL_FAILURE(reg_sigterm());
}

/* Started by AICoder, pid:61620ja3016d21014ce508184069901a5cb025cd */
TEST_F(sys_prog_manage, check_system_resource_usage) {
    MOCKER(is_start_system_resource_check).stubs().will(returnValue((int)FALSE));
    EXPECT_EQ(check_system_resource_usage(), SUCCESS);
    GlobalMockObject::reset();      
    MOCKER(is_start_system_resource_check).stubs().will(returnValue((int)TRUE));   
    MOCKER(get_system_cpu_usage).stubs().will(returnValue((float)-1));
    MOCKER(get_system_mem_usage).stubs().will(returnValue((float)-1));
    EXPECT_EQ(check_system_resource_usage(), FAILURE);
    GlobalMockObject::reset();
    MOCKER(is_start_system_resource_check).stubs().will(returnValue((int)TRUE));
    MOCKER(get_system_cpu_usage).stubs().will(returnValue((float)-1));
    EXPECT_EQ(check_system_resource_usage(), FAILURE);
    GlobalMockObject::reset();
    MOCKER(is_start_system_resource_check).stubs().will(returnValue((int)TRUE));
    MOCKER(get_system_mem_usage).stubs().will(returnValue((float)-1));
    EXPECT_EQ(check_system_resource_usage(), FAILURE);
    GlobalMockObject::reset();
    MOCKER(is_start_system_resource_check).stubs().will(returnValue((int)TRUE));
    MOCKER(save_high_resources_event).stubs().will(returnValue(SUCCESS));
    MOCKER(restart_all_apps).stubs().will(returnValue(SUCCESS));
    EXPECT_EQ(check_system_resource_usage(), SUCCESS);
    GlobalMockObject::reset();
    MOCKER(is_start_system_resource_check).stubs().will(returnValue((int)TRUE));
    MOCKER(get_system_cpu_usage).stubs().will(returnValue((float)99));
    MOCKER(get_system_mem_usage).stubs().will(returnValue((float)98));
    int i = 0;
    for(i = 0; i < 30; i++){
        check_system_resource_usage();
    }
    EXPECT_EQ(check_system_resource_usage(), SUCCESS);
    GlobalMockObject::reset();
}
/* Ended by AICoder, pid:61620ja3016d21014ce508184069901a5cb025cd */

/* Started by AICoder, pid:d299e56425x3d5c14076088340595f16f401661e */
TEST_F(sys_prog_manage, restart_all_apps) {
    EXPECT_EQ(restart_all_apps(), SUCCESS);
}
/* Ended by AICoder, pid:d299e56425x3d5c14076088340595f16f401661e */

/* Started by AICoder, pid:s84d4cf03895755145760b5610abaa15ca90d1ab */
TEST_F(sys_prog_manage, save_high_resources_event) {
    EXPECT_EQ(save_high_resources_event(HIGH_CPU), SUCCESS);
    EXPECT_EQ(save_high_resources_event(HIGH_MEM), SUCCESS);
    EXPECT_EQ(save_high_resources_event(3), FAILURE);
}
/* Ended by AICoder, pid:s84d4cf03895755145760b5610abaa15ca90d1ab */

/* Started by AICoder, pid:c039ecb142v1d521456e08d8b0f1720beb894132 */
TEST_F(sys_prog_manage, is_start_system_resource_check) {
    EXPECT_EQ(is_start_system_resource_check(), FALSE);
}
/* Ended by AICoder, pid:c039ecb142v1d521456e08d8b0f1720beb894132 */

int main(int argc, char *argv[]) {
    testing::AddGlobalTestEnvironment(new ENV);
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

