// 
/*****************************************************************************
 模块名      :  bmu业务模块
 文件名      :  bmu_business.c
 内容摘要    : 	主要用于是实现BMU电池业务
 版本        : 	V1.0
*****************************************************************************/
#include <stdlib.h>
#include <unistd.h>
#include <math.h>
#include "oss/plat_oss.h"
#include "plat_error.h"
#include "pdt_dictionary_define.h"
#include "unitest.h"
#include "bmu_business.h"
#include "product_oss_mid.h"
#include "product_oss_msg.h"
#include "plat_data_access.h"
#include "data_access.h"
#include "fapi_bmu_comm.h"

STATIC bmu_alm_data_t s_bmu_alm_data[BMU_NUM];

#ifndef UNITTEST
    STATIC int s_balance_result[BMU_NUM][BMU_CELL_NUM];
    STATIC int s_balance_result_bak[BMU_NUM][BMU_CELL_NUM];
    STATIC bmu_ana_data_t s_bmu_ana_data[BMU_NUM];
    STATIC bmu_dig_data_t s_bmu_dig_data[BMU_NUM];
    STATIC bmus_para_t s_bmus_para_in;
    STATIC int deal_bmu_business(void);
    STATIC int load_bmu_ana_data(void);
    STATIC int load_bmu_dig_data(void);
    STATIC int load_bmu_alm_data(void);
    STATIC int load_bmus_para(void);
    STATIC int is_all_optimizer_work_well(void);
    STATIC int load_bmu_data(void);
    STATIC int calc_balance_ctrl_status(void);
    STATIC int bmu_balance_process(void);
    STATIC int start_cell_balance_ctrl(void);
    STATIC int calc_intervals(Balance_Para_Struct *balance_para_t);
    STATIC int check_balance_ctrl_result(void);
    STATIC float get_interval_threshold(float voltage, Balance_Para_Struct balance_para_t[], int num_intervals);
    STATIC float get_min_voltage(float_type_t *cell_volt, int count);
    STATIC int check_balance(float cell_volt, float bmu_min_cell_volt, Balance_Para_Struct balance_para_t[], int num_intervals, int *balance_res);
#endif

STATIC int load_bmu_ana_data(void)
{
    static sig_2_sid_info_t sig_2_sid_map[] = {
        {s_bmu_ana_data[0].cell_volt, SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE, BMU_CELL_NUM, sizeof(float), type_float},
        {s_bmu_ana_data[0].cell_temperature, SID_BATTERY_MODULE_ANA_BMU_CELL_TEMPERATURE, BMU_CELL_TEMP_NUM, sizeof(float), type_float},
        {NULL, 0, 0, 0},
    };

    static dev_2_sid_info_t dev_2_sid_info = {
        sig_2_sid_map,
        BMU_NUM,
        sizeof(sig_2_sid_map)/sizeof(sig_2_sid_info_t) -1 ,
        sizeof(bmu_ana_data_t),
        NULL};

    get_sm_data_to_custom_struct_by_dev(&dev_2_sid_info);
    return SUCCESS;
}

STATIC int load_bmu_dig_data(void)
{
    static sig_2_sid_info_t sig_2_sid_map[] = {
        {&s_bmu_dig_data[0].bmu_exist_sta, SID_BATTERY_MODULE_DIG_BMU_EXIST_STATE, 1, sizeof(int), type_int},
        {&s_bmu_dig_data[0].bmu_comm_sta, SID_BATTERY_MODULE_DIG_BMU_COMMUNICATION_STATUS, 1, sizeof(int), type_int},
        {s_bmu_dig_data[0].bmu_cell_balance_sta, SID_BATTERY_MODULE_DIG_BMU_CELL_BALANCE_STATUS, BMU_CELL_NUM, sizeof(int), type_int},
        {NULL, 0, 0, 0},
    };

    static dev_2_sid_info_t dev_2_sid_info = {
        sig_2_sid_map,
        BMU_NUM,
        sizeof(sig_2_sid_map)/sizeof(sig_2_sid_info_t) -1 ,
        sizeof(bmu_dig_data_t),
        NULL};

    get_sm_data_to_custom_struct_by_dev(&dev_2_sid_info);
    return SUCCESS;
}

STATIC int load_bmu_alm_data(void)
{
    static sig_2_sid_info_t sig_2_sid_map[] = {
        {s_bmu_alm_data[0].cell_over_volt_lvl1_alm, SID_BATTERY_MODULE_ALM_BMU_CELL_OVERVOLTAGE_ALARM_LEVEL1, BMU_CELL_NUM, sizeof(int), type_int},
        {s_bmu_alm_data[0].cell_over_volt_lvl2_alm, SID_BATTERY_MODULE_ALM_BMU_CELL_OVERVOLTAGE_ALARM_LEVEL2, BMU_CELL_NUM, sizeof(int), type_int},
        {s_bmu_alm_data[0].cell_over_volt_lvl3_alm, SID_BATTERY_MODULE_ALM_BMU_CELL_OVERVOLTAGE_ALARM_LEVEL3, BMU_CELL_NUM, sizeof(int), type_int},
        {s_bmu_alm_data[0].cell_charge_temp_high_cutoff_alm, SID_BATTERY_MODULE_ALM_BMU_CELL_CHARGE_HIGH_TEMPERATURE_CUTOFF_ALARM, BMU_CELL_TEMP_NUM, sizeof(int), type_int},
        {s_bmu_alm_data[0].cell_charge_temp_low_cutoff_alm, SID_BATTERY_MODULE_ALM_BMU_CELL_CHARGE_LOW_TEMPERATURE_CUTOFF_ALARM, BMU_CELL_TEMP_NUM, sizeof(int), type_int},
        {s_bmu_alm_data[0].cell_discharge_temp_high_cutoff_alm, SID_BATTERY_MODULE_ALM_BMU_CELL_DISCHARGE_HIGH_TEMPERATURE_CUTOFF_ALARM, BMU_CELL_TEMP_NUM, sizeof(int), type_int},
        {s_bmu_alm_data[0].cell_discharge_temp_low_cutoff_alm, SID_BATTERY_MODULE_ALM_BMU_CELL_DISCHARGE_LOW_TEMPERATURE_CUTOFF_ALARM, BMU_CELL_TEMP_NUM, sizeof(int), type_int},
        {&s_bmu_alm_data[0].bmu_vol_high_end_alarm, SID_BATTERY_MODULE_ALM_BMU_VOLTAGE_HIGH_END_ALARM, 1, sizeof(int), type_int},
        {&s_bmu_alm_data[0].bmu_vol_low_end_alarm, SID_BATTERY_MODULE_ALM_BMU_VOLTAGE_LOW_END_ALARM, 1, sizeof(int), type_int},
        {s_bmu_alm_data[0].cell_volt_high_cutoff_alm, SID_BATTERY_MODULE_ALM_BMU_CELL_VOLT_HIGH_CUTOFF_ALARM, BMU_CELL_NUM, sizeof(int), type_int},
        {s_bmu_alm_data[0].cell_volt_low_cutoff_alm, SID_BATTERY_MODULE_ALM_BMU_CELL_VOLT_LOW_CUTOFF_ALARM, BMU_CELL_NUM, sizeof(int), type_int},
        {&s_bmu_alm_data[0].bmu_cg_vol_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_MODULE_CHARGE_VOLTAGE_DIFFERENCE_END_ALARM, 1, sizeof(int), type_int},
        {&s_bmu_alm_data[0].bmu_comm_fail_alm, SID_BATTERY_MODULE_ALM_BMU_COMMUNICATION_FAIL, 1, sizeof(int), type_int},
        {&s_bmu_alm_data[0].bmu_term_high_alm, SID_BATTERY_MODULE_ALM_BMU_TERMINAL_HIGH_TEMPERATURE_ALARM, 1, sizeof(int), type_int},
        {s_bmu_alm_data[0].cell_temp_sample_fault, SID_BATTERY_MODULE_ALM_BMU_TEMPERATURE_DETECTION_ABNORMAL_ALARM,BMU_CELL_TEMP_NUM, sizeof(int), type_int},
        {&s_bmu_alm_data[0].bcmu_charge_Temperature_dif_alarm_end, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_MODULE_CHARGE_TEMPERATURE_DIF_ALARM_END, 1, sizeof(int), type_int},
        {&s_bmu_alm_data[0].bcmu_discharge_Temperature_dif_alarm_end, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_MODULE_DISCHARGE_TEMPERATURE_DIF_ALARM_END, 1, sizeof(int), type_int},
        {&s_bmu_alm_data[0].cell_cg_vol_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_CELL_CHARGE_VOLTAGE_EXTREMELY_DIFF_END_ALARM, 1, sizeof(int), type_int},
        {&s_bmu_alm_data[0].bmu_dischg_vol_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_MODULE_DISCHARGE_VOLTAGE_DIFFERENCE_ALARM_END, 1, sizeof(int), type_int},
        {&s_bmu_alm_data[0].cell_dischg_vol_diff_end_alarm, SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_CELL_DISCHARGE_VOLTAGE_EXTREMELY_DIFF_ALARM_END, 1, sizeof(int), type_int},
        {NULL, 0, 0, 0},
    };

    static dev_2_sid_info_t dev_2_sid_info = {
        sig_2_sid_map,
        BMU_NUM,
        sizeof(sig_2_sid_map)/sizeof(sig_2_sid_info_t) -1 ,
        sizeof(bmu_alm_data_t),
        NULL};

    get_sm_data_to_custom_struct_by_dev(&dev_2_sid_info);
    return SUCCESS;
}


/// @brief 任意一节单体产生告警则返回TRUE
/// @param alm_val 单体告警数组
/// @param alarm_num 单体数量
/// @return TRUE：存在一个单体产生告警
int if_exist_one_alarm(int_type_t* alm_val, int alarm_num)
{
    int i = 0;
    for (i = 0; i < alarm_num; i++)
    {
        if (alm_val[i].i_value == FAULT)
        {
            return TRUE;
        }
    }
    return FALSE;
}


/// @brief 判断设备是否通讯正常
/// @return NORMAL:通讯正常 FAULT:通讯异常
int is_bmu_comm_normal(int dev_sn)
{
    sid sid_temp = 0;
    int bmu_comm_status=INT32S_INVALID;

    sid_temp = SID_SET_DEV_SN(dev_sn, SID_BATTERY_MODULE_DIG_BMU_COMMUNICATION_STATUS);
    if (SUCCESS != pdt_get_data(sid_temp, &bmu_comm_status, sizeof(bmu_comm_status)))
    {
        return FAULT;
    }
    if (bmu_comm_status == NORMAL)
    {
        return NORMAL;
    }
    return FAULT;
}

/// @brief BMU在位判断
/// @param dev_sn 设备序号
/// @return NORMAL:在位 FAULT:不在位
int is_bmu_exist(int dev_sn)
{
    sid sid_temp = 0;
    int bmu_exist_status=INT32S_INVALID;

    sid_temp = SID_SET_DEV_SN(dev_sn, SID_BATTERY_MODULE_DIG_BMU_EXIST_STATE);
    if (SUCCESS != pdt_get_data(sid_temp, &bmu_exist_status, sizeof(bmu_exist_status)))
    {
        return FAULT;
    }
    if (bmu_exist_status == EXIST)
    {
        return NORMAL;
    }
    return FAULT;
}

int get_bmu_alm_data(bmu_alm_data_t* bmu_alm_data) {
    plat_memcpy_s(bmu_alm_data, sizeof(s_bmu_alm_data), s_bmu_alm_data, sizeof(s_bmu_alm_data));
    return SUCCESS;
}

int bmu_battery_object_init(void)
{
    /* init timers */
    if (set_timer(PCHAR_TO_MID(MID_BCMUAPP_BMU), TIMER1, BCMUAPP_BMU_TIMER) != SUCCESS) {
        return FAILURE;
    }
    if (set_timer(PCHAR_TO_MID(MID_BCMUAPP_BMU), TIMER2, BMU_BALANCE_TIMER) != SUCCESS) {
        return FAILURE;
    }
    return SUCCESS;
}

STATIC int load_bmu_data(void)
{
    load_bmu_ana_data();
    load_bmu_dig_data();
    load_bmu_alm_data();
    return SUCCESS;
}


/// @brief 检查均衡控制结果，控制失败则再次进行控制。
/// @param void
/// @return
STATIC int check_balance_ctrl_result(void)
{
    int i,j;
    sid ctrl_sid = 0ll;
    for (i = 0; i < BMU_NUM; i++)
    {
        if ((s_bmu_dig_data[i].bmu_exist_sta.i_value == EXIST) && (s_bmu_dig_data[i].bmu_comm_sta.i_value == NORMAL))
        {
            // 通过比较实时获取的均衡状态和上一次的均衡控制状态是否一致
            for (j = 0; j < BMU_CELL_NUM; j++)
            {
                if (s_bmu_dig_data[i].bmu_cell_balance_sta[j].i_value != s_balance_result_bak[i][j])
                {
                    ctrl_sid = SID_SET_DEV_SN(i + 1, SID_BATTERY_MODULE_DIG_BMU_CELL_BALANCING_CONTROL);
                    exe_bmu_ctrl_asy_cmd(ctrl_sid);
                    break;
                }
            }
        }
    }
    return SUCCESS;
}

STATIC int deal_bmu_business(void)
{
    load_bmus_para();
    load_bmu_data();
    check_balance_ctrl_result();
	return SUCCESS;
}

/// @brief 启动单体均衡控制
/// @param 无
/// @return 无
STATIC int start_cell_balance_ctrl(void)
{
    int i, j;
    int compare_result = 0;
    sid sid_temp = 0ll;
    sid ctrl_sid = 0ll;
    for (i = 0; i < BMU_NUM; i++)
    {
        // 比较单个BMU计算出的均衡控制状态和上一次的是否一致
        compare_result = memcmp(&s_balance_result[i][0], &s_balance_result_bak[i][0], sizeof(int) * BMU_CELL_NUM);

        // 单个电池模块总的均衡控制状态发生改变的时候下发均衡控制命令
        if(compare_result)
        {
            // 首先将备份的均衡控制状态进行更新。
            plat_memcpy_s(&s_balance_result_bak[i][0], BMU_CELL_NUM * sizeof(int), &s_balance_result[i][0], BMU_CELL_NUM * sizeof(int));

            // 将计算的均衡控制状态设置到共享内存中，然后再下发均衡控制的遥控命令。
            for (j = 0; j < BMU_CELL_NUM; j++)
            {
                sid_temp = SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_DIG_BMU_CELL_BALANCING_CONTROL, i + 1, j + 1);
                pdt_set_data(sid_temp, &s_balance_result[i][j], sizeof(int), type_int);
            }

            ctrl_sid = SID_SET_DEV_SN(i + 1, SID_BATTERY_MODULE_DIG_BMU_CELL_BALANCING_CONTROL);
            exe_bmu_ctrl_asy_cmd(ctrl_sid);
        }
    }
    return SUCCESS;
}

/// @brief 电芯均衡处理
/// @param void
/// @return SUCCESS
STATIC int bmu_balance_process(void)
{
    calc_balance_ctrl_status();
    start_cell_balance_ctrl();
    return SUCCESS;
}

int bmu_battery_object_main(msgid msg_id, msgsn msg_sn, const void *msg, unsigned int msg_len, const mid sender)
{
    switch (msg_id) {
        case TIMER1_MSG:
            deal_bmu_business();
            break;
        case TIMER2_MSG:
            bmu_balance_process();
            break;
        default:
            break;
    }
    return SUCCESS;
}

// Function to find the minimum voltage in a module or cluster
STATIC float get_min_voltage(float_type_t *cell_volt, int count)
{
    int i = 0;
    float min_voltage = cell_volt[0].f_value;
    for (i = 1; i < count; i++)
    {
        if (cell_volt[i].f_value < min_voltage)
        {
            min_voltage = cell_volt[i].f_value;
        }
    }
    return min_voltage;
}

/// @brief 根据均衡区间数量和上下限，判断当前单体电压落到了哪个区间内。
/// @param voltage 待判断的单体电压值
/// @param balance_para_t 均衡区间信息
/// @param num_intervals 均衡区间数量
/// @return 对应均衡区间的阈值
STATIC float get_interval_threshold(float voltage, Balance_Para_Struct balance_para_t[], int num_intervals)
{
    int i;
    for (i = 0; i < num_intervals; i++)
    {
        if (voltage > balance_para_t[i].lower_bound && voltage <= balance_para_t[i].upper_bound)
        {
            return balance_para_t[i].threshold;
        }
    }
    // 如果没有落在任何区间，则返回一个不可能的差值，保证不满足均衡条件。
    return IMPOSSIBLE_DIFF_VALUE;
}

/// @brief 计算单个单体的均衡状态
/// @param cell_volt 单体电压
/// @param bmu_min_cell_volt 待比较的最低单体电压
/// @param balance_para_t 均衡区间参数
/// @param num_intervals 均衡区间个数
/// @param balance_res 最终均衡判断结果
/// @return
STATIC int check_balance(float cell_volt, float bmu_min_cell_volt, Balance_Para_Struct balance_para_t[], int num_intervals, int *balance_res)
{
    float vdiff = cell_volt - bmu_min_cell_volt;
    float vthreshold = get_interval_threshold(cell_volt, balance_para_t, num_intervals);
    // 差值单位为V, 阈值单位为mV
    if (vdiff > (vthreshold / 1000))
    {
        *balance_res = 1;
        return 1;
    }
    else
    {
        *balance_res = 0;
        return 0;
    }
}

/* Started by AICoder, pid:qade2i9930s25511404f095040dc0701a53499e3 */
/// @brief 将均衡区间参数转换为数组的形式，方便数据处理。
/// @param balance_para_t 均衡区间数组
/// @return SUCCESS
STATIC int calc_intervals(Balance_Para_Struct *balance_para_t)
{
    // 确保 balance_para_t 不为 NULL
    if (balance_para_t == NULL)
    {
        return FAILURE;
    }

    Balance_Para_Struct balance_para[EQUAL_INTERVAL_MAX_NUM] =
    {
        {s_bmus_para_in.equal_interval1_upper_limit, s_bmus_para_in.equal_interval1_lower_limit, s_bmus_para_in.equal_interval1_thre},
        {s_bmus_para_in.equal_interval2_upper_limit, s_bmus_para_in.equal_interval2_lower_limit, s_bmus_para_in.equal_interval2_thre},
        {s_bmus_para_in.equal_interval3_upper_limit, s_bmus_para_in.equal_interval3_lower_limit, s_bmus_para_in.equal_interval3_thre},
        {s_bmus_para_in.equal_interval4_upper_limit, s_bmus_para_in.equal_interval4_lower_limit, s_bmus_para_in.equal_interval4_thre},
        {s_bmus_para_in.equal_interval5_upper_limit, s_bmus_para_in.equal_interval5_lower_limit, s_bmus_para_in.equal_interval5_thre},
        {s_bmus_para_in.equal_interval6_upper_limit, s_bmus_para_in.equal_interval6_lower_limit, s_bmus_para_in.equal_interval6_thre}
    };

    plat_memcpy_s(balance_para_t, sizeof(balance_para), balance_para, sizeof(balance_para));
    return SUCCESS;
}
/* Ended by AICoder, pid:qade2i9930s25511404f095040dc0701a53499e3 */

/// @brief 计算均衡控制状态（注意:通讯正常的BMU的65个单体都得计算）
/// @return
STATIC int calc_balance_ctrl_status(void)
{
    int i,j;
    sid cell_volt_sid_temp = 0x0ll;

    Balance_Para_Struct balance_para_t[EQUAL_INTERVAL_MAX_NUM];
    plat_memset_s(balance_para_t, sizeof(balance_para_t), 0x00, sizeof(balance_para_t));
    calc_intervals(balance_para_t);

    int num_intervals = s_bmus_para_in.actual_equal_interval_num;

    // 首先计算每个电池模块最低的单体电压
    float bmu_min_cell_volt[BMU_NUM] = {3.8f, 3.8f, 3.8f, 3.8f};

    // 所有在位且通讯正常的BMU中最小的单体电压值
    float bmus_min_cell_volt = 3.8f;

    // 所有的优化器工作正常时，通讯正常的BMU的最小单体电压不一致，各算各的。
    if (is_all_optimizer_work_well() == SUCCESS)
    {
        for (i = 0; i < BMU_NUM; i++)
        {
            cell_volt_sid_temp = SID_SET_DEV_SN(i + 1, SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE);
            if (is_data_valid(cell_volt_sid_temp, TYPE_VALUE) == DATA_VALID)
            {
                bmu_min_cell_volt[i] = get_min_voltage(&s_bmu_ana_data[i].cell_volt[0], BMU_CELL_NUM);
            }
        }
    }
    /* 某个优化器不存在或者存在但是工作异常 */
    else
    {
        for (i = 0; i < BMU_NUM; i++)
        {
            cell_volt_sid_temp = SID_SET_DEV_SN(i + 1, SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE);
            if (is_data_valid(cell_volt_sid_temp, TYPE_VALUE) == DATA_VALID)
            {
                bmu_min_cell_volt[i] = get_min_voltage(&s_bmu_ana_data[i].cell_volt[0], BMU_CELL_NUM);
            }
            // 由于此情况下所有正常的BMU采用同一个最小单体电压，所以此处记录下最小的单体电压。
            if (bmu_min_cell_volt[i] < bmus_min_cell_volt)
            {
                bmus_min_cell_volt = bmu_min_cell_volt[i];
            }
        }
        // 为保证所有正常BMU的单体采用同一最小单体电压进行比较，这里将四个模块的最小单体电压全部刷新成最小的。
        for (i = 0; i < BMU_NUM; i++)
        {
            bmu_min_cell_volt[i] = bmus_min_cell_volt;
        }
    }

    // 循环判断所有电池模块的单体是否需要进行开启均衡或关闭均衡
    for (i = 0; i < BMU_NUM; i++)
    {
        // BMU在位并且通讯正常才有必要进行均衡控制状态判断
        if ((s_bmu_dig_data[i].bmu_exist_sta.i_value == EXIST) && (s_bmu_dig_data[i].bmu_comm_sta.i_value == NORMAL))
        {
            for (j = 0; j < BMU_CELL_NUM; j++)
            {
                check_balance(s_bmu_ana_data[i].cell_volt[j].f_value, bmu_min_cell_volt[i], balance_para_t, num_intervals, &s_balance_result[i][j]);
            }
        }
    }
    return 0;
}

STATIC int load_bmus_para(void)
{
    sig_sid_map_t get_sig_map[] = {
        {&s_bmus_para_in.actual_equal_interval_num, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL_NUMBER, 1, 1},
        {&s_bmus_para_in.equal_interval1_upper_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL1_UPPER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval1_lower_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL1_LOWER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval1_thre, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL1_THRE, 1, 1},
        {&s_bmus_para_in.equal_interval2_upper_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL2_UPPER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval2_lower_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL2_LOWER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval2_thre, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL2_THRE, 1, 1},
        {&s_bmus_para_in.equal_interval3_upper_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL3_UPPER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval3_lower_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL3_LOWER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval3_thre, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL3_THRE, 1, 1},
        {&s_bmus_para_in.equal_interval4_upper_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL4_UPPER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval4_lower_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL4_LOWER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval4_thre, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL4_THRE, 1, 1},
        {&s_bmus_para_in.equal_interval5_upper_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL5_UPPER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval5_lower_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL5_LOWER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval5_thre, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL5_THRE, 1, 1},
        {&s_bmus_para_in.equal_interval6_upper_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL6_UPPER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval6_lower_limit, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL6_LOWER_LIMIT, 1, 1},
        {&s_bmus_para_in.equal_interval6_thre, SID_BATTERY_MODULE_GROUP_PARA_BMUS_EQUALIZATION_INTERVAL6_THRE, 1, 1},
        {NULL, 0, 0, 0},
    };
    get_data_to_custom_struct(get_sig_map);
    return SUCCESS;
}

STATIC int is_all_optimizer_work_well(void)
{
    int i;
    int opt_work_sta[BMU_NUM];
    sid sid_temp = 0;
    for (i = 0; i < BMU_NUM; i++)
    {
        sid_temp = SID_SET_DEV_SN(i + 1, SID_OPTIMIZER_DIG_OPTIMIZER_COMMUNICATION_STATUS);
        if (is_data_valid(sid_temp, TYPE_VALUE) == DATA_INVALID)
        {
            return FAILURE;
        }
        sid_temp = SID_SET_DEV_SN(i + 1, SID_OPTIMIZER_DIG_OPTIMIZER_FAULT_STATUS);
        pdt_get_data(sid_temp, &opt_work_sta[i], sizeof(int));
        // 通讯正常但是工作异常
        if (opt_work_sta[i] == FAULT)
        {
            return FAILURE;
        }
    }
    return SUCCESS;
}