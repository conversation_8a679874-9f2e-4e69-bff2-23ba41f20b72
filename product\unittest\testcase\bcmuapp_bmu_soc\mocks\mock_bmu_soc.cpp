/*Copyright [year] <Copyright Owner>*/
#include <stdlib.h>
#include <string.h>
#include "plat_error.h"
#include "data_type.h"
#include "stdio.h"
#include "plat_dictionary_define.h"
#include "data_access.h"
#include "pdt_cmapi_cfg_objs.h"
#include "pdt_cmapi_power_subrack.h"
#include "bmu_soc.h"
#include "sids.h"
#include "time.h"
#include "pdt_common.h"

#ifdef __cplusplus
extern "C" {
#endif   /*__cplusplus */

float battery_module_voltage = 0.0f;
float bmu_cell_undervoltage_end_threshold = 2.71f;
float bmu_module_undervoltage_end_threshold = 196;
float cell_voltage = 0.0f;
int bcmu_soc = 0;
int s_mock_soh_set = 100;
int batt_status = 0;
int bcmu_soh = 100;
float low_threshold = 10;
float high_threshold = 95;
int cluster_curr = 0;
int cluster_cycle_times_param = 0;

// 添加全局变量来控制mock行为
int g_mock_bcmu_serial_result = SUCCESS;
int g_mock_bmu_serial_results[BMU_NUM] = {SUCCESS, SUCCESS, SUCCESS, SUCCESS};
char g_mock_bcmu_serial[32] = "BCMU-SERIAL-12345";
char g_mock_bmu_serials[BMU_NUM][32] = {
    "BMU1-SERIAL-12345",
    "BMU2-SERIAL-12345",
    "BMU3-SERIAL-12345",
    "BMU4-SERIAL-12345"
};

// 添加SOC校正告警相关的全局变量
float s_max_accumulated_ah_since_correction = 0.0f;
int s_full_chg_or_dischg_status = FALSE;
int s_soc_correction_alarm = FALSE;

// 添加备件替换SOH参数相关的全局变量
int g_mock_backup_replacement_soh[BMU_NUM] = {85, 90, 88, 92};

// 在pdt_get_data函数中添加处理
int pdt_get_data(sid signal_id, void *data, size_t size) {
    int i = 0;
    int j = 0;
    if (signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CYCLE_TIMES)
    {
        *(int *)data = cluster_cycle_times_param;
        return SUCCESS;
    }
    // 处理BCMU序列号请求
    if (signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DEVINFO_BCMU_SERIAL_NUMBER) {
        if (data != NULL && size >= sizeof(g_mock_bcmu_serial)) {
            memcpy(data, g_mock_bcmu_serial, sizeof(g_mock_bcmu_serial));
        }
        return g_mock_bcmu_serial_result;
    } else if (signal_id == SID_BATTERY_MODULE_GROUP_PARA_BMUS_CELL_UNDERVOLTAGE_END_THRESHOLD) {
        *((float*)data) = bmu_cell_undervoltage_end_threshold;
        return SUCCESS;
    } else if (signal_id == SID_BATTERY_MODULE_GROUP_PARA_BMUS_VOLTAGE_LOW_END_THRESHOLD){
        *((float*)data) = bmu_module_undervoltage_end_threshold;
        return SUCCESS;
    }else if (signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_LOW_THRESHOLD) {
        *((float*)data) = low_threshold;
        return SUCCESS;
    } else if (signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_CELL_VOLTAGE_OUTLIER_SOC_NON_PLATFORM_PERIOD_HIGH_THRESHOLD){
        *((float*)data) = high_threshold;
        return SUCCESS;
    }

    // 处理BMU序列号请求
    for (i = 0; i < BMU_NUM; i++) {
        if (signal_id == SID_SET_DEV_SN(i + 1, SID_BATTERY_MODULE_DEVINFO_BMU_CELL_SERIAL_NUMBER)) {
            if (data != NULL && size >= sizeof(g_mock_bmu_serials[i])) {
                memcpy(data, g_mock_bmu_serials[i], sizeof(g_mock_bmu_serials[i]));
            }
            return g_mock_bmu_serial_results[i];
        }

        // 处理备件替换SOH参数请求
        if (signal_id == PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_BACKUP_REPLACEMENT_SOH, 1, i + 1)) {
            if (data != NULL && size >= sizeof(int)) {
                *((int*)data) = g_mock_backup_replacement_soh[i];
            }
            return SUCCESS;
        }

        if(signal_id == SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_BATTERY_MODULE_SOH, 1, i + 1)) {
            *((int*)data) = s_mock_soh_set;
            return SUCCESS;
        } else if (signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT) {
            *(float*)data = cluster_curr;
            return SUCCESS;
        } else if (signal_id == SID_SET_DEV_SN_SIG_VINDEX(SID_OPTIMIZER_ANA_OPTIMIZER_INPUT_CURRENT, i + 1, 1)) {
            *(float*)data = 10.0f * i; // Different values for different indexes
            return (i < 2) ? SUCCESS : FAILURE; // Simulate failure for the last one
        } else if (signal_id == SID_SET_DEV_SN_SIG_VINDEX(SID_OPTIMIZER_ANA_OPTIMIZER_OUTPUT_CURRENT, i + 1, 1)) {
            *(float*)data = 5.0f * i; // Different values for different indexes
            return (i < 2) ? SUCCESS : FAILURE; // Simulate failure for the last one
        }else if(signal_id == SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_VOLTAGE){
            *(float*)data = battery_module_voltage;
            return SUCCESS;
        }else if(signal_id == SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE){
            *(float*)data = cell_voltage;
            return SUCCESS;
        }
        else if(signal_id == SID_BATTERY_MODULE_GROUP_PARA_BMUS_CELL_OVERVOLTAGE_END_THRESHOLD){
            *(float*)data = 3.65f;
            return SUCCESS;
        }
        else if(signal_id == SID_BATTERY_MODULE_GROUP_PARA_BMUS_VOLTAGE_HIGH_END_THRESHOLD){
            *(float*)data = 230.0f;
            return SUCCESS;
        }
        else if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC){
            *(int*)data = bcmu_soc;
            return SUCCESS;
        }else if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS){
            *(int *)data = batt_status;
            return SUCCESS;
        }
        else if(signal_id == PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_DIG_BMU_COMMUNICATION_STATUS, i+1, 1)){
            *(int *)data = batt_status;
            return SUCCESS;
        }
        else if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH){
            *(int *)data = bcmu_soh;
            return SUCCESS;
        }
        for(j = 0; j < BMU_CELL_NUM; j ++) {
            if(signal_id == PDT_SID_SET_DEV_SN_SIG_VINDEX(SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE, i+ 1, j+1)){
                *(float*)data = cell_voltage;
                return SUCCESS;
            }
        }
    }
    // 添加SOC校正告警相关的信号处理
    if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_MAX_CHG_DISCHG_AH_SINCE_LAST_SOC_CALIBRATION) {
        if(data != NULL && size >= sizeof(float)) {
            *((float*)data) = s_max_accumulated_ah_since_correction;
        }
        return SUCCESS;
    }
    if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_FULL_CHG_OR_DISCHG_STATUS) {
        if(data != NULL && size >= sizeof(int)) {
            *((int*)data) = s_full_chg_or_dischg_status;
        }
        return SUCCESS;
    }
    if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_CLUSTER_SOC_CORRECTION_ALARM) {
        if(data != NULL && size >= sizeof(int)) {
            *((int*)data) = s_soc_correction_alarm;
        }
        return SUCCESS;
    }
    return FAILURE;
}
/* Ended by AICoder, pid:2a35df6ab535a5514d050a6fb04e2e12c7f50f55 */

int pdt_set_data(sid signal_id, void *data, size_t size, data_type_e data_type)
{
    if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CYCLE_TIMES)
    {
        cluster_cycle_times_param = *(int *)data;
        return SUCCESS;
    }
    if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT)
    {
        cluster_curr = *(float *)data;
        return SUCCESS;
    }
    if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC)
    {
        bcmu_soc = *(int *)data;
        return SUCCESS;
    }else if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS)
    {
        batt_status = *(int *)data;
        return SUCCESS;
    }
    else if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH)
    {
        bcmu_soh = *(int *)data;
        return SUCCESS;
    }
    else if(signal_id == SID_BATTERY_MODULE_DIG_BMU_COMMUNICATION_STATUS)
    {
        batt_status = *(int *)data;
        return SUCCESS;
    }
    else if (signal_id == SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_VOLTAGE)
    {
        battery_module_voltage = *(float *)data;
        return SUCCESS;
    }
    else if (signal_id == SID_BATTERY_MODULE_GROUP_PARA_BMUS_CELL_UNDERVOLTAGE_END_THRESHOLD)
    {
        bmu_cell_undervoltage_end_threshold = *(float *)data;
        return SUCCESS;
    }
    else if (signal_id == SID_BATTERY_MODULE_GROUP_PARA_BMUS_VOLTAGE_LOW_END_THRESHOLD)
    {
        bmu_module_undervoltage_end_threshold = *(float *)data;
        return SUCCESS;
    }
    else if (signal_id ==  SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE) 
    {
        cell_voltage = *(float *)data;
    }
    // 添加SOC校正告警相关的信号处理
    if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_MAX_CHG_DISCHG_AH_SINCE_LAST_SOC_CALIBRATION) {
        if(data != NULL && size >= sizeof(float)) {
            s_max_accumulated_ah_since_correction = *((float*)data);
        }
        return SUCCESS;
    }
    if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_FULL_CHG_OR_DISCHG_STATUS) {
        if(data != NULL && size >= sizeof(int)) {
            s_full_chg_or_dischg_status = *((int*)data);
        }
        return SUCCESS;
    }
    if(signal_id == SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_CLUSTER_SOC_CORRECTION_ALARM) {
        if(data != NULL && size >= sizeof(int)) {
            s_soc_correction_alarm = *((int*)data);
        }
        return SUCCESS;
    }
    return SUCCESS;
}

time_t current_time = 0;
/* Started by AICoder, pid:sc822745bbrf21f145940913d034c10ed7c42133 */
int get_system_time(time_t *time) {
    *time = current_time;
    return SUCCESS;
}
/* Ended by AICoder, pid:sc822745bbrf21f145940913d034c10ed7c42133 */

int read_file( char *file_Name,void *buff, unsigned int ulSize )
{
	return 1;
}

unsigned int calculate_crc32( const char *in_put, unsigned int length ){
	return SUCCESS;
}

int plat_memcpy_s(void *s1, rsize_t s1max, const void *s2, rsize_t n)
{
    int num = 0;
	num = s1max <= n ? s1max : n;
	memcpy(s1, s2, num);
	return SUCCESS;
}

int write_file( char *file_Name,void *buff, unsigned int ulSize )
{
	return SUCCESS;
}

void get_sm_data_to_custom_struct_by_dev(dev_2_sid_info_t *sig_sid_map)
{
    return;
}

int plat_memset_s(void *s, size_t smax, int c, size_t n)
{
    return SUCCESS;
}

float pdt_get_valid_data(float data, float max_val, float min_val)
{
    if (data > max_val)
    {
        return max_val;
    }
    else if (data < min_val)
    {
        return min_val;
    }
    else
    {
        return data;
    }
}

int get_diff_days(time_base_t *p_time)
{
    return 2;
}

int pdt_get_time( time_base_t *p_time )
{
    struct tm tm_time;

    time_t t_time;
    if(p_time == NULL)
    {
        return -1;
    }

    t_time = time( NULL );
    localtime_r(&t_time,&tm_time);

    p_time->year = tm_time.tm_year + 1900;
    p_time->month= tm_time.tm_mon + 1;
    p_time->day = tm_time.tm_mday;
    p_time->hour = tm_time.tm_hour;
    p_time->minute = tm_time.tm_min;
    p_time->second = tm_time.tm_sec;

    return 0;
}

int get_linear_point(float x1, float y1, float x2, float y2, float x, float* py)
{
    float m, b;

    // 检查 x1 和 x2 是否相等，避免除以零
    if (fabs(x1-x2) < 0.001)
    {
        return FAILURE;
    }

    // 检查 py 是否为空指针
    if (NULL == py)
    {
        return FAILURE;
    }

    // 计算斜率 m
    m = (y2 - y1) / (x2 - x1);

    // 计算截距 b
    b = y1 - m * x1;

    // 计算插值点的 y 值
    *py = m * x + b;

    return SUCCESS;
}

int bubble_sorts(float_type_t *data, int num) {
    int i = 0;
    int j = 0;
    if (data == NULL || num <= 0) {
        return FAILURE; // 参数无效
    }

    for (i = 0; i < num - 1; i++) {
        for (j = 0; j < num - 1 - i; j++) {
            if (data[j].f_value < data[j + 1].f_value) {
                float_type_t temp = data[j];
                data[j] = data[j + 1];
                data[j + 1] = temp;
            }
        }
    }
    return SUCCESS; // 排序成功
}

int is_bmu_comm_normal(int dev_sn)
{
    return NORMAL;
}

int send_soh_data_to_bmu(int dev_sn){
    return SUCCESS;
}

void snprintf_s_void(char *buf, size_t n, const char *format, ...)
{
	return ;
}

unsigned short calculate_crc16(const unsigned char *input, unsigned int length) {
    return 0;
}

short get_int16_data(const unsigned char *p) {
    return 0;
}

int is_bmu_exist(int dev_sn) {
    return NORMAL;
}

int boardcast_serial_num_to_bmu(void)
{
    return SUCCESS;
}

int get_soh_data_from_bmu(int dev_sn)
{
    return SUCCESS;
}

int snprintf_s(char *buf, size_t n, const char *format, ...)
{
    return SUCCESS;
}

void write_event_content(event_content_t *content,
                         char *content1, char *content2,
                         char *content3, char *content4,
                         char *content5, char *content6, char *content7)
{
    return;
}

int set_history_event_2_db(history_event_t *event)
{
    return SUCCESS;
}

int lock_file_write(const char *file_name, int *fd) {
    return SUCCESS;
}

int unlock_file(int fd) {
    return SUCCESS;
}

// 添加设置备件替换SOH参数的函数
void set_mock_backup_replacement_soh(int bmu_index, int soh_value) {
    if (bmu_index >= 0 && bmu_index < BMU_NUM) {
        g_mock_backup_replacement_soh[bmu_index] = soh_value;
    }
}

void set_all_mock_backup_replacement_soh(int soh_value) {
    for (int i = 0; i < BMU_NUM; i++) {
        g_mock_backup_replacement_soh[i] = soh_value;
    }
}

int plat_printf_s(const char *format, ...)
{
    va_list args;
    int result;

    va_start(args, format);
    result = vprintf(format, args);
    va_end(args);
    return result;
}

int pdt_set_para(sid signal_id, void *data, size_t size) {
    return SUCCESS;
}

boolean is_file_exist(const char *filename)
{
	return TRUE;
}

#ifdef __cplusplus
}
#endif  /* __cplusplus */
