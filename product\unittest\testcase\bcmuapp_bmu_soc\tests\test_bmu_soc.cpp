/**
 * @file     test_bmu_soc.cpp
 * @brief    test_bmu_soc.cpp
 * @details  test_bmu_soc.cpp
 * <AUTHOR> 
 * @date     2017-03-20
 * @version  A001
 * @par Copyright (c):
 *       ZTE Corporation 
 * @par History:
 *   version: author, date, descn
 */ 

/*  includes  */
#include <mockcpp/mokc.h>
#include "gtest/gtest.h"
#include "data_type.h"
#include "sids.h"
#include "plat_data_access.h"
#include "bmu_soc.h"
#include "data_access.h"
#include "mock_bmu_soc.h"
#include "pdt_common.h"
#include "fapi_bmu_comm.h"
#include "data_access.h"
#include "bmu_business.h"
#include "bcmu_business.h"

#define FLOAT_PRECISION          (0.000001f)                         ///<  浮点数精度
#define if_float_equal(x, y)     (fabs(x - y) < FLOAT_PRECISION)   

int bmu_soh_data_syned[BMU_NUM] = {FALSE};

extern "C"
{
    extern float s_bmu_current[BMU_NUM];
    extern float s_bmu_real_capacity[BMU_NUM]; 
    extern bmu_cal_soc_t s_bmu_cal_soc[BMU_NUM];
    extern bmu_soc_save_t s_bmu_soc_save;
    extern time_t  s_pre_time;
    extern bmu_cal_soe_t s_bmu_cal_soe[BMU_NUM];
    extern float s_bmu_real_energy[BMU_NUM];
    extern float s_mock_soh_set;
    extern bmu_sox_info_t s_bmu_sox_in[BMU_NUM];
    extern bmu_sox_deal_t s_bmu_sox_deal[BMU_NUM];
    extern int g_mock_bcmu_serial_result;
    extern int g_mock_bmu_serial_results[BMU_NUM];
    extern bcmu_save_t s_bcmu_save_bak;

    extern int cal_batt_current(void);
    extern int is_soc_saving_needed(void);
    extern int read_check_soc_save(bmu_soc_save_t * save_ptr);
    extern void init_soc_save(void);
    extern int init_soc_data(void);
    extern int cal_bmu_soc(void);
    extern int if_config_opt(void);
    extern int is_bmu_charge_full(int dev_sn);
    extern int is_bmu_discharge_full(int dev_sn);
    extern int judge_bmu_charge_full_status(void);
    extern int judge_bmu_discharge_full_status(void);
    extern int adjust_bmu_soc(void);
    extern time_t current_time;
    extern float battery_module_voltage;
    extern float cell_voltage;
    extern float calc_average_temp(const float_type_t *arr, int size);
    extern int calculate_calender_life_decline(bmu_sox_deal_t *bmu_sox_deal, bcmu_save_t *bmu_soh_save, bmu_sox_info_t *bmu_sox_in, int bmu_index);
    extern int plus_calender_life_decline(bmu_sox_deal_t *bmu_sox_deal, bcmu_save_t *bmu_soh_save, bmu_sox_info_t *bmu_sox_in, int bmu_index);
    extern int write_bmu_soh_info_to_file(bcmu_save_t * bcmu_save_tmp);
    extern int read_bmu_soh_info_from_file(bcmu_save_t * bcmu_save_tmp);
    extern int deal_calendar_decline_when_start(void);
    extern int calculate_bmu_soh(void);
    extern int cal_adjust_soh(bmu_sox_info_t *batt_info, bmu_sox_deal_t *batt_deal);
    extern int save_sox_to_file(bcmu_save_t * bcmu_save_tmp, bmu_sox_deal_t  *bmu_sox_deal);
    extern float get_cell_dcr(float cell_temp);
    extern int update_bcmu_soc(void);
    extern int init_bmu_sox_save(void);
    extern int cal_bmu_soe(void);
    extern int cal_and_adjust_bmu_soe(void);
    extern int adjust_bmu_soe_by_ocv(void);
    extern float ocv_to_soc(float ocv);
    extern int adjust_bmu_soe_efficiency(void);
    extern float calculate_soe_rack1(bmu_cal_soe_t *bmu_soe);
    extern float calculate_soe_rack2(bmu_cal_soe_t *bmu_soe, float *bmu_energy);
    extern float limit_soe_change(float new_soe, float last_soe, int* is_limiting);
    extern float limit_charging_soe(float new_soe, float last_soe, int* is_limiting);
    extern float limit_discharging_soe(float new_soe, float last_soe, int* is_limiting);
    extern float calculate_cluster_soe(bmu_cal_soe_t *bmu_soe, float *bmu_energy, float* last_cluster_soe, int* last_opt_status, int* is_limiting);
    extern int if_opt_open(void);
    extern int update_soh(int bcmu_soh, int new_value);
    extern int get_min_soh(int bmu_soh[]);
    extern int calculate_cluster_soh(void);
    extern unsigned short get_int16_data(const unsigned char *data);
    extern int s_replace_finish_flag;
    extern int s_soh_sync_finish_flag;
    extern float lookup_voltage(float temp, float soc);
    extern float lookup_soc(float temp, float voltage);
    extern int judge_idle_status(void);
    extern time_t s_idle_start_time;
    extern int calculate_cell_voltage_extremes(void);
    extern float bmu_cell_voltages_max[BMU_NUM];
    extern float bmu_cell_voltages_min[BMU_NUM];
    extern float bmu_cell_temps_at_max[BMU_NUM];
    extern float bmu_cell_temps_at_min[BMU_NUM];
    extern float global_max_v;
    extern float global_min_v;
    extern float global_temp_at_max;
    extern float global_temp_at_min;
    extern int soc_static_calibration(soc_adjust_record_t *adjust_ret);
    extern int is_cell_voltage_out_of_platform_region(void);
    extern soc_adjust_record_t adjust_charge_end_soc(int opt_config_status);
    extern soc_adjust_record_t adjust_discharge_end_soc(int opt_config_status);
    extern int save_adjust_soc_history_event(soc_adjust_record_t soc_adjust_type);
    extern soc_adjust_record_t s_before_soc_adjust_record;
    extern int check_backup_replacement_soh_change(int bmu_index, bmu_sox_deal_t *bmu_sox_deal);
    extern int calculate_cluster_cycle_times(void);
}

void set_mock_soe_data_bak(bcmu_save_t* mock_save, float soe, float chg_eff, float dischg_eff, float wh, int crc)
{
    for (int i = 0; i < BMU_NUM; i++)
    {
        mock_save->bmu_save_data[i].bmu_soe = soe;
        mock_save->bmu_save_data[i].soe_chg_efficiency = chg_eff;
        mock_save->bmu_save_data[i].soe_dischg_efficiency = dischg_eff;
        mock_save->bmu_save_data[i].cumulative_wh = wh;
        mock_save->crc = crc;
    }
}

void set_mock_bmu_current(float current)
{
    for (int i = 0; i < BMU_NUM; i++)
    {
        s_bmu_current[i] = current;
    }
}

void set_mock_bmu_ocv(float ocv)
{
    for (int i = 0; i < BMU_NUM; i++)
    {
        s_bmu_sox_in[i].bmu_sox_data.cell_ocv.f_value = ocv;
    }
}
class ENV : public testing::Environment {
 public:
    virtual void SetUp() {
        // SetUp run once before all testcases
        // you can add mock group here
    }
    virtual void TearDown() {
        // TearDown run once before all testcases
    }
};

class bmu_soc : public testing::Test {
 protected:
    static void SetUpTestCase() {
        // SetUpTestCase run once before testcase groups named by bcmuapp_bcmu
    }

    static void TearDownTestCase() {
        // TearDownTestCase run once before testcase groups named by bcmuapp_bcmu
    }

    virtual void SetUp() {
        // SetUp run before every testcase of group bcmuapp_bcmu
        // 初始化测试数据
        memset(&s_before_soc_adjust_record, 0, sizeof(soc_adjust_record_t));
        memset(&test_bmu_soe, 0, sizeof(test_bmu_soe));
        memset(test_bmu_energy, 0, sizeof(test_bmu_energy));
        last_cluster_soe = 50.0f;
        last_opt_status = FALSE;
        is_limiting = FALSE;
    }

    virtual void TearDown() {
        // SetUp run before every testcase of group bcmuapp_bcmu
    }

    bmu_cal_soe_t test_bmu_soe[BMU_NUM];
    float test_bmu_energy[BMU_NUM];
    float last_cluster_soe;
    int last_opt_status;
    int is_limiting;

    // Some expensive resource shared by all tests.
    // int share_variable ;
};


// add testcase below

/* Started by AICoder, pid:g7145bdeber6111145f00b6ab0ae351f6d41cd95 */
TEST_F(bmu_soc, ReadCheckSuccess) {
    memset(&s_bmu_soc_save, 0x55, sizeof(s_bmu_soc_save));
    init_soc_save();
    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_FLOAT_EQ(s_bmu_soc_save.soc_save[i], 50);
        EXPECT_FLOAT_EQ(s_bmu_soc_save.batt_rate_cap[i], 314);
    }
}
/* Ended by AICoder, pid:g7145bdeber6111145f00b6ab0ae351f6d41cd95 */

/* Started by AICoder, pid:hfc2dsc1fbw9f47140650babe02ec50582d73202 */
TEST_F(bmu_soc, InitSocDataTestNormalOperation) {
    memset(&s_bmu_cal_soc, 0, sizeof(s_bmu_cal_soc));
    EXPECT_EQ(init_soc_data(), SUCCESS);
    for (int i = 0; i < BMU_NUM; ++i) {
        float expected_cumulative_ah = s_bmu_soc_save.soc_save[i] * TOTAL_CAPACITY;
        EXPECT_FLOAT_EQ(s_bmu_cal_soc[i].soc, (float)50);
        EXPECT_FLOAT_EQ(s_bmu_cal_soc[i].cumulative_ah, expected_cumulative_ah);
    }
}
/* Ended by AICoder, pid:hfc2dsc1fbw9f47140650babe02ec50582d73202 */

/* Started by AICoder, pid:96992y4d61tfdd3142930933802cb519dbd39d29 */
TEST_F(bmu_soc, BMUSocSaveTestNullPointer) {
    EXPECT_EQ(read_check_soc_save(NULL), FALSE);
}

TEST_F(bmu_soc, BMUSocSaveTestCRCMismatch) {
    bmu_soc_save_t save_ptr;
    save_ptr.crc = 0x12345678; // Set to an incorrect value
    EXPECT_EQ(read_check_soc_save(&save_ptr), FALSE);
}
/* Ended by AICoder, pid:96992y4d61tfdd3142930933802cb519dbd39d29 */

/* Started by AICoder, pid:4604f7375612156142740b8dc0be453d97770a04 */
TEST_F(bmu_soc, BattCurrentTestTotalClusterCurrentFailure) {
    MOCKER(pdt_get_data)
            .stubs()
            .with(any(), any(), any())
            .will(returnValue(FAILURE));
    EXPECT_EQ(cal_batt_current(), FAILURE);
    EXPECT_EQ(cal_and_adjust_bmu_soc(), FAILURE);
    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_FLOAT_EQ(s_bmu_current[i], 0.0f);
    }
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, BattCurrentTestNormalOperation) {
    float cluster_current = 100.0f;
    int ret = pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT, &cluster_current, sizeof(float), type_float);
    EXPECT_EQ(ret, SUCCESS);

    ret = cal_batt_current();
    EXPECT_EQ(ret, SUCCESS);

    float total_input_current = 0.0f + 10.0f; // Only first two succeed
    float expected_current[BMU_NUM] = {100.0f - total_input_current + 0.0f,
                                        100.0f - total_input_current + 5.0f,
                                        100.0f - total_input_current + 0.0f, // Failures
                                        100.0f - total_input_current + 0.0f}; // Failures

    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_FLOAT_EQ(s_bmu_current[i], expected_current[i]);
    }
    EXPECT_EQ(cal_and_adjust_bmu_soc(), SUCCESS);
}
/* Ended by AICoder, pid:4604f7375612156142740b8dc0be453d97770a04 */

/* Started by AICoder, pid:cc822o45bbkf21f145940913d034c16ed7c42133 */
TEST_F(bmu_soc, BMUSocTestNormalOperation) {
    current_time = 100;

    s_bmu_current[0] = 10.0f; // Charging
    s_bmu_current[1] = -10.0f; // Discharging
    s_bmu_current[2] = 0.0f; // No current
    s_bmu_current[3] = 5.0f; // Small current

    EXPECT_EQ(cal_bmu_soc(), SUCCESS);

    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_EQ(s_bmu_cal_soc[i].last_time, current_time);
        EXPECT_GE(s_bmu_cal_soc[i].soc, 0.0f);
        EXPECT_LE(s_bmu_cal_soc[i].soc, 100.0f);
    }
}

TEST_F(bmu_soc, BMUSocTestTimeDeltaEdgeCases) {
    memset(&s_bmu_cal_soc, 0, sizeof(s_bmu_cal_soc));
    current_time = 1000;
    EXPECT_EQ(get_system_time(&current_time), SUCCESS);

    s_bmu_current[0] = 10.0f; // Charging

    // Simulate no time change
    s_bmu_cal_soc[0].last_time = current_time;
    EXPECT_EQ(cal_bmu_soc(), SUCCESS);
    EXPECT_FLOAT_EQ(s_bmu_cal_soc[0].cumulative_ah, 0.0f);

    // Simulate large time jump
    s_bmu_cal_soc[0].last_time = current_time - 301;
    EXPECT_EQ(cal_bmu_soc(), SUCCESS);
    EXPECT_FLOAT_EQ(s_bmu_cal_soc[0].cumulative_ah, 0.0f);

    // Simulate small current
    s_bmu_current[0] = CURRENT_DETECTION_ERROR / 2;
    EXPECT_EQ(cal_bmu_soc(), SUCCESS);
    EXPECT_FLOAT_EQ(s_bmu_cal_soc[0].cumulative_ah, 0.0f);
}

TEST_F(bmu_soc, BMUSocTestSocBoundaryConditions) {
    memset(&s_bmu_cal_soc, 0, sizeof(s_bmu_cal_soc));
    current_time = 2000;
    EXPECT_EQ(get_system_time(&current_time), SUCCESS);

    s_bmu_current[0] = -314.0f * 60 / 0.98; // Large current to exceed 10% SOC
    s_bmu_cal_soc[0].last_time = current_time - 60; // 1 minute ago
    EXPECT_EQ(cal_bmu_soc(), SUCCESS);
    EXPECT_FLOAT_EQ(s_bmu_cal_soc[0].soc, 100.0f);
}

TEST_F(bmu_soc, BMUSocTestSocBoundaryConditions2) {
    memset(&s_bmu_cal_soc, 0, sizeof(s_bmu_cal_soc));
    s_bmu_cal_soc->cumulative_ah = 314.0f;
    current_time = 2000;
    EXPECT_EQ(get_system_time(&current_time), SUCCESS);

    s_bmu_current[0] = 314.0f * 60; // Large negative current to go below 0% SOC
    s_bmu_cal_soc[0].last_time = current_time - 60; // 1 minute ago
    EXPECT_EQ(cal_bmu_soc(), SUCCESS);
    EXPECT_FLOAT_EQ(s_bmu_cal_soc[0].soc, 0.0f);
}

TEST_F(bmu_soc, BMUSocTestGetSystemTimeFailure) {
    // Mock get_system_time to fail
    MOCKER(get_system_time)
        .stubs()
        .will(returnValue(FAILURE));

    EXPECT_EQ(cal_bmu_soc(), FAILURE);
}
/* Ended by AICoder, pid:cc822o45bbkf21f145940913d034c16ed7c42133 */

/* Started by AICoder, pid:jd0d9b81c1a801014e5a0a030025f42ad2d1eb43 */
TEST_F(bmu_soc, IfConfigOptTestOptimizerConfigured) {
    int opt_config_status = 1;

    MOCKER(pdt_get_data)
        .stubs()
        .with(any(), outBoundP((void *)&opt_config_status, sizeof(opt_config_status)))
        .will(returnValue(SUCCESS));
    EXPECT_EQ(TRUE, if_config_opt());
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, IfConfigOptTestOptimizerNotConfigured) {
    int opt_config_status = 0;

    MOCKER(pdt_get_data)
        .stubs()
        .with(any(), outBoundP((void *)&opt_config_status, sizeof(opt_config_status)))
        .will(returnValue(SUCCESS));
    EXPECT_EQ(FALSE, if_config_opt());
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, IfConfigOptTestPdtGetDataFailure) {
    // 模拟pdt_get_data失败的情况
    MOCKER(pdt_get_data)
            .stubs()
            .with(any(), any(), any())
            .will(returnValue(FAILURE));
    EXPECT_EQ(FALSE, if_config_opt());

    // 恢复原始函数
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:jd0d9b81c1a801014e5a0a030025f42ad2d1eb43 */

/* Started by AICoder, pid:673edl8d6ed2186147f30825606eab321992bc89 */
TEST_F(bmu_soc, TestModuleVoltageExceedsThreshold) {
    battery_module_voltage = 231.0f;
    float cell_voltages[BMU_CELL_NUM] = {3.0f, 3.0f, 3.0f, 3.0f};
    EXPECT_TRUE(is_bmu_charge_full(1));
    EXPECT_TRUE(judge_bmu_charge_full_status());
}

TEST_F(bmu_soc, TestCellVoltageExceedsThreshold) {
    battery_module_voltage = 229.0f;
    cell_voltage = 3.66f;
    EXPECT_TRUE(is_bmu_charge_full(1));
    EXPECT_TRUE(judge_bmu_charge_full_status());
}

TEST_F(bmu_soc, TestNoVoltageExceedsThreshold) {
    battery_module_voltage = 229.0f;
    cell_voltage = 3.64f;
    EXPECT_FALSE(is_bmu_charge_full(1));
    EXPECT_FALSE(judge_bmu_charge_full_status());
}

TEST_F(bmu_soc, TestDataFetchFailure) {
    // 模拟pdt_get_data失败的情况
    MOCKER(pdt_get_data)
            .stubs()
            .with(any(), any(), any())
            .will(returnValue(FAILURE));
    EXPECT_EQ(FALSE, is_bmu_charge_full(1));

    // 恢复原始函数
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:673edl8d6ed2186147f30825606eab321992bc89 */

/* Started by AICoder, pid:9f262b4ef0p65f814e7f0855908d515fe4e06247 */
int is_bmu_charge_full_stub(int dev_sn)
{
    if(dev_sn == 1)
    {
        return TRUE;
    }
    return FALSE;
}

TEST_F(bmu_soc, AdjustBMUSocTestBoundaryConditions) {
    memset(&s_bmu_cal_soc, 0, sizeof(s_bmu_cal_soc));
    s_bmu_real_capacity[0] = 314.0f;
    s_bmu_real_capacity[1] = 314.0f;
    MOCKER(is_bmu_charge_full)
        .stubs()
        .will(invoke(is_bmu_charge_full_stub));
    adjust_charge_end_soc(TRUE);
    EXPECT_FLOAT_EQ(100.0f, s_bmu_cal_soc[0].soc);
    EXPECT_FLOAT_EQ(s_bmu_real_capacity[0], s_bmu_cal_soc[0].cumulative_ah);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, AdjustBMUSocTestConfigOptFalseChargeFullStatusTrue) {
    memset(&s_bmu_cal_soc, 0, sizeof(s_bmu_cal_soc));
    s_bmu_real_capacity[0] = 314.0f;
    s_bmu_real_capacity[1] = 314.0f;
    MOCKER(is_bmu_charge_full)
        .stubs()
        .will(invoke(is_bmu_charge_full_stub));
    adjust_charge_end_soc(FALSE);
    EXPECT_FLOAT_EQ(100.0f, s_bmu_cal_soc[0].soc);
    EXPECT_FLOAT_EQ(100.0f, s_bmu_cal_soc[1].soc);
    EXPECT_FLOAT_EQ(s_bmu_real_capacity[0], s_bmu_cal_soc[0].cumulative_ah);
    EXPECT_FLOAT_EQ(s_bmu_real_capacity[1], s_bmu_cal_soc[1].cumulative_ah);
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:9f262b4ef0p65f814e7f0855908d515fe4e06247 */

/* Started by AICoder, pid:335ebpf7b2yc1c91472308ed701544793a653057 */
TEST_F(bmu_soc, AdjustBMUSocTestIfConfigOptTrue) {
    MOCKER(is_bmu_charge_full)
        .stubs()
        .will(returnValue(TRUE));
    MOCKER(is_bmu_discharge_full)
        .stubs()
        .will(returnValue(FALSE));

    adjust_discharge_end_soc(TRUE);
    EXPECT_FLOAT_EQ(100.0f, s_bmu_cal_soc[0].soc);
    EXPECT_FLOAT_EQ(s_bmu_real_capacity[0], s_bmu_cal_soc[0].cumulative_ah);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, AdjustBMUSocTestIfConfigOptFalseChargeFull) {
    MOCKER(judge_bmu_charge_full_status)
        .stubs()
        .will(returnValue(TRUE));
    MOCKER(judge_bmu_discharge_full_status)
        .stubs()
        .will(returnValue(FALSE));

    adjust_discharge_end_soc(FALSE);
    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_FLOAT_EQ(100.0f, s_bmu_cal_soc[i].soc);
        EXPECT_FLOAT_EQ(s_bmu_real_capacity[i], s_bmu_cal_soc[i].cumulative_ah);
    }
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, AdjustBMUSocTestIfConfigOptFalseDischargeFull) {
    MOCKER(judge_bmu_charge_full_status)
        .stubs()
        .will(returnValue(FALSE));
    MOCKER(judge_bmu_discharge_full_status)
        .stubs()
        .will(returnValue(TRUE));

    adjust_discharge_end_soc(FALSE);
    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_FLOAT_EQ(0.0f, s_bmu_cal_soc[i].soc);
        EXPECT_FLOAT_EQ(0.0f, s_bmu_cal_soc[i].cumulative_ah);
    }
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, AdjustBMUSocTestIfConfigOptFalseNoFull) {
    MOCKER(judge_bmu_charge_full_status)
        .stubs()
        .will(returnValue(FALSE));
    MOCKER(judge_bmu_discharge_full_status)
        .stubs()
        .will(returnValue(FALSE));

        adjust_discharge_end_soc(FALSE);
    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_FLOAT_EQ(0.0f, s_bmu_cal_soc[i].soc);
        EXPECT_FLOAT_EQ(0.0f, s_bmu_cal_soc[i].cumulative_ah);
    }
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:335ebpf7b2yc1c91472308ed701544793a653057 */

/* Started by AICoder, pid:be8a01954abcdd4142be0ba83056c5372a9565d3 */
TEST_F(bmu_soc, SaveSocToFileTestTimeLessThan60Seconds) {
    current_time = 1000;
    s_pre_time = 1010;
    EXPECT_EQ(FAILURE, is_soc_saving_needed());
}

TEST_F(bmu_soc, SaveSocToFileTestNoSOCChange) {
    current_time = 1000;
    s_pre_time = current_time - 60; // Ensure time difference is > 60 seconds
    for (int i = 0; i < BMU_NUM; ++i) {
        s_bmu_cal_soc[i].soc = 50.0f;
        s_bmu_soc_save.soc_save[i] = 50.0f;
    }
    EXPECT_EQ(SUCCESS, is_soc_saving_needed());
}

TEST_F(bmu_soc, SaveSocToFileTestSOCChange) {
    current_time = 1000;
    s_pre_time = current_time - 120; // Ensure time difference is > 60 seconds
    for (int i = 0; i < BMU_NUM; ++i) {
        s_bmu_cal_soc[i].soc = 50.0f;
        s_bmu_soc_save.soc_save[i] = 49.0f; // Simulate a change in SOC
    }
    EXPECT_EQ(SUCCESS, is_soc_saving_needed());
}

TEST_F(bmu_soc, SaveSocToFileTestGetSystemTimeFailure) {
    MOCKER(get_system_time)
        .stubs()
        .will(returnValue(FAILURE));

    EXPECT_EQ(FAILURE, is_soc_saving_needed());

    // Restore original function
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:be8a01954abcdd4142be0ba83056c5372a9565d3 */

/* Started by AICoder, pid:a96a7b2233d241ea8c2adf90ef58a423 */
// 测试用例
TEST_F(bmu_soc, CalculateBmuSoh) {
    // 调用待测函数
    int result = calculate_bmu_soh();

    // 验证结果
    EXPECT_EQ(result, SUCCESS);
}
/* Ended by AICoder, pid:a96a7b2233d241ea8c2adf90ef58a423 */

/* Started by AICoder, pid:cd5edc1aa438493ca6adf8d6e5c1d79b */
TEST_F(bmu_soc, TestEmptyArray) {
    float_type_t arr[] = {};
    float result = calc_average_temp(arr, 0);
    EXPECT_FLOAT_EQ(result, 0.0);
}

TEST_F(bmu_soc, TestSingleElement) {
    float_type_t arr[1] = {{1.0}};
    float result = calc_average_temp(arr, 1);
    EXPECT_FLOAT_EQ(result, 1.0);
}

TEST_F(bmu_soc, TestMultipleElements) {
    float_type_t arr[3] = {{1.0}, {2.0}, {3.0}};
    float result = calc_average_temp(arr, 3);
    EXPECT_FLOAT_EQ(result, 2.0);
}

TEST_F(bmu_soc, TestNegativeNumbers) {
    float_type_t arr[3] =  {{-1.0}, {-2.0}, {-3.0}};
    float result = calc_average_temp(arr, 3);
    EXPECT_FLOAT_EQ(result, -2.0);
}

TEST_F(bmu_soc, TestNullArray) {
    float result = calc_average_temp(NULL, 5);
    EXPECT_FLOAT_EQ(result, 0.0);
}

TEST_F(bmu_soc, TestInvalidSize) {
    float_type_t arr[1] = {{-1.0}};
    float result = calc_average_temp(arr, -1);
    EXPECT_FLOAT_EQ(result, 0.0);
}
/* Ended by AICoder, pid:cd5edc1aa438493ca6adf8d6e5c1d79b */


/* Started by AICoder, pid:4b293c5227a9453eb36095522fdaa044 */
TEST_F(bmu_soc, TestCalculateCalenderLifeDecline) {
    bmu_sox_deal_t bmu_sox_deal = {0};
    bcmu_save_t bmu_soh_save = {0};
    bmu_sox_info_t bmu_sox_in = {0};
    int bmu_index = 0;

    // Test case 1: bmu_soh_decline_data is NULL
    EXPECT_EQ(calculate_calender_life_decline(NULL, &bmu_soh_save, &bmu_sox_in, bmu_index), FAILURE);

    // Test case 2: bmu_soh_save is NULL
    EXPECT_EQ(calculate_calender_life_decline(&bmu_sox_deal, NULL, &bmu_sox_in, bmu_index), FAILURE);

    // Test case 2: bmu_sox_in is NULL
    EXPECT_EQ(calculate_calender_life_decline(&bmu_sox_deal, &bmu_soh_save, NULL, bmu_index), FAILURE);

    // Test case 3: calender_decline_seconds < ONE_DAY_SECONDS
    bmu_sox_deal.calender_decline_seconds = 3999;
    EXPECT_EQ(calculate_calender_life_decline( &bmu_sox_deal, &bmu_soh_save, &bmu_sox_in, bmu_index), SUCCESS);
    EXPECT_EQ(bmu_sox_deal.calender_decline_seconds, 4000);

    // Test case 4: calender_decline_seconds == ONE_DAY_SECONDS
    bmu_sox_deal.calender_decline_seconds = 24*60*60;
    EXPECT_EQ(calculate_calender_life_decline( &bmu_sox_deal, &bmu_soh_save, &bmu_sox_in, bmu_index), SUCCESS);
    EXPECT_EQ(bmu_sox_deal.calender_decline_seconds, 0);

    // Test case 5: calender_decline_seconds > ONE_DAY_SECONDS
    bmu_sox_deal.calender_decline_seconds = 24*60*60 +1;
    EXPECT_EQ(calculate_calender_life_decline(&bmu_sox_deal, &bmu_soh_save, &bmu_sox_in, bmu_index), SUCCESS);
    EXPECT_EQ(bmu_sox_deal.calender_decline_seconds, 0);
}

/* Ended by AICoder, pid:4b293c5227a9453eb36095522fdaa044 */


/* Started by AICoder, pid:95c9a00998b0498989074586e67ddd79 */
TEST_F(bmu_soc, plus_calender_life_decline) {
    bmu_sox_deal_t bmu_sox_deal = {0};
    bcmu_save_t bmu_soh_save = {0};
    bmu_sox_info_t bmu_sox_in = {0};
    int bmu_index = 0;
    bmu_sox_in.bmu_sox_data.cell_average_temperature.f_value = 30.0;

    MOCKER(pdt_get_time)
        .stubs()
        .with(eq(&bmu_sox_deal.last_calc_calender_time))
        .will(returnValue(0));

    MOCKER(write_bmu_soh_info_to_file)
        .stubs()
        .with(eq(&bmu_soh_save))
        .will(returnValue(0));

    int ret = plus_calender_life_decline(&bmu_sox_deal, &bmu_soh_save, &bmu_sox_in, bmu_index);
    EXPECT_EQ(ret, SUCCESS);

    // 检查计算结果是否符合预期
    EXPECT_NEAR(bmu_sox_deal.calender_life_decline, 10.0 / (27.5 - 0.5 * 30.0) / 365, 0.0001);

    GlobalMockObject::verify();
}

/* Ended by AICoder, pid:95c9a00998b0498989074586e67ddd79 */

/* Started by AICoder, pid:r2ec23b7a7bb2ce146150a9eb022414e58175961 */
TEST_F(bmu_soc, deal_calender_decline_when_start_NormalCase) {
    MOCKER(get_diff_days)
        .stubs()
        .will(returnValue(100));
    EXPECT_EQ(deal_calendar_decline_when_start(), SUCCESS);
}

TEST_F(bmu_soc, deal_calender_decline_when_start_OneYearBoundary) {
    MOCKER(get_diff_days)
        .stubs()
        .will(returnValue(ONE_YEAR_DAYS));
    EXPECT_EQ(deal_calendar_decline_when_start(), SUCCESS);
}

TEST_F(bmu_soc, deal_calender_decline_when_start_OneDayBoundary) {
    MOCKER(get_diff_days)
        .stubs()
        .will(returnValue(1));
    EXPECT_EQ(deal_calendar_decline_when_start(), SUCCESS);
}

TEST_F(bmu_soc, deal_calender_decline_when_start_InvalidDiffDays) {
    MOCKER(get_diff_days)
        .stubs()
        .will(returnValue(ONE_YEAR_DAYS + 1));
    EXPECT_EQ(deal_calendar_decline_when_start(), SUCCESS);

    MOCKER(get_diff_days)
        .stubs()
        .will(returnValue(0));
    EXPECT_EQ(deal_calendar_decline_when_start(), SUCCESS);
}
/* Ended by AICoder, pid:r2ec23b7a7bb2ce146150a9eb022414e58175961 */

/* Started by AICoder, pid:c63bd0a5500e4772b1946a9eb495f983 */
TEST_F(bmu_soc, test_write_bmu_soh_info_to_file) {
    bcmu_save_t bmu_soh_save_tmp = {0};
    bcmu_save_t bmu_soh_save = {0};

    MOCKER(plat_memset_s)
        .stubs()
        .with(any(), any(), any(), any())
        .will(returnValue(0));

    MOCKER(plat_memcpy_s)
        .stubs()
        .with(any(), any(), any(), any())
        .will(returnValue(0));

    MOCKER(calculate_crc32)
        .stubs()
        .with(any(), any())
        .will(returnValue(0));

    MOCKER(write_file)
        .stubs()
        .with(any(), any(), any())
        .will(returnValue(SUCCESS));
    

    EXPECT_EQ(write_bmu_soh_info_to_file(&bmu_soh_save_tmp), TRUE);
    GlobalMockObject::verify();

    MOCKER(write_file)
        .stubs()
        .with(any(), any(), any())
        .will(returnValue(FAILURE));

    EXPECT_EQ(write_bmu_soh_info_to_file(&bmu_soh_save_tmp), FALSE);

    GlobalMockObject::verify();
}

/* Ended by AICoder, pid:c63bd0a5500e4772b1946a9eb495f983 */

/* Started by AICoder, pid:rb79bec3ddh0a6d14e8209b8100db33ddc46e2c7 */
TEST_F(bmu_soc, NullPointer) {
    EXPECT_EQ(read_bmu_soh_info_from_file(NULL), FALSE);
}

TEST_F(bmu_soc, ReadFileFailure) {
    bcmu_save_t test_data;
    MOCKER(read_file)
        .stubs()
        .with(any(), any(), any())
        .will(returnValue(FAILURE));
    // Simulate file read failure
    EXPECT_EQ(read_bmu_soh_info_from_file(&test_data), FALSE);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, CRCMismatch) {
    bcmu_save_t test_data;
    // Write valid data to file
    test_data.crc = calculate_crc32((char *)&test_data, sizeof(bcmu_save_t) - sizeof(int));

    // Invalidate CRC
    test_data.crc++;

    EXPECT_EQ(read_bmu_soh_info_from_file(&test_data), FALSE);
}

TEST_F(bmu_soc, ValidRead) {
    bcmu_save_t test_data;
    // Write valid data to file
    test_data.crc = calculate_crc32((char *)&test_data, sizeof(bcmu_save_t) - sizeof(int));
    MOCKER(read_file)
        .stubs()
        .with(any(), any(), any())
        .will(returnValue(SUCCESS));

    EXPECT_EQ(read_bmu_soh_info_from_file(&test_data), TRUE);
}
/* Ended by AICoder, pid:rb79bec3ddh0a6d14e8209b8100db33ddc46e2c7 */


/* Started by AICoder, pid:00e94c7d6dac4cb69c7028967a3af3f7 */
TEST_F(bmu_soc, TestCalAdjustSoh) {
    bmu_sox_info_t batt_info;
    bmu_sox_deal_t batt_deal;

    // Test case 1: Return FALSE when batt_info is NULL
    EXPECT_EQ(cal_adjust_soh(NULL, &batt_deal), FAILURE);

    // Test case 2: Return FALSE when batt_deal is NULL
    EXPECT_EQ(cal_adjust_soh(&batt_info, NULL), FAILURE);

    // Test case 3: Return TRUE when conditions are not met
    batt_info.bmu_sox_para.batt_rated_cap.i_value = 10;
    batt_info.bmu_sox_data.cell_ocv.f_value = 10;
    batt_info.bmu_sox_data.cell_average_temperature.f_value = 10;
    batt_deal.discharge_from_full = true;
    batt_deal.discharge_cap_for_celebrate = 10;
    EXPECT_EQ(cal_adjust_soh(&batt_info, &batt_deal), FAILURE);

    // Test case 3: Return TRUE when conditions are not met
    batt_info.bmu_sox_para.batt_rated_cap.i_value = 10;
    batt_info.bmu_sox_data.cell_ocv.f_value = 1;
    batt_deal.discharge_from_full = true;
    batt_deal.discharge_cap_for_celebrate = 10;
    batt_info.bmu_sox_data.cell_average_temperature.f_value = 10;
    EXPECT_EQ(cal_adjust_soh(&batt_info, &batt_deal), FAILURE);

    // Test case 4: Return TRUE when conditions are met and temperature is greater than or equal to 15
    batt_info.bmu_sox_para.batt_rated_cap.i_value = 10;
    batt_info.bmu_sox_data.cell_ocv.f_value = 2.5;
    batt_deal.discharge_from_full = FALSE;
    batt_deal.discharge_cap_for_celebrate = 10;
    batt_info.bmu_sox_data.cell_average_temperature.f_value = 20;
    batt_deal.added_life_decline = 10;
    batt_deal.calender_life_decline = 10;
    EXPECT_EQ(cal_adjust_soh(&batt_info, &batt_deal), FAILURE);

    // Test case 4: Return TRUE when conditions are met and temperature is greater than or equal to 15
    batt_info.bmu_sox_para.batt_rated_cap.i_value = 10;
    batt_info.bmu_sox_data.cell_ocv.f_value = 2.5;
    batt_deal.discharge_from_full = true;
    batt_deal.discharge_cap_for_celebrate = 7;
    batt_info.bmu_sox_data.cell_average_temperature.f_value = 20;
    batt_deal.added_life_decline = 10;
    batt_deal.calender_life_decline = 10;
    EXPECT_EQ(cal_adjust_soh(&batt_info, &batt_deal), FAILURE);

    // Test case 4: Return TRUE when conditions are met and temperature is greater than or equal to 15
    batt_info.bmu_sox_para.batt_rated_cap.i_value = 10;
    batt_info.bmu_sox_data.cell_ocv.f_value = 2.5;
    batt_deal.discharge_from_full = true;
    batt_deal.discharge_cap_for_celebrate = 10;
    batt_info.bmu_sox_data.cell_average_temperature.f_value = -1;
    batt_deal.added_life_decline = 10;
    batt_deal.calender_life_decline = 10;
    EXPECT_EQ(cal_adjust_soh(&batt_info, &batt_deal), FAILURE);

    // Test case 5: Return TRUE when conditions are met and temperature is less than 15
    batt_info.bmu_sox_para.batt_rated_cap.i_value = 10;
    batt_info.bmu_sox_data.cell_ocv.f_value = 2.5;
    batt_deal.discharge_from_full = true;
    batt_deal.discharge_cap_for_celebrate = 300;
    batt_info.bmu_sox_data.cell_average_temperature.f_value = 10;
    batt_deal.added_life_decline = 10;
    batt_deal.calender_life_decline = 10;
    EXPECT_EQ(cal_adjust_soh(&batt_info, &batt_deal), SUCCESS);
}

/* Ended by AICoder, pid:00e94c7d6dac4cb69c7028967a3af3f7 */

/* Started by AICoder, pid:3f72c19d1207467ab57f9f98ffcab0f7 */
TEST_F(bmu_soc, test_get_cell_dcr) {
    EXPECT_FLOAT_EQ(get_cell_dcr(-10.0), 20.0);
    EXPECT_FLOAT_EQ(get_cell_dcr(0), 12.0);
    EXPECT_FLOAT_EQ(get_cell_dcr(10), 6.0);
    EXPECT_FLOAT_EQ(get_cell_dcr(15), 4.0);
    EXPECT_FLOAT_EQ(get_cell_dcr(25), 3.0);
    EXPECT_FLOAT_EQ(get_cell_dcr(45), 2.0);
    EXPECT_FLOAT_EQ(get_cell_dcr(50), 2.0);
}

/* Ended by AICoder, pid:3f72c19d1207467ab57f9f98ffcab0f7 */

/* Started by AICoder, pid:f42e192558274008a42f7af1b57d606d */
TEST_F(bmu_soc, update_bcmu_soc) {
    int bcmu_soc = 0;
    // 模拟数据
    s_bmu_cal_soc[0].soc = 50.0f;
    s_bmu_cal_soc[1].soc = 75.0f;
    s_bmu_cal_soc[2].soc = 25.0f;
    s_bmu_cal_soc[3].soc = 80.0f;

    // 调用待测函数
    int ret = update_bcmu_soc();

    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &bcmu_soc, sizeof(int));

    // 验证返回值
    EXPECT_EQ(ret, SUCCESS);
    EXPECT_EQ(bcmu_soc, s_bmu_cal_soc[2].soc);

    // 验证pdt_set_data是否被正确调用
    GlobalMockObject::verify();
}

/* Ended by AICoder, pid:f42e192558274008a42f7af1b57d606d */

/* Started by AICoder, pid:s5b8521486c0a70143a60aa6f0f2be2f0c04031e */
TEST_F(bmu_soc, InitSoeTest) {
    // 从文件读成功
    bcmu_save_t s_mock_save_bak = {0};
    set_mock_soe_data_bak(&s_mock_save_bak, 33, 0.9, 0.8, 666, 0);
    MOCKER(read_bmu_soh_info_from_file).stubs().with(outBoundP(&s_mock_save_bak, sizeof(s_mock_save_bak))).will(returnValue(TRUE));
    EXPECT_EQ(TRUE, init_bmu_sox_save());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 33);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe_chg_efficiency, 0.9);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe_dischg_efficiency, 0.8);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].cumulative_wh, 666);
    GlobalMockObject::verify();
    // 从文件读失败
    MOCKER(read_bmu_soh_info_from_file).stubs().will(returnValue(FALSE));
    EXPECT_EQ(TRUE, init_bmu_sox_save());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[3].soe, 50);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[3].soe_chg_efficiency, 1);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[3].soe_dischg_efficiency, 1);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[3].cumulative_wh, NOMINAL_ENERGY / 2);
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:s5b8521486c0a70143a60aa6f0f2be2f0c04031e */

/* Started by AICoder, pid:5c394b9080j27f8146ba0a58a0369787ae34c3b8 */
TEST_F(bmu_soc, CalculateSoeTest) {
    sid sid_temp = 0;
    float wh_expect = 0;
    float Emax_expect = 0;
    float soe_expect = 0;

    // 第一次上电
    MOCKER(read_bmu_soh_info_from_file).stubs().will(returnValue(FALSE));
    EXPECT_EQ(TRUE, init_bmu_sox_save());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 50);

    // 读取时间失败
    MOCKER(get_system_time).stubs().will(returnValue(FAILURE));
    EXPECT_EQ(FAILURE, cal_bmu_soe());
    GlobalMockObject::verify();

    // 第一次执行
    current_time = 1000;
    EXPECT_EQ(SUCCESS, cal_bmu_soe());
    /// 第一次赋值上次计算时间
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].last_time, 1000);
    /// 计算数值不变
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 50);
    /// 发生时间设置，时间跳变
    current_time = 2000;
    EXPECT_EQ(SUCCESS, cal_bmu_soe());
    /// 时间调整
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].last_time, 2000);
    /// 计算数值不变
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 50);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].cumulative_wh, NOMINAL_ENERGY / 2);

    // 第二次执行
    current_time = 2004;
    battery_module_voltage = 231.0f;
    set_mock_bmu_current(-10.0f); // 充电
    /// BMU的电压采集不到
    MOCKER(pdt_get_data).stubs().will(returnValue(FAILURE));  //.with(eq(sid_temp), any(), any())
    EXPECT_EQ(SUCCESS, cal_bmu_soe());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].last_time, 2000);  // 未完成统计，上次计算时间不变
    EXPECT_FLOAT_EQ(s_bmu_real_energy[0], 0);  // Emax未计算
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 50);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].cumulative_wh, NOMINAL_ENERGY / 2);
    GlobalMockObject::verify();

    // 第三次执行
    current_time = 2008;
    s_mock_soh_set = 0.0f;
    // SOH没获取到，或者SOH还未计算完成，导致Emax错误计算为0的情况
    EXPECT_EQ(SUCCESS, cal_bmu_soe());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].last_time, 2000);  // 未完成统计，上次计算时间不变
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 50);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].cumulative_wh, NOMINAL_ENERGY / 2);

    // 第四次执行
    // SOH正常读取到
    current_time = 2012;
    s_mock_soh_set = 100.0f;
    battery_module_voltage = 231.0f;
    set_mock_bmu_current(-10.0f); // 充电
    // 注： delta_wh = UI * time_delta / 3600.0f * chg_efficiency
    wh_expect = NOMINAL_ENERGY / 2 + 10 * 231 * (2012 - 2000) / 3600.0f * 1;
    // 注： Emax = NOMINAL_ENERGY * SOH / 100.0f
    Emax_expect = NOMINAL_ENERGY;
    // 注： SOE = WH / Emax * 100
    soe_expect = wh_expect / Emax_expect * 100.0f;
    printf("charge wh_expect = %f, Emax expect = %f, soe_expect = %f\n", wh_expect, Emax_expect, soe_expect);
    EXPECT_EQ(SUCCESS, cal_bmu_soe());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].last_time, 2012);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].cumulative_wh, wh_expect);
    EXPECT_FLOAT_EQ(s_bmu_real_energy[0], Emax_expect);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, soe_expect);

    // 静置场景
    current_time = 2016;
    set_mock_bmu_current(1.0f); // 满足静置
    EXPECT_EQ(SUCCESS, cal_bmu_soe());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].last_time, 2016);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].cumulative_wh, wh_expect);
    EXPECT_FLOAT_EQ(s_bmu_real_energy[0], Emax_expect);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, soe_expect);

    // 放电场景
    current_time = 2020;
    set_mock_bmu_current(11.0f); // 放电
    wh_expect = s_bmu_cal_soe[0].cumulative_wh - 11 * 231 * (2020 - 2016) / 3600.0f / 1;
    Emax_expect = NOMINAL_ENERGY;
    soe_expect = wh_expect / Emax_expect * 100.0f;
    printf("discharge wh_expect = %f, Emax expect = %f, soe_expect = %f\n", wh_expect, Emax_expect, soe_expect);
    EXPECT_EQ(SUCCESS, cal_bmu_soe());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].last_time, 2020);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].cumulative_wh, wh_expect);
    EXPECT_FLOAT_EQ(s_bmu_real_energy[0], Emax_expect);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, soe_expect);
}
/* Ended by AICoder, pid:5c394b9080j27f8146ba0a58a0369787ae34c3b8 */

/* Started by AICoder, pid:oc074bba7cw0e95145670a0300014f11ec12c63b */
TEST_F(bmu_soc, CalAndAdjustBmuSoeTest) {
    // 计算电流失败
    MOCKER(cal_batt_current).stubs().will(returnValue(FAILURE));
    EXPECT_EQ(FAILURE, cal_and_adjust_bmu_soe());
    GlobalMockObject::verify();
    // 计算成功
    MOCKER(cal_batt_current).stubs().will(returnValue(SUCCESS));
    MOCKER(cal_bmu_soe).stubs().will(returnValue(SUCCESS));
    EXPECT_EQ(SUCCESS, cal_and_adjust_bmu_soe());
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:oc074bba7cw0e95145670a0300014f11ec12c63b */

TEST_F(bmu_soc, AdjustBmuSoeByOcvTest) {
    // 电量不低，不做OCV曲线拟合校正
    bcmu_save_t s_mock_save_bak = {0};
    set_mock_soe_data_bak(&s_mock_save_bak, 33, 0.9, 0.8, 666, 0);
    MOCKER(read_bmu_soh_info_from_file).stubs().with(outBoundP(&s_mock_save_bak, sizeof(s_mock_save_bak))).will(returnValue(TRUE));
    EXPECT_EQ(TRUE, init_bmu_sox_save());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 33);
    EXPECT_EQ(SUCCESS, adjust_bmu_soe_by_ocv());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 33);  // 没做修正
    GlobalMockObject::verify();
    // 电量低，满足校正条件
    // 通信异常
    set_mock_soe_data_bak(&s_mock_save_bak, 3, 0.9, 0.8, 666, 0);
    MOCKER(read_bmu_soh_info_from_file).stubs().with(outBoundP(&s_mock_save_bak, sizeof(s_mock_save_bak))).will(returnValue(TRUE));
    MOCKER(is_bmu_comm_normal).stubs().will(returnValue(FAULT));
    EXPECT_EQ(TRUE, init_bmu_sox_save());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 3);
    EXPECT_EQ(SUCCESS, adjust_bmu_soe_by_ocv());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 3);  // 没做修正
    GlobalMockObject::verify();
    // 通信正常，SOC低，满足校正条件, 但非静置状态
    MOCKER(is_bmu_comm_normal).stubs().will(returnValue(SUCCESS));
    set_mock_bmu_current(-10.0f); // 充电
    set_mock_bmu_ocv(3.1f);
    EXPECT_EQ(SUCCESS, adjust_bmu_soe_by_ocv());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 3);  // 没做修正
    GlobalMockObject::verify();

    // 通信正常，SOC低，满足校正条件, 满足静置状态
    MOCKER(is_bmu_comm_normal).stubs().will(returnValue(SUCCESS));
    set_mock_bmu_current(-1.0f);
    set_mock_bmu_ocv(3.1f);
    s_bmu_sox_deal[0].save_to_file = FALSE;
    /// 等待时间未到
    for (int i = 0; i < SOE_CAL_STANDY_KEEP_COUNT - 1; i++)
    {
        adjust_bmu_soe_by_ocv();
    }
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 3);  // 没做修正
    /// 等待时间到了
    float soe_adjust_expect = ocv_to_soc(3.1) * s_bmu_real_energy[0] / NOMINAL_ENERGY;
    adjust_bmu_soe_by_ocv();
    printf("soe_adjust_expect = %f, adjusted soe = %f \n", soe_adjust_expect, s_bmu_cal_soe[0].soe);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, soe_adjust_expect);  // 做了修正
    EXPECT_FLOAT_EQ(s_bmu_sox_deal[0].save_to_file, TRUE);

    /// 再次执行,数值不变，不重复存储
    /* Started by AICoder, pid:xedbem36eeh59d314f3e093a70382d077fe9845f */
    s_bmu_sox_deal[0].save_to_file = FALSE;
    adjust_bmu_soe_by_ocv();
    printf("soe_adjust_expect = %f, adjusted soe = %f \n", soe_adjust_expect, s_bmu_cal_soe[0].soe);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, soe_adjust_expect);
    EXPECT_FLOAT_EQ(s_bmu_sox_deal[0].save_to_file, FALSE);
    GlobalMockObject::verify();
    /* Ended by AICoder, pid:xedbem36eeh59d314f3e093a70382d077fe9845f */
}

/* Started by AICoder, pid:pcea54dced360fb147a608cdb0ac283141b83878 */
TEST_F(bmu_soc, AdjustBmuSoeEfficiencyTest) {
    bcmu_save_t s_mock_save_bak = {0};
    set_mock_soe_data_bak(&s_mock_save_bak, 96, 1.0f, 1.0f, 666, 0);
    MOCKER(read_bmu_soh_info_from_file).stubs().with(outBoundP(&s_mock_save_bak, sizeof(s_mock_save_bak))).will(returnValue(TRUE));
    EXPECT_EQ(TRUE, init_bmu_sox_save());
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe, 96);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].cumulative_wh, 666);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe_chg_efficiency, 1.0f);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe_dischg_efficiency, 1.0f);
    // 因soh未计算，Emax=0情况的过滤
    s_bmu_real_energy[0] = 0.0f;
    EXPECT_EQ(SUCCESS, adjust_bmu_soe_efficiency());
    // 满充
    float chg_eff_ajust_expect = 666 / NOMINAL_ENERGY;
    s_bmu_sox_deal[0].save_to_file = FALSE;
    s_bmu_real_energy[0] = NOMINAL_ENERGY;  // 取SOH=100%时电量 1004.8
    s_bmu_cal_soc[0].soc = 100;
    EXPECT_EQ(SUCCESS, adjust_bmu_soe_efficiency());
    printf("chg_eff_ajust_expect = %f, adjusted chg_eff = %f \n", chg_eff_ajust_expect, s_bmu_cal_soe[0].soe_chg_efficiency);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe_chg_efficiency, chg_eff_ajust_expect);
    EXPECT_FLOAT_EQ(s_bmu_sox_deal[0].save_to_file, TRUE);
    // 满放
    float dis_eff_ajust_expect = (NOMINAL_ENERGY - 666) / NOMINAL_ENERGY;
    s_bmu_sox_deal[0].save_to_file = FALSE;
    s_bmu_cal_soc[0].soc = 0;
    EXPECT_EQ(SUCCESS, adjust_bmu_soe_efficiency());
    printf("dis_eff_ajust_expect = %f, adjusted dischg_eff = %f \n", dis_eff_ajust_expect, s_bmu_cal_soe[0].soe_dischg_efficiency);
    EXPECT_FLOAT_EQ(s_bmu_cal_soe[0].soe_dischg_efficiency, dis_eff_ajust_expect);
    EXPECT_FLOAT_EQ(s_bmu_sox_deal[0].save_to_file, TRUE);

    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:pcea54dced360fb147a608cdb0ac283141b83878 */
// 测试用例1：优化器关闭时的基本功能
TEST_F(bmu_soc, ClusterSoeOptimizeDisabled) {
    // 设置预期行为
    MOCKER(calculate_soe_rack1)
        .stubs()
        .will(returnValue(60.0f));
    
    MOCKER(if_opt_open)
        .stubs()
        .will(returnValue(FALSE));
    
    MOCKER(pdt_set_data)
        .stubs()
        .will(returnValue(SUCCESS));

    // 执行测试
    float result = calculate_cluster_soe(test_bmu_soe, test_bmu_energy, 
                                       &last_cluster_soe, &last_opt_status, &is_limiting);

    // 验证结果
    EXPECT_FLOAT_EQ(result, 60.0f);
    EXPECT_FLOAT_EQ(last_cluster_soe, 60.0f);
    EXPECT_FALSE(last_opt_status);
    GlobalMockObject::verify();
}

// 测试用例2：优化器开启时的功能
TEST_F(bmu_soc, ClusterSoeOptimizeEnabled) {
    // 设置预期行为
    MOCKER(calculate_soe_rack1)
        .stubs()
        .will(returnValue(60.0f));
    
    MOCKER(if_opt_open)
        .stubs()
        .will(returnValue(TRUE));
    
    MOCKER(calculate_soe_rack2)
        .stubs()
        .will(returnValue(55.0f));
    
    MOCKER(pdt_set_data)
        .stubs()
        .will(returnValue(SUCCESS));

    // 执行测试
    float result = calculate_cluster_soe(test_bmu_soe, test_bmu_energy, 
                                       &last_cluster_soe, &last_opt_status, &is_limiting);

    // 验证结果
    EXPECT_FLOAT_EQ(result, 55.0f);  // 应该选择较小值
    EXPECT_FLOAT_EQ(last_cluster_soe, 55.0f);
    EXPECT_TRUE(last_opt_status);
    GlobalMockObject::verify();
}

// 测试用例3：优化器状态变化触发限幅
TEST_F(bmu_soc, ClusterSoeOptimizeStateChange) {
    last_opt_status = TRUE;  // 上一次状态为开启

    // 设置预期行为
    MOCKER(calculate_soe_rack1)
        .stubs()
        .will(returnValue(60.0f));
    
    MOCKER(if_opt_open)
        .stubs()
        .will(returnValue(FALSE));  // 当前状态为关闭
    
    MOCKER(limit_soe_change)
        .stubs()
        .with(any(), any(),any())
        .will(returnValue(55.0f));
    
    MOCKER(pdt_set_data)
        .stubs()
        .will(returnValue(SUCCESS));

    // 执行测试
    float result = calculate_cluster_soe(test_bmu_soe, test_bmu_energy, 
                                       &last_cluster_soe, &last_opt_status, &is_limiting);

    // 验证结果
    EXPECT_FLOAT_EQ(result, 55.0f);
    EXPECT_FLOAT_EQ(last_cluster_soe, 55.0f);
    EXPECT_FALSE(last_opt_status);
    GlobalMockObject::verify();
}

// 测试用例4：正在限幅过程中
TEST_F(bmu_soc, ClusterSoeLimitingInProgress) {
    is_limiting = TRUE;

    // 设置预期行为
    MOCKER(calculate_soe_rack1)
        .stubs()
        .will(returnValue(60.0f));
    
    MOCKER(if_opt_open)
        .stubs()
        .will(returnValue(FALSE));
    
    MOCKER(limit_soe_change)
        .stubs()
        .with(any(), any(),any())
        .will(returnValue(52.0f));
    
    MOCKER(pdt_set_data)
        .stubs()
        .will(returnValue(SUCCESS));

    // 执行测试
    float result = calculate_cluster_soe(test_bmu_soe, test_bmu_energy, 
                                       &last_cluster_soe, &last_opt_status, &is_limiting);

    // 验证结果
    EXPECT_FLOAT_EQ(result, 52.0f);
    EXPECT_FLOAT_EQ(last_cluster_soe, 52.0f);
    EXPECT_FALSE(last_opt_status);
    GlobalMockObject::verify();
}

// 测试用例5：数据设置失败
TEST_F(bmu_soc, ClusterSoeSetDataFailure) {
    // 设置预期行为
    MOCKER(calculate_soe_rack1)
        .stubs()
        .will(returnValue(60.0f));
    
    MOCKER(if_opt_open)
        .stubs()
        .will(returnValue(FALSE));
    
    MOCKER(pdt_set_data)
        .stubs()
        .will(returnValue(FAILURE));

    // 执行测试
    float result = calculate_cluster_soe(test_bmu_soe, test_bmu_energy, 
                                       &last_cluster_soe, &last_opt_status, &is_limiting);

    // 验证结果
    EXPECT_FLOAT_EQ(result, 60.0f);  // 即使设置失败也应返回计算结果
    EXPECT_FLOAT_EQ(last_cluster_soe, 60.0f);
    GlobalMockObject::verify();
}

// 测试用例1：所有BMU通信正常，SOE值各不相同
TEST_F(bmu_soc, SoeRack1_AllNormal) {
    // 设置测试数据
    for (int i = 0; i < BMU_NUM; i++) {
        test_bmu_soe[i].soe = 60.0f + i * 10.0f;  // 60, 70, 80, 90
    }

    // 模拟所有BMU通信正常
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 执行测试
    float result = calculate_soe_rack1(test_bmu_soe);

    // 验证结果
    // soe_min = 60.0f, soe_max = 90.0f
    // result = 60.0 * 100.0 / (100.0 + 60.0 - 90.0) = 85.71f
    EXPECT_NEAR(result, 85.71f, 0.01f);
    GlobalMockObject::verify();
}

// 测试用例3：所有BMU SOE相等
TEST_F(bmu_soc, SoeRack1_AllEqual) {
    // 设置测试数据
    for (int i = 0; i < BMU_NUM; i++) {
        test_bmu_soe[i].soe = 75.0f;
    }

    // 模拟所有BMU通信正常
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 执行测试
    float result = calculate_soe_rack1(test_bmu_soe);

    // 验证结果
    // soe_min = soe_max = 75.0f
    // result = 75.0 * 100.0 / (100.0 + 75.0 - 75.0) = 75.0f
    EXPECT_FLOAT_EQ(result, 75.0f);
    GlobalMockObject::verify();
}

// 测试用例4：所有BMU通信异常
TEST_F(bmu_soc, SoeRack1_AllFault) {
    // 设置测试数据
    for (int i = 0; i < BMU_NUM; i++) {
        test_bmu_soe[i].soe = 70.0f + i * 10.0f;
    }

    // 模拟所有BMU通信异常
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(FAULT));

    // 执行测试
    float result = calculate_soe_rack1(test_bmu_soe);

    // 验证结果
    // 当所有BMU都异常时，soe_min保持初始值100.0f，soe_max保持初始值0.0f
    // result = 100.0 * 100.0 / (100.0 + 100.0 - 0.0) = 50.0f
    EXPECT_FLOAT_EQ(result, 100.0f);
    GlobalMockObject::verify();
}

// 测试用例5：极端值测试
TEST_F(bmu_soc, SoeRack1_ExtremeValues) {
    // 设置测试数据
    test_bmu_soe[0].soe = 0.0f;    // 最小可能值
    test_bmu_soe[1].soe = 100.0f;  // 最大可能值
    test_bmu_soe[2].soe = 50.0f;   // 中间值
    test_bmu_soe[3].soe = 75.0f;   // 普通值

    // 模拟所有BMU通信正常
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 执行测试
    float result = calculate_soe_rack1(test_bmu_soe);

    // 验证结果
    // soe_min = 0.0f, soe_max = 100.0f
    // result = 0.0 * 100.0 / (100.0 + 0.0 - 100.0) = 0.0f
    EXPECT_FLOAT_EQ(result, 0.0f);
    GlobalMockObject::verify();
}

// 测试用例1：所有BMU通信正常
TEST_F(bmu_soc, SoeRack2_AllNormal) {
    // 设置测试数据
    float test_bmu_energy[BMU_NUM] = {100.0f, 200.0f, 150.0f, 250.0f};
    for (int i = 0; i < BMU_NUM; i++) {
        test_bmu_soe[i].soe = 60.0f + i * 10.0f;  // 60, 70, 80, 90
    }

    // 模拟所有BMU通信正常
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 执行测试
    float result = calculate_soe_rack2(test_bmu_soe, test_bmu_energy);

    // 验证结果
    // sum_soe_emax = (60*100 + 70*200 + 80*150 + 90*250) = 6000 + 14000 + 12000 + 22500 = 54500
    // sum_emax = 100 + 200 + 150 + 250 = 700
    // result = 54500 / 700 = 77.86f
    EXPECT_NEAR(result, 77.86f, 0.01f);
    GlobalMockObject::verify();
}

// 测试用例3：所有BMU通信异常
TEST_F(bmu_soc, SoeRack2_AllFault) {
    // 设置测试数据
    float test_bmu_energy[BMU_NUM] = {100.0f, 200.0f, 150.0f, 250.0f};
    for (int i = 0; i < BMU_NUM; i++) {
        test_bmu_soe[i].soe = 70.0f;
    }

    // 模拟所有BMU通信异常
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(FAULT));

    // 执行测试
    float result = calculate_soe_rack2(test_bmu_soe, test_bmu_energy);

    // 验证结果
    // 所有BMU通信异常时，sum_soe_emax和sum_emax都为0
    EXPECT_FLOAT_EQ(result, 70.0f);
    GlobalMockObject::verify();
}

// 测试用例4：总能量为0
TEST_F(bmu_soc, SoeRack2_ZeroEnergy) {
    // 设置测试数据
    float test_bmu_energy[BMU_NUM] = {0.0f, 0.0f, 0.0f, 0.0f};
    for (int i = 0; i < BMU_NUM; i++) {
        test_bmu_soe[i].soe = 70.0f;
    }

    // 模拟所有BMU通信正常
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 执行测试
    float result = calculate_soe_rack2(test_bmu_soe, test_bmu_energy);

    // 验证结果
    // 当总能量为0时，应返回0
    EXPECT_FLOAT_EQ(result, 0.0f);
    GlobalMockObject::verify();
}

// 测试用例5：极端值测试
TEST_F(bmu_soc, SoeRack2_ExtremeValues) {
    // 设置测试数据
    float test_bmu_energy[BMU_NUM] = {1000.0f, 0.0f, 2000.0f, 0.0f};
    test_bmu_soe[0].soe = 0.0f;    // 最小SOE
    test_bmu_soe[1].soe = 100.0f;  // 最大SOE
    test_bmu_soe[2].soe = 50.0f;   // 中间值
    test_bmu_soe[3].soe = 75.0f;   // 普通值

    // 模拟所有BMU通信正常
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(SUCCESS));

    // 执行测试
    float result = calculate_soe_rack2(test_bmu_soe, test_bmu_energy);

    // 验证结果
    // sum_soe_emax = (0*1000 + 50*2000) = 100000
    // sum_emax = 1000 + 2000 = 3000
    // result = 100000 / 3000 = 33.33f
    EXPECT_NEAR(result, 33.33f, 0.01f);
}

TEST_F(bmu_soc, LimitChargingSoe_Decrease) {
    // 测试充电时SOE下降的情况
    float new_soe = 75.0f;
    float last_soe = 80.0f;
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_charging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, last_soe);  // 应返回上一次的值
    EXPECT_TRUE(is_limiting);  // 限幅标志应为TRUE
}

TEST_F(bmu_soc, LimitChargingSoe_ExceedLimit) {
    // 测试SOE上升超过限制速率的情况
    float last_soe = 60.0f;
    float new_soe = last_soe + SOE_LIMIT_RATE + 5.0f;  // 确保超过限制速率
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_charging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, last_soe + SOE_LIMIT_RATE);  // 应返回限制后的值
    EXPECT_TRUE(is_limiting);  // 限幅标志应为TRUE
}

TEST_F(bmu_soc, LimitChargingSoe_WithinLimit) {
    // 测试SOE上升在限制范围内的情况
    float last_soe = 70.0f;
    float new_soe = last_soe + (SOE_LIMIT_RATE / 2.0f);  // 确保在限制范围内
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_charging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, new_soe);  // 应返回新的SOE值
    EXPECT_FALSE(is_limiting);  // 限幅标志应为FALSE
}

TEST_F(bmu_soc, LimitChargingSoe_NoChange) {
    // 测试SOE没有变化的情况
    float last_soe = 65.0f;
    float new_soe = 65.0f;
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_charging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, new_soe);  // 应返回新的SOE值
    EXPECT_FALSE(is_limiting);  // 限幅标志应为FALSE
}

TEST_F(bmu_soc, LimitChargingSoe_ExactLimit) {
    // 测试SOE上升恰好等于限制速率的情况
    float last_soe = 50.0f;
    float new_soe = last_soe + SOE_LIMIT_RATE;  // 恰好等于限制速率
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_charging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, new_soe);  // 应返回新的SOE值
    EXPECT_FALSE(is_limiting);  // 限幅标志应为FALSE
}


TEST_F(bmu_soc, LimitDischargingSoe_Increase) {
    // 测试放电时SOE上升的情况
    float new_soe = 85.0f;
    float last_soe = 80.0f;
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_discharging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, last_soe);  // 应返回上一次的值
    EXPECT_TRUE(is_limiting);  // 限幅标志应为TRUE
}

TEST_F(bmu_soc, LimitDischargingSoe_ExceedLimit) {
    // 测试SOE下降超过限制速率的情况
    float last_soe = 60.0f;
    float new_soe = last_soe - (SOE_LIMIT_RATE + 5.0f);  // 确保超过限制速率
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_discharging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, last_soe - SOE_LIMIT_RATE);  // 应返回限制后的值
    EXPECT_TRUE(is_limiting);  // 限幅标志应为TRUE
}

TEST_F(bmu_soc, LimitDischargingSoe_WithinLimit) {
    // 测试SOE下降在限制范围内的情况
    float last_soe = 70.0f;
    float new_soe = last_soe - (SOE_LIMIT_RATE / 2.0f);  // 确保在限制范围内
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_discharging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, new_soe);  // 应返回新的SOE值
    EXPECT_FALSE(is_limiting);  // 限幅标志应为FALSE
}

TEST_F(bmu_soc, LimitDischargingSoe_NoChange) {
    // 测试SOE没有变化的情况
    float last_soe = 65.0f;
    float new_soe = 65.0f;
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_discharging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, new_soe);  // 应返回新的SOE值
    EXPECT_FALSE(is_limiting);  // 限幅标志应为FALSE
}

TEST_F(bmu_soc, LimitDischargingSoe_ExactLimit) {
    // 测试SOE下降恰好等于限制速率的情况
    float last_soe = 50.0f;
    float new_soe = last_soe - SOE_LIMIT_RATE;  // 恰好等于限制速率
    int is_limiting = FALSE;
    
    // 执行测试
    float result = limit_discharging_soe(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, new_soe);  // 应返回新的SOE值
    EXPECT_FALSE(is_limiting);  // 限幅标志应为FALSE
}

TEST_F(bmu_soc, LimitSoeChange_GetDataFail) {
    // 测试获取电池状态失败的情况
    float new_soe = 75.0f;
    float last_soe = 70.0f;
    int is_limiting = FALSE;
    
    // Mock pdt_get_data失败
    MOCKER(pdt_get_data)
        .stubs()
        .will(returnValue(FAILURE));
    
    // 执行测试
    float result = limit_soe_change(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, new_soe);  // 应返回新的SOE值，不进行限幅
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, LimitSoeChange_ChargingState) {
    // 测试充电状态下的SOE限幅
    float new_soe = 80.0f;
    float last_soe = 70.0f;
    int is_limiting = FALSE;
    int battery_status = BCMU_BAT_STATUS_CHARGE;
    
    // Mock pdt_get_data返回充电状态
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &battery_status, sizeof(battery_status), type_int);
    
    // 执行测试
    float result = limit_soe_change(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, last_soe + SOE_LIMIT_RATE);  // 应返回限幅后的值
    EXPECT_TRUE(is_limiting);  // 限幅标志应为TRUE
}

TEST_F(bmu_soc, LimitSoeChange_DischargingState) {
    // 测试放电状态下的SOE限幅
    float new_soe = 60.0f;
    float last_soe = 70.0f;
    int is_limiting = FALSE;
    int battery_status = BCMU_BAT_STATUS_DISCHARGE;
    
    // Mock pdt_get_data返回放电状态
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &battery_status, sizeof(battery_status), type_int);
    
    // 执行测试
    float result = limit_soe_change(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, last_soe - SOE_LIMIT_RATE);  // 应返回限幅后的值
    EXPECT_TRUE(is_limiting);  // 限幅标志应为TRUE
}

TEST_F(bmu_soc, LimitSoeChange_OtherState) {
    // 测试非充电非放电状态
    float new_soe = 75.0f;
    float last_soe = 70.0f;
    int is_limiting = FALSE;
    int battery_status = BCMU_BAT_STATUS_STANDY;  // 其他状态
    
    // Mock pdt_get_data返回其他状态
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &battery_status, sizeof(battery_status), type_int);
    
    // 执行测试
    float result = limit_soe_change(new_soe, last_soe, &is_limiting);
    
    // 验证结果
    EXPECT_FLOAT_EQ(result, new_soe);  // 应返回原始SOE值，不进行限幅
}

/* Started by AICoder, pid:h6ed1u0999zaf84149e90b71a0c66e5c09b89212 */
// 测试update_soh函数
TEST_F(bmu_soc, update_soh_test) {
    int bcmu_soh = 50;
    int new_value = 75;
    int result = update_soh(bcmu_soh, new_value);
    EXPECT_EQ(result, 50);

    bcmu_soh = 50;
    new_value = 25;
    result = update_soh(bcmu_soh, new_value);
    EXPECT_EQ(result, 25);

    bcmu_soh = 75;
    new_value = 50;
    result = update_soh(bcmu_soh, new_value);
    EXPECT_EQ(result, 50);

    bcmu_soh = 50;
    new_value = 125;
    result = update_soh(bcmu_soh, new_value);
    EXPECT_EQ(result, 50);

    bcmu_soh = 50;
    new_value = -1;
    result = update_soh(bcmu_soh, new_value);
    EXPECT_EQ(result, 0);
}

TEST_F(bmu_soc, GetMinSoHTest) {
    int bmu_soh[BMU_NUM] = {50, 50, 50, 50};
    int result = get_min_soh(bmu_soh);
    EXPECT_EQ(result, 50);

    bmu_soh[1] = 60;
    result = get_min_soh(bmu_soh);
    EXPECT_EQ(result, 50);
}

TEST_F(bmu_soc, CalculateClusterSoHTest) {
    // 假设所有BMU的SOH值相同
    int result = 0;
    int status = 1;
    int bcmu_soh = 110;

    pdt_set_data(SID_BATTERY_MODULE_DIG_BMU_COMMUNICATION_STATUS, &status, sizeof(int), type_int);
    result = calculate_cluster_soh();
    EXPECT_EQ(FAILURE, result);

    status = 0;
    pdt_set_data(SID_BATTERY_MODULE_DIG_BMU_COMMUNICATION_STATUS, &status, sizeof(int), type_int);
    MOCKER(if_opt_open)
        .stubs()
        .will(returnValue(TRUE));
    result = calculate_cluster_soh();
    EXPECT_EQ(SUCCESS, result);

    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH, &bcmu_soh, sizeof(int), type_int);
    MOCKER(if_opt_open)
        .stubs()
        .will(returnValue(FALSE));
    result = calculate_cluster_soh();
    EXPECT_EQ(SUCCESS, result);
}
/* Ended by AICoder, pid:h6ed1u0999zaf84149e90b71a0c66e5c09b89212 */

TEST_F(bmu_soc, update_and_send_soh_data_test) {
    // 模拟send_soh_data_to_bmu函数
    MOCKER(send_soh_data_to_bmu)
        .stubs()
        .with(any())
        .will(returnValue(SUCCESS));
    // 调用被测函数
    int result = update_and_send_soh_data();
    // 验证结果
    EXPECT_EQ(SUCCESS, result);
    GlobalMockObject::verify();
}

// 测试电池簇循环次数计算 - 正常累计但未达到阈值
TEST_F(bmu_soc, CalculateClusterCycleTimesNormalAccumulation) {
    float cluster_current = 10.0f;  // 10A簇电流
    int cluster_cycle_times_param = 5;
    int bcmu_soh = 100;

    // 设置测试数据到数据字典
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT,
                 &cluster_current, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CYCLE_TIMES,
                 &cluster_cycle_times_param, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH, &bcmu_soh, sizeof(int), type_int);

    // 初始化测试数据
    s_bcmu_save_bak.bcmu_total_discharge_cap = 100.0f;  // 已累计100Ah
    s_bcmu_save_bak.bcmu_cycle_times = 5;

    // 执行测试
    int result = calculate_cluster_cycle_times();

    // 验证结果 - 未达到314Ah阈值，应该返回FALSE
    EXPECT_EQ(result, FALSE);
    EXPECT_GT(s_bcmu_save_bak.bcmu_total_discharge_cap, 100.0f);  // 容量应该增加
    EXPECT_EQ(s_bcmu_save_bak.bcmu_cycle_times, 5);  // 循环次数不变
}

// 测试电池簇循环次数计算 - 达到循环阈值
TEST_F(bmu_soc, CalculateClusterCycleTimesReachThreshold) {
    float cluster_current = 10.0f;  // 10A簇电流
    int cluster_cycle_times_param = 8;
    int bcmu_soh = 100;

    // 设置测试数据到数据字典
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT,
                 &cluster_current, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CYCLE_TIMES,
                 &cluster_cycle_times_param, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH, &bcmu_soh, sizeof(int), type_int);

    // 初始化测试数据 - 接近阈值
    s_bcmu_save_bak.bcmu_total_discharge_cap = 313.999f;  // 接近314Ah阈值
    s_bcmu_save_bak.bcmu_cycle_times = 8;

    // Mock write_bmu_soh_info_to_file
    MOCKER(write_bmu_soh_info_to_file)
        .stubs()
        .will(returnValue(TRUE));

    // 执行测试
    int result = calculate_cluster_cycle_times();

    // 验证结果 - 达到阈值，应该返回TRUE
    EXPECT_EQ(result, TRUE);
    EXPECT_FLOAT_EQ(s_bcmu_save_bak.bcmu_total_discharge_cap, 0.0f);  // 容量重置
    EXPECT_EQ(s_bcmu_save_bak.bcmu_cycle_times, 9);  // 循环次数+1

    GlobalMockObject::verify();
}

// 测试电池簇循环次数计算 - 电流低于检测误差
TEST_F(bmu_soc, CalculateClusterCycleTimesLowCurrent) {
    float cluster_current = 1.0f;  // 1A簇电流，低于2.15A检测误差
    int cluster_cycle_times_param = 3;
    int bcmu_soh = 100;

    // 设置测试数据到数据字典
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT,
                 &cluster_current, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CYCLE_TIMES,
                 &cluster_cycle_times_param, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH, &bcmu_soh, sizeof(int), type_int);

    // 初始化测试数据
    s_bcmu_save_bak.bcmu_total_discharge_cap = 200.0f;
    s_bcmu_save_bak.bcmu_cycle_times = 3;
    float original_cap = s_bcmu_save_bak.bcmu_total_discharge_cap;

    // 执行测试
    int result = calculate_cluster_cycle_times();

    // 验证结果 - 电流太小，容量不应该增加
    EXPECT_EQ(result, FALSE);
    EXPECT_FLOAT_EQ(s_bcmu_save_bak.bcmu_total_discharge_cap, original_cap);  // 容量不变
    EXPECT_EQ(s_bcmu_save_bak.bcmu_cycle_times, 3);  // 循环次数不变
}

// 测试电池簇循环次数计算 - 北向协议修改循环次数
TEST_F(bmu_soc, CalculateClusterCycleTimesParamModified) {
    float cluster_current = 5.0f;  // 5A簇电流
    int cluster_cycle_times_param = 15;  // 北向协议设置的新值
    int bcmu_soh = 100;

    // 设置测试数据到数据字典
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT,
                 &cluster_current, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_BATTERY_CLUSTER_CYCLE_TIMES,
                 &cluster_cycle_times_param, sizeof(int), type_int);
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_MANAGEMENT_UNIT_SOH, &bcmu_soh, sizeof(int), type_int);

    // 初始化测试数据
    s_bcmu_save_bak.bcmu_total_discharge_cap = 100.0f;
    s_bcmu_save_bak.bcmu_cycle_times = 10;  // 本地值与参数不同

    // 执行测试
    int result = calculate_cluster_cycle_times();

    // 验证结果 - 本地值应该更新为参数值
    EXPECT_EQ(result, FALSE);  // 未达到阈值
    EXPECT_EQ(s_bcmu_save_bak.bcmu_cycle_times, 15);  // 更新为北向协议设置的值
}
/* Ended by AICoder, pid:cluster_cycle_times_test_001 */

// 测试获取BCMU序列号成功的情况
TEST_F(bmu_soc, get_bcmu_serial_numbers_success_test) {
    serial_numbers_t bcmu_serials;

    // 设置mock行为
    g_mock_bcmu_serial_result = SUCCESS;
    for (int i = 0; i < BMU_NUM; i++) {
        g_mock_bmu_serial_results[i] = SUCCESS;
    }

    // 调用被测函数
    int result = get_bcmu_serial_numbers(&bcmu_serials);

    // 验证结果
    EXPECT_EQ(SUCCESS, result);
}

// 测试获取BCMU序列号失败的情况
TEST_F(bmu_soc, get_bcmu_serial_numbers_bcmu_failure_test) {
    serial_numbers_t bcmu_serials;

    // 设置mock行为
    g_mock_bcmu_serial_result = FAILURE;

    // 调用被测函数
    int result = get_bcmu_serial_numbers(&bcmu_serials);

    // 验证结果
    EXPECT_EQ(FAILURE, result);
    GlobalMockObject::verify();
}

// 测试获取BMU序列号失败的情况
TEST_F(bmu_soc, get_bcmu_serial_numbers_bmu_failure_test) {
    serial_numbers_t bcmu_serials;

    // 设置mock行为
    g_mock_bcmu_serial_result = SUCCESS;
    g_mock_bmu_serial_results[0] = SUCCESS;
    g_mock_bmu_serial_results[1] = FAILURE; // 第二个BMU失败

    // 调用被测函数
    int result = get_bcmu_serial_numbers(&bcmu_serials);

    // 验证结果
    EXPECT_EQ(FAILURE, result);
    GlobalMockObject::verify();
}

// 测试空指针参数的情况
TEST_F(bmu_soc, get_bcmu_serial_numbers_null_pointer_test) {
    // 调用被测函数，传入空指针
    int result = get_bcmu_serial_numbers(NULL);

    // 验证结果
    EXPECT_EQ(FAILURE, result);
}

// 定义一个全局变量来存储测试数据
static unsigned char g_test_buffer[32 * 5 + 2] = {0};
static unsigned short g_mock_crc = 0x1234;

TEST_F(bmu_soc, load_bmu_serial_numbers_success_test) {
    // 准备测试数据
    serial_numbers_t serials = {0};

    // 填充测试数据
    for (int i = 0; i < 32; i++) {
        g_test_buffer[i] = 'A' + i % 26; // BCMU序列号
    }

    for (int i = 0; i < BMU_NUM; i++) {
        for (int j = 0; j < 32; j++) {
            g_test_buffer[32 + i * 32 + j] = 'a' + (i + j) % 26; // BMU序列号
        }
    }

    // 模拟CRC计算和存储
    g_mock_crc = 0x1234;
    g_test_buffer[32 * 5] = g_mock_crc & 0xFF;
    g_test_buffer[32 * 5 + 1] = (g_mock_crc >> 8) & 0xFF;

    // 模拟依赖函数
    MOCKER(read_file)
        .stubs()
        .with(any(), any(), any())
        .will(returnValue(1)); // 返回1表示成功读取了1个数据项

    MOCKER(calculate_crc16)
        .stubs()
        .with(any(), any())
        .will(returnValue(g_mock_crc));

    MOCKER(get_int16_data)
        .stubs()
        .with(any())
        .will(returnValue(g_mock_crc));

    // 执行测试
    int result = load_bmu_serial_numbers(1, &serials);

    // 验证结果
    EXPECT_EQ(SUCCESS, result);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, load_bmu_serial_numbers_null_pointer_test) {
    // 测试空指针情况
    int result = load_bmu_serial_numbers(1, NULL);
    EXPECT_EQ(FAILURE, result);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, load_bmu_serial_numbers_file_not_exist_test) {
    serial_numbers_t serials = {0};
    // 测试文件不存在的情况
    MOCKER(is_file_exist)
        .stubs()
        .will(returnValue(FALSE));
    int result = load_bmu_serial_numbers(1, &serials);
    EXPECT_EQ(FAILURE, result);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, load_bmu_serial_numbers_read_file_failure_test) {
    serial_numbers_t serials = {0};

    // 模拟文件读取失败
    MOCKER(read_file)
        .stubs()
        .will(returnValue(10)); // 返回小于预期的大小

    int result = load_bmu_serial_numbers(1, &serials);
    EXPECT_EQ(FAILURE, result);
    GlobalMockObject::verify();
}

// 定义一个回调函数用于CRC失败测试
int read_file_callback_crc_failure(char *file_path, void *buffer, unsigned int size) {
    memcpy(buffer, g_test_buffer, 32 * 5 + 2);
    return 32 * 5 + 2;
}

TEST_F(bmu_soc, load_bmu_serial_numbers_crc_failure_test) {
    serial_numbers_t serials = {0};

    // 清空测试缓冲区
    memset(g_test_buffer, 0, sizeof(g_test_buffer));

    // 模拟文件读取成功
    MOCKER(read_file)
        .stubs()
        .will(invoke(read_file_callback_crc_failure));

    // 模拟CRC校验失败
    MOCKER(calculate_crc16)
        .stubs()
        .will(returnValue(0x1234));

    MOCKER(get_int16_data)
        .stubs()
        .will(returnValue(0x4321)); // 不同的CRC值

    int result = load_bmu_serial_numbers(1, &serials);
    EXPECT_EQ(FAILURE, result);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, find_majority_bmu_record_test)
{
    // 准备测试数据
    serial_numbers_t bmu_saved_serials[BMU_NUM] = {0};
    serial_numbers_t majority_record_out = {0};
    int majority_count_out = 0;
    int active_bmu_indices[BMU_NUM] = {0, 1, 2, 3};
    int num_active_bmus = 4;

    // 设置不同的序列号模式
    // BMU 0和1有相同的序列号
    strncpy((char *)bmu_saved_serials[0].bcmu_serial, "BCMU-SERIAL-001", 32);
    strncpy((char *)bmu_saved_serials[0].bmu_serials[0], "BMU-SERIAL-001", 32);
    strncpy((char *)bmu_saved_serials[0].bmu_serials[1], "BMU-SERIAL-002", 32);

    strncpy((char *)bmu_saved_serials[1].bcmu_serial, "BCMU-SERIAL-001", 32);
    strncpy((char *)bmu_saved_serials[1].bmu_serials[0], "BMU-SERIAL-001", 32);
    strncpy((char *)bmu_saved_serials[1].bmu_serials[1], "BMU-SERIAL-002", 32);

    // BMU 2有不同的序列号
    strncpy((char *)bmu_saved_serials[2].bcmu_serial, "BCMU-SERIAL-002", 32);
    strncpy((char *)bmu_saved_serials[2].bmu_serials[0], "BMU-SERIAL-003", 32);

    // BMU 3有不同的序列号
    strncpy((char *)bmu_saved_serials[3].bcmu_serial, "BCMU-SERIAL-003", 32);
    strncpy((char *)bmu_saved_serials[3].bmu_serials[0], "BMU-SERIAL-004", 32);

    // 测试找到多数派记录
    EXPECT_EQ(SUCCESS, find_majority_bmu_record(bmu_saved_serials, active_bmu_indices, num_active_bmus, &majority_record_out, &majority_count_out));
    EXPECT_EQ(2, majority_count_out);                                               // 应该找到2个相同记录
    EXPECT_STREQ("BCMU-SERIAL-001", (const char *)majority_record_out.bcmu_serial); // 多数派记录应该是BMU 0和1的记录

    // 测试所有记录都不同的情况
    active_bmu_indices[0] = 1;
    active_bmu_indices[1] = 2;
    active_bmu_indices[2] = 3;
    num_active_bmus = 3;

    // 确保所有活动BMU的记录都不同
    strncpy((char *)bmu_saved_serials[1].bcmu_serial, "BCMU-SERIAL-004", 32);

    // 根据函数实际行为，即使所有记录都不同，函数也会返回SUCCESS
    // 但会选择其中一个记录作为"多数派"，且majority_count_out应为1
    EXPECT_EQ(SUCCESS, find_majority_bmu_record(bmu_saved_serials, active_bmu_indices, num_active_bmus, &majority_record_out, &majority_count_out));
    EXPECT_EQ(1, majority_count_out); // 应该找到1个记录（任意一个）

    // 测试只有一个活动BMU的情况
    num_active_bmus = 1;
    active_bmu_indices[0] = 1; // 使用索引1的BMU
    EXPECT_EQ(SUCCESS, find_majority_bmu_record(bmu_saved_serials, active_bmu_indices, num_active_bmus, &majority_record_out, &majority_count_out));
    EXPECT_EQ(1, majority_count_out);                                               // 应该找到1个记录
    EXPECT_STREQ("BCMU-SERIAL-004", (const char *)majority_record_out.bcmu_serial); // 多数派记录应该是唯一活动BMU的记录
    GlobalMockObject::verify();
}

// 测试judge_serial_comparison_scenario函数
TEST_F(bmu_soc, judge_serial_comparison_scenario_test) {
    serial_numbers_t bmu_saved_serials[BMU_NUM] = {0};
    int active_bmu_indices[BMU_NUM] = {0, 1, 2, 3};
    int num_active_bmus = 4;
    serial_numbers_t bcmu_saved_serials = {0};
    serial_numbers_t majority_bmu_record = {0};
    int majority_bmu_count = 0;
    serial_compare_result_t result = {0};

    // 准备测试数据
    // 情况1：BCMU和所有BMU都未更换
    // 所有BMU记录相同，且与BCMU记录一致
    for (int i = 0; i < BMU_NUM; i++) {
        strncpy((char *)bmu_saved_serials[i].bcmu_serial, "BCMU123456", 10);
        for (int j = 0; j < BMU_NUM; j++) {
            strncpy((char *)bmu_saved_serials[i].bmu_serials[j], "BMU123456", 9);
        }
    }
    strncpy((char *)bcmu_saved_serials.bcmu_serial, "BCMU123456", 10);
    for (int j = 0; j < BMU_NUM; j++) {
        strncpy((char *)bcmu_saved_serials.bmu_serials[j], "BMU123456", 9);
    }

    // 多数派记录与所有BMU记录一致
    memcpy(&majority_bmu_record, &bmu_saved_serials[0], sizeof(serial_numbers_t));
    majority_bmu_count = num_active_bmus;

    // 测试情况1 - 所有记录一致
    EXPECT_EQ(SUCCESS, judge_serial_comparison_scenario(bmu_saved_serials, active_bmu_indices, num_active_bmus, 
                                                       &bcmu_saved_serials, &majority_bmu_record, majority_bmu_count, &result));
    // 根据实际行为，当多数派数量>=3且BCMU序列号与多数派一致时，bcmu_changed为0
    EXPECT_EQ(0, result.bcmu_changed);
    EXPECT_EQ(0, result.bmu_changed_count);
    EXPECT_EQ(0, result.bmu_changed_mask);

    // 情况2：BCMU更换了
    // 修改BCMU记录使其与BMU多数派记录不一致
    strncpy((char *)bcmu_saved_serials.bcmu_serial, "BCMU_NEW", 8);
    majority_bmu_count = 4; // 确保多数派数量>=3

    // 测试情况2
    memset(&result, 0, sizeof(result));
    EXPECT_EQ(SUCCESS, judge_serial_comparison_scenario(bmu_saved_serials, active_bmu_indices, num_active_bmus, 
                                                       &bcmu_saved_serials, &majority_bmu_record, majority_bmu_count, &result));
    // 根据实际行为，当多数派数量>=3且BCMU序列号与多数派不一致时，bcmu_changed为1
    EXPECT_EQ(1, result.bcmu_changed);
    EXPECT_EQ(0, result.bmu_changed_count);
    EXPECT_EQ(0, result.bmu_changed_mask);

    // 情况3：BCMU未更换，但某个BMU更换
    // 恢复BCMU记录，修改一个BMU的记录使其与多数派不一致
    strncpy((char *)bcmu_saved_serials.bcmu_serial, "BCMU123456", 10);
    
    // 修改BMU 1的记录
    memset(&bmu_saved_serials[1], 0, sizeof(serial_numbers_t));
    strncpy((char *)bmu_saved_serials[1].bcmu_serial, "BCMU_DIFF", 9);
    
    // 多数派记录仍与大多数BMU记录一致
    majority_bmu_count = 3; // 确保多数派数量>=3

    // 测试情况3
    memset(&result, 0, sizeof(result));
    EXPECT_EQ(SUCCESS, judge_serial_comparison_scenario(bmu_saved_serials, active_bmu_indices, num_active_bmus, 
                                                       &bcmu_saved_serials, &majority_bmu_record, majority_bmu_count, &result));
    // 根据实际行为，当多数派数量>=3且BCMU序列号与多数派一致，但有BMU与多数派不一致时
    EXPECT_EQ(0, result.bcmu_changed);
    EXPECT_EQ(1, result.bmu_changed_count);
    EXPECT_EQ(2, result.bmu_changed_mask); // 2 = 1 << 1，表示索引1的BMU被标记为更换
}


// 测试参数为NULL的情况
TEST_F(bmu_soc, compare_serial_numbers_null_params_test) {
    serial_numbers_t bmu_serials[BMU_NUM];
    serial_numbers_t bcmu_serial;
    serial_compare_result_t result;

    // 测试bmu_saved_serials为NULL
    EXPECT_EQ(FAILURE, compare_serial_numbers(NULL, BMU_NUM, &bcmu_serial, &result));

    // 测试bcmu_saved_serials为NULL
    EXPECT_EQ(FAILURE, compare_serial_numbers(bmu_serials, BMU_NUM, NULL, &result));

    // 测试result为NULL
    EXPECT_EQ(FAILURE, compare_serial_numbers(bmu_serials, BMU_NUM, &bcmu_serial, NULL));
    GlobalMockObject::verify();
}

// 测试找不到多数派BMU记录的情况
TEST_F(bmu_soc, compare_serial_numbers_no_majority_record_test) {
    serial_numbers_t bmu_serials[BMU_NUM];
    serial_numbers_t bcmu_serial;
    serial_compare_result_t result;

    // 模拟BMU在线且通信正常
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(NORMAL));

    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 模拟find_majority_bmu_record返回失败
    MOCKER(find_majority_bmu_record)
        .stubs()
        .will(returnValue(FAILURE));

    EXPECT_EQ(FAILURE, compare_serial_numbers(bmu_serials, BMU_NUM, &bcmu_serial, &result));
    // 根据当前实现，找不到多数派时，不设置BCMU更换标志
    EXPECT_EQ(0, result.bcmu_changed);
    GlobalMockObject::verify();
}

// 测试正常情况下的序列号比较
TEST_F(bmu_soc, compare_serial_numbers_normal_test) {
    serial_numbers_t bmu_serials[BMU_NUM];
    serial_numbers_t bcmu_serial;
    serial_compare_result_t result;

    // 模拟BMU在线且通信正常
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(NORMAL));

    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 模拟find_majority_bmu_record成功
    MOCKER(find_majority_bmu_record)
        .stubs()
        .will(returnValue(SUCCESS));

    // 模拟judge_serial_comparison_scenario成功
    MOCKER(judge_serial_comparison_scenario)
        .stubs()
        .will(returnValue(SUCCESS));

    EXPECT_EQ(SUCCESS, compare_serial_numbers(bmu_serials, BMU_NUM, &bcmu_serial, &result));
    GlobalMockObject::verify();
}

// 用于模拟compare_serial_numbers的回调函数 - BCMU更换场景
STATIC int MockCompareSerialNumbersBcmuChanged(serial_numbers_t bmu_saved_serials[], int bmu_count, serial_numbers_t *bcmu_saved_serials, serial_compare_result_t *result)
{
    result->bcmu_changed = 1;
    result->bmu_changed_count = 0;
    return SUCCESS;
}

// 用于模拟compare_serial_numbers的回调函数 - BMU更换场景
STATIC int MockCompareSerialNumbersBmuChanged(serial_numbers_t bmu_saved_serials[], int bmu_count, serial_numbers_t *bcmu_saved_serials, serial_compare_result_t *result)
{
    result->bcmu_changed = 0;
    result->bmu_changed_count = 1;
    result->bmu_changed_mask = 1; // 第一个BMU被更换
    return SUCCESS;
}

// 用于模拟load_bmu_serial_numbers的回调函数 - 空序列号
STATIC int MockLoadSerialNumbersEmpty(int dev_addr, serial_numbers_t *serials)
{
    memset(serials, 0, sizeof(serial_numbers_t));
    return SUCCESS;
}

// 用于模拟load_bmu_serial_numbers的回调函数 - 非空序列号
STATIC int MockLoadSerialNumbersNonEmpty(int dev_addr, serial_numbers_t *serials)
{
    memset(serials, 0, sizeof(serial_numbers_t));
    strncpy((char*)serials->bcmu_serial, "BCMU-TEST", 9);
    return SUCCESS;
}

TEST_F(bmu_soc, check_serial_number_changes_test) {
    // 测试场景1：正在备件替换中
    MOCKER(get_backup_status)
        .stubs()
        .will(returnValue(enum_backup_doing));
    EXPECT_EQ(SUCCESS, check_serial_number_changes());
    GlobalMockObject::verify();
    
    // 测试场景2：BMU通信异常
    MOCKER(get_backup_status)
        .stubs()
        .will(returnValue(enum_backup_none));
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(FAULT));
    EXPECT_EQ(FAILURE, check_serial_number_changes());
    GlobalMockObject::verify();
    
    // 测试场景3：系统首次运行，所有BMU序列号为默认空值
    MOCKER(get_backup_status)
        .stubs()
        .will(returnValue(enum_backup_none));
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(NORMAL));
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 使用预定义的回调函数模拟空序列号文件
    MOCKER(load_bmu_serial_numbers)
        .stubs()
        .with(any(), any())
        .will(invoke(MockLoadSerialNumbersEmpty));

    MOCKER(boardcast_serial_num_to_bmu)
        .stubs()
        .will(returnValue(SUCCESS));
    EXPECT_EQ(SUCCESS, check_serial_number_changes());
    GlobalMockObject::verify();

    // 测试场景4：BCMU更换
    MOCKER(get_backup_status)
        .stubs()
        .will(returnValue(enum_backup_none));
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(NORMAL));
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 修改这里：使用预定义的回调函数模拟非空序列号文件
    MOCKER(load_bmu_serial_numbers)
        .stubs()
        .with(any(), any())
        .will(invoke(MockLoadSerialNumbersNonEmpty));

    MOCKER(get_bcmu_serial_numbers)
        .stubs()
        .will(returnValue(SUCCESS));
    MOCKER(compare_serial_numbers)
        .stubs()
        .will(invoke(MockCompareSerialNumbersBcmuChanged));
    MOCKER(get_soh_data_from_bmu)
        .stubs()
        .will(returnValue(SUCCESS));
    MOCKER(record_bmu_index_pending_backup)
        .stubs()
        .will(returnValue(SUCCESS));
    MOCKER(boardcast_serial_num_to_bmu)
        .stubs()
        .will(returnValue(SUCCESS));
    EXPECT_EQ(SUCCESS, check_serial_number_changes());
    GlobalMockObject::verify();

    // 测试场景5：BMU更换
    MOCKER(get_backup_status)
        .stubs()
        .will(returnValue(enum_backup_none));
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(NORMAL));
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));

    // 修改这里：使用预定义的回调函数模拟非空序列号文件
    MOCKER(load_bmu_serial_numbers)
        .stubs()
        .with(any(), any())
        .will(invoke(MockLoadSerialNumbersNonEmpty));

    MOCKER(get_bcmu_serial_numbers)
        .stubs()
        .will(returnValue(SUCCESS));
    MOCKER(compare_serial_numbers)
        .stubs()
        .will(invoke(MockCompareSerialNumbersBmuChanged));
    MOCKER(get_soh_data_from_bmu)
        .stubs()
        .will(returnValue(SUCCESS));
    MOCKER(record_bmu_index_pending_backup)
        .stubs()
        .will(returnValue(SUCCESS));
    MOCKER(boardcast_serial_num_to_bmu)
        .stubs()
        .will(returnValue(SUCCESS));
    EXPECT_EQ(SUCCESS, check_serial_number_changes());
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, handle_serial_comparison_result_test) {
    // 场景1：BCMU和BMU都没有变更
    serial_compare_result_t result1 = {0};
    result1.bcmu_changed = 0;
    result1.bmu_changed_count = 0;
    EXPECT_EQ(FAILURE, handle_serial_comparison_result(&result1));

    // 场景2：BCMU更换了
    serial_compare_result_t result2 = {0};
    result2.bcmu_changed = 1;
    result2.bmu_changed_count = 0;
    EXPECT_EQ(SUCCESS, handle_serial_comparison_result(&result2));
    EXPECT_EQ(enum_backup_doing, get_backup_status());

    // 场景3：BCMU未更换，但部分BMU更换
    serial_compare_result_t result3 = {0};
    result3.bcmu_changed = 0;
    result3.bmu_changed_count = 2;
    result3.bmu_changed_mask = 0x5; // 第0和第2个BMU被更换
    EXPECT_EQ(SUCCESS, handle_serial_comparison_result(&result3));
    EXPECT_EQ(enum_backup_doing, get_backup_status());

    // 检查待同步BMU索引
    int bmu_indices[BMU_NUM] = {0};
    get_bmu_index_pending_backup(bmu_indices);
    // 验证索引是否正确记录
    for (int i = 0; i < BMU_NUM; i++) {
        if (result3.bmu_changed_mask & (1 << i)) {
            EXPECT_EQ(i, bmu_indices[i]);
        }
    }
}

// 模拟获取BMU索引的桩函数
int get_bmu_index_pending_backup_stub(int* bmu_index) {
    bmu_index[0] = 0; // 设置第一个BMU需要更新
    bmu_index[1] = -1;
    return SUCCESS;
}

// 模拟pdt_get_data的桩函数
int pdt_get_data_stub(sid sid_temp, void* data, size_t size) {
    static float test_float = 10.5f;
    static time_base_t test_time = {0};

    if (size == sizeof(float)) {
        *(float*)data = test_float;
    } else if (size == sizeof(time_base_t)) {
        *(time_base_t*)data = test_time;
    }
    return SUCCESS;
}

TEST_F(bmu_soc, update_bmu_soh_calc_base_value_test) {
    // 准备测试数据
    bmu_sox_deal_t bmu_sox_deal[BMU_NUM] = {0};
    float test_float = 10.5f;
    time_base_t test_time = {0};

    // 模拟获取需要更新SOH基准数据的BMU索引
    MOCKER(get_bmu_index_pending_backup)
        .stubs()
        .will(invoke(get_bmu_index_pending_backup_stub));

    // 模拟BMU SOH数据已同步标志
    bmu_soh_data_syned[0] = TRUE;

    // 模拟pdt_get_data成功返回
    MOCKER(pdt_get_data)
        .stubs()
        .will(invoke(pdt_get_data_stub));

    // 调用被测函数
    EXPECT_EQ(SUCCESS, update_bmu_soh_calc_base_value(bmu_sox_deal));

    // 验证结果
    EXPECT_FLOAT_EQ(test_float, bmu_sox_deal[0].calender_life_decline);
    EXPECT_FLOAT_EQ(test_float, bmu_sox_deal[0].cycle_life_decline);
    EXPECT_FLOAT_EQ(test_float, bmu_sox_deal[0].added_life_decline);
    EXPECT_FLOAT_EQ(test_float, bmu_sox_deal[0].total_discharge_cap);
    EXPECT_EQ(0, memcmp(&test_time, &bmu_sox_deal[0].last_calc_calender_time, sizeof(time_base_t)));
    EXPECT_TRUE(bmu_sox_deal[0].save_to_file);

    // 验证全局变量状态
    EXPECT_EQ(enum_backup_done, s_replace_finish_flag);
    EXPECT_FALSE(s_soh_sync_finish_flag);
    EXPECT_FALSE(bmu_soh_data_syned[0]);

    // 验证s_bmu_index_pending_backup被重置
    extern int s_bmu_index_pending_backup[BMU_NUM];
    EXPECT_EQ(-1, s_bmu_index_pending_backup[0]);

    GlobalMockObject::verify();
}

TEST_F(bmu_soc, JudgeIdleStatus) {
    MOCKER(get_system_time).stubs().will(returnValue(FAILURE));
    EXPECT_EQ(judge_idle_status(), FALSE);
    GlobalMockObject::verify();

    MOCKER(get_system_time).stubs().will(returnValue(SUCCESS));
    MOCKER(pdt_get_data)
        .stubs()
        .with(eq((long long)SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT), any(), any())
        .will(returnValue(FAILURE));
    EXPECT_EQ(judge_idle_status(), FALSE);
    GlobalMockObject::verify();

}

TEST_F(bmu_soc, JudgeIdleStatus_CurrentBelowThreshold_NotEnoughTime)
{
    // 模拟时间和电流
    current_time = 1000;
    float fake_current = 1.0f;
    int idle_time_threshold = 2; // 2小时

    MOCKER(pdt_get_data)
        .stubs()
        .with(eq((long long)SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_CURRENT), outBoundP((void *)&fake_current, sizeof(fake_current)), any())
        .will(returnValue(SUCCESS));
    MOCKER(pdt_get_data)
        .stubs()
        .with(eq((long long)SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_IDLE_TIME_THRESHOLD), outBoundP((void *)&idle_time_threshold, sizeof(idle_time_threshold)), any())
        .will(returnValue(SUCCESS));

    s_idle_start_time = 0; // 首次进入
    EXPECT_EQ(judge_idle_status(), FALSE);
    // 再次调用，模拟时间未到
    current_time = 3599;
    EXPECT_EQ(judge_idle_status(), FALSE);
    // 时间到
    current_time = 8201;
    EXPECT_EQ(judge_idle_status(), TRUE);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, CalculateCellVoltageExtremes_AllEqual) {
    for (int i = 0; i < BMU_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            s_bmu_sox_in[i].bmu_sox_data.cell_volt[j].f_value = 3.0f;
        }
        for (int k = 0; k < BMU_CELL_TEMP_NUM; ++k) {
            s_bmu_sox_in[i].bmu_sox_data.cell_temperature[k].f_value = 25.0f + k;
        }
    }
    global_max_v = -FLT_MAX;
    global_min_v = FLT_MAX;
    calculate_cell_voltage_extremes();
    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_FLOAT_EQ(bmu_cell_voltages_max[i], 3.0f);
        EXPECT_FLOAT_EQ(bmu_cell_voltages_min[i], 3.0f);
    }
    EXPECT_FLOAT_EQ(global_max_v, 3.0f);
    EXPECT_FLOAT_EQ(global_min_v, 3.0f);
}

TEST_F(bmu_soc, CalculateCellVoltageExtremes_DifferentValues) {
    // BMU0: min=2.5, max=3.5; BMU1: min=2.0, max=4.0
    s_bmu_sox_in[0].bmu_sox_data.cell_volt[0].f_value = 2.5f;
    s_bmu_sox_in[0].bmu_sox_data.cell_volt[1].f_value = 3.5f;
    s_bmu_sox_in[0].bmu_sox_data.cell_temperature[0].f_value = 20.0f;
    s_bmu_sox_in[0].bmu_sox_data.cell_temperature[1].f_value = 30.0f;
    s_bmu_sox_in[1].bmu_sox_data.cell_volt[0].f_value = 2.0f;
    s_bmu_sox_in[1].bmu_sox_data.cell_volt[1].f_value = 4.0f;
    s_bmu_sox_in[1].bmu_sox_data.cell_temperature[0].f_value = 21.0f;
    s_bmu_sox_in[1].bmu_sox_data.cell_temperature[1].f_value = 31.0f;
    for (int i = 2; i < BMU_NUM; ++i) {
        for (int j = 0; j < BMU_CELL_NUM; ++j) {
            s_bmu_sox_in[i].bmu_sox_data.cell_volt[j].f_value = 3.0f;
        }
        for (int k = 0; k < BMU_CELL_TEMP_NUM; ++k) {
            s_bmu_sox_in[i].bmu_sox_data.cell_temperature[k].f_value = 25.0f + k;
        }
    }
    global_max_v = -FLT_MAX;
    global_min_v = FLT_MAX;
    calculate_cell_voltage_extremes();
    EXPECT_FLOAT_EQ(bmu_cell_voltages_min[0], 2.5f);
    EXPECT_FLOAT_EQ(bmu_cell_voltages_max[0], 3.5f);
    EXPECT_FLOAT_EQ(bmu_cell_voltages_min[1], 2.0f);
    EXPECT_FLOAT_EQ(bmu_cell_voltages_max[1], 4.0f);
    EXPECT_FLOAT_EQ(global_min_v, 2.0f);
    EXPECT_FLOAT_EQ(global_max_v, 4.0f);
    EXPECT_FLOAT_EQ(global_temp_at_min, 21.0f);
    EXPECT_FLOAT_EQ(global_temp_at_max, 31.0f);
}

TEST_F(bmu_soc, LookupVoltage_BasicTablePoints) {
    // 精确命中表格点
    EXPECT_FLOAT_EQ(lookup_voltage(-30.0f, 0.0f), 1.8253f);
    EXPECT_FLOAT_EQ(lookup_voltage(50.0f, 100.0f), 3.3841f);
    EXPECT_FLOAT_EQ(lookup_voltage(25.0f, 10.0f), 3.1218f);

    // SOC低于最小/高于最大，应该clamp
    EXPECT_FLOAT_EQ(lookup_voltage(25.0f, -10.0f), lookup_voltage(25.0f, 0.0f));
    EXPECT_FLOAT_EQ(lookup_voltage(25.0f, 200.0f), lookup_voltage(25.0f, 100.0f));

    // 温度低于最小/高于最大，应该clamp
    EXPECT_FLOAT_EQ(lookup_voltage(-100.0f, 10.0f), lookup_voltage(-30.0f, 10.0f));
    EXPECT_FLOAT_EQ(lookup_voltage(100.0f, 10.0f), lookup_voltage(50.0f, 10.0f));

    // 插值测试（温度和SOC都在区间内）
    float v1 = lookup_voltage(-30.0f, 4.0f); // 在(-30,0)-(5,0)之间插值
    float v2 = lookup_voltage(-20.0f, 7.5f); // SOC=7.5在5和10之间
    EXPECT_GT(v1, 1.8253f);
    EXPECT_LT(v1, 1.8569f);
    EXPECT_GT(v2, 2.1140f);
    EXPECT_LT(v2, 2.2019f);

    // 精确在边界
    EXPECT_FLOAT_EQ(lookup_voltage(-30.0f, 100.0f), 3.3744f);
    EXPECT_FLOAT_EQ(lookup_voltage(50.0f, 0.0f), 2.7265f);
    
}

// 单元测试：lookup_soc
TEST_F(bmu_soc, LookupSoc_BasicTablePoints) {
    // 精确命中表格点
    EXPECT_NEAR(lookup_soc(-30.0f, 1.8253f), 0.0f, 1e-4);
    EXPECT_NEAR(lookup_soc(50.0f, 3.3841f), 100.0f, 1e-4);
    EXPECT_NEAR(lookup_soc(25.0f, 3.1218f), 10.0f, 1e-4);

    // 温度低于最小/高于最大，应该clamp
    EXPECT_NEAR(lookup_soc(-100.0f, 2.0187f), lookup_soc(-30.0f, 2.0187f), 1e-4);
    EXPECT_NEAR(lookup_soc(100.0f, 3.1607f), lookup_soc(50.0f, 3.1607f), 1e-4);

    // 精确在边界
    EXPECT_NEAR(lookup_soc(-30.0f, 3.3744f), 100.0f, 1e-4);
    EXPECT_NEAR(lookup_soc(50.0f, 2.7265f), 0.0f, 1e-4);

    // 电压低于最小/高于最大，应该clamp到SOC区间边界
    EXPECT_NEAR(lookup_soc(25.0f, 0.0f), 0.0f, 1e-4);
    EXPECT_NEAR(lookup_soc(25.0f, 10.0f), 100.0f, 1e-4);

    // 插值测试（电压在区间内）
    float soc1 = lookup_soc(-30.0f, 1.9f); // 在5%和10%之间
    float soc2 = lookup_soc(25.0f, 3.3f);  // 在95%和95%之间
    EXPECT_GT(soc1, 5.0f);
    EXPECT_LT(soc1, 10.0f);
    // EXPECT_GT(soc2, 95.0f);
    // EXPECT_LT(soc2, 100.0f);

    // 插值测试（电压在区间内）
    float soc = lookup_soc(30.0f, 2.95f); // 在5%和10%之间
    EXPECT_GT(soc, 0.0f);
    EXPECT_LT(soc, 5.0f);
}

TEST_F(bmu_soc, SocStaticCalibration_IdleStatusFail) {
    // judge_idle_status 返回 FALSE
    soc_adjust_record_t adjust_ret;
    MOCKER(judge_idle_status).stubs().will(returnValue(FALSE));
    EXPECT_EQ(soc_static_calibration(&adjust_ret), FAILURE);
    GlobalMockObject::verify();

    // judge_idle_status 返回 TRUE
    MOCKER(judge_idle_status).stubs().will(returnValue(TRUE));
    // is_cell_voltage_out_of_platform_region 返回 FALSE
    MOCKER(is_cell_voltage_out_of_platform_region).stubs().will(returnValue(FALSE));
    EXPECT_EQ(soc_static_calibration(&adjust_ret), FAILURE);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, SocStaticCalibration_ClusterLevelCalibration_AllNeedCalibrate) {
    soc_adjust_record_t adjust_ret;
    // judge_idle_status 返回 TRUE
    MOCKER(judge_idle_status).stubs().will(returnValue(TRUE));
    // is_cell_voltage_out_of_platform_region 返回 TRUE
    MOCKER(is_cell_voltage_out_of_platform_region).stubs().will(returnValue(TRUE));
    // 校正阈值
    int soc_threshold = 1;
    MOCKER(pdt_get_data)
        .stubs()
        .with(eq((long long)SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_MINIMUM_ALLOWABLE_CORRECTION_DELTA_THRESHOLD), outBoundP((void *)&soc_threshold, sizeof(soc_threshold)), any())
        .will(returnValue(SUCCESS));
    // 优化器未配置
    MOCKER(if_config_opt).stubs().will(returnValue(FALSE));
    // 设置所有BMU soc 差值大于阈值
    for (int i = 0; i < BMU_NUM; ++i) {
        s_bmu_cal_soc[i].soc = 4.0f + i;
        s_bmu_real_capacity[i] = 100.0f;
    }
    // mock lookup_soc
    MOCKER(lookup_soc).stubs().will(returnValue(2.0f));
    // mock pdt_set_data
    MOCKER(pdt_set_data).stubs().will(returnValue(SUCCESS));
    EXPECT_EQ(soc_static_calibration(&adjust_ret), SUCCESS);
    // 校准后所有BMU soc 应一致
    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_FLOAT_EQ(s_bmu_cal_soc[i].soc, 2.0f * 100.0f / (100.0f + 2.0f - 2.0f));
        EXPECT_FLOAT_EQ(s_bmu_cal_soc[i].cumulative_ah, s_bmu_cal_soc[i].soc / 100.0f * 100.0f);
    }
    GlobalMockObject::verify();
}

/* Started by AICoder, pid:z95c7i25bd0f19f14df008c170ddc38fe7d61322 */
TEST_F(bmu_soc, SocStaticCalibration_ModuleLevelCalibration) {
    soc_adjust_record_t adjust_ret;
    // Mock judge_idle_status to return TRUE
    MOCKER(judge_idle_status).stubs().will(returnValue(TRUE));
    // Mock is_cell_voltage_out_of_platform_region to return TRUE
    MOCKER(is_cell_voltage_out_of_platform_region).stubs().will(returnValue(TRUE));
    // Set the calibration threshold
    int soc_threshold = 1;
    MOCKER(pdt_get_data)
        .stubs()
        .with(eq((long long)SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_PARA_MINIMUM_ALLOWABLE_CORRECTION_DELTA_THRESHOLD), outBoundP((void *)&soc_threshold), any())
        .will(returnValue(SUCCESS));
    // Mock if_config_opt to return TRUE
    MOCKER(if_config_opt).stubs().will(returnValue(TRUE));
    // Set min/max voltages and temperatures for each BMU
    for (int i = 0; i < BMU_NUM; ++i) {
        bmu_cell_voltages_min[i] = 3.0f + i;
        bmu_cell_temps_at_min[i] = 25.0f + i;
        bmu_cell_voltages_max[i] = 3.5f + i;
        bmu_cell_temps_at_max[i] = 30.0f + i;
        s_bmu_cal_soc[i].soc = 5.0f;
        s_bmu_real_capacity[i] = 100.0f;
    }
    // Mock lookup_soc to return 2.0f
    MOCKER(lookup_soc).stubs().will(returnValue(2.0f));
    // Mock pdt_set_data to return SUCCESS
    MOCKER(pdt_set_data).stubs().will(returnValue(SUCCESS));
    // Expect soc_static_calibration to succeed
    EXPECT_EQ(soc_static_calibration(&adjust_ret), SUCCESS);
    // After calibration, all BMU SOCs should be the new value
    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_FLOAT_EQ(s_bmu_cal_soc[i].soc, 2.0f * 100.0f / (100.0f + 2.0f - 2.0f));
        EXPECT_FLOAT_EQ(s_bmu_cal_soc[i].cumulative_ah, s_bmu_cal_soc[i].soc / 100.0f * 100.0f);
    }
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, IsCellVoltageOutOfPlatformRegion_LowRegion) {
    // Set global variables
    global_min_v = 3.0f;
    global_max_v = 3.2f;
    global_temp_at_min = 25.0f;
    global_temp_at_max = 25.0f;

    // Mock lookup_voltage return values
    MOCKER(lookup_voltage)
        .stubs()
        .with(eq((float)25), eq((float)10))  // Upper boundary of low region
        .will(returnValue((float)3.3));
    MOCKER(lookup_voltage)
        .stubs()
        .with(eq((float)25), eq((float)0))   // Lower boundary of low region
        .will(returnValue((float)2.8));
    MOCKER(lookup_voltage)
        .stubs()
        .with(eq((float)25), eq((float)95))  // Lower boundary of high region
        .will(returnValue((float)3.6));
    MOCKER(lookup_voltage)
        .stubs()
        .with(eq((float)25), eq((float)100)) // Upper boundary of high region
        .will(returnValue((float)4));

    // Test low region voltage
    EXPECT_EQ(is_cell_voltage_out_of_platform_region(), TRUE);

    global_min_v = 3.7f;
    global_max_v = 3.8f;
    EXPECT_EQ(is_cell_voltage_out_of_platform_region(), TRUE);

    global_min_v = 3.0f;
    global_max_v = 3.8f;
    EXPECT_EQ(is_cell_voltage_out_of_platform_region(), FALSE);

    global_min_v = 3.4f;
    global_max_v = 3.5f;
    EXPECT_EQ(is_cell_voltage_out_of_platform_region(), FALSE);

    global_min_v = 3.4f;
    global_max_v = 3.8f;
    EXPECT_EQ(is_cell_voltage_out_of_platform_region(), FALSE);

    global_min_v = 3.2f;
    global_max_v = 3.5f;
    EXPECT_EQ(is_cell_voltage_out_of_platform_region(), FALSE);
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:z95c7i25bd0f19f14df008c170ddc38fe7d61322 */

// 测试空指针情况
TEST_F(bmu_soc, update_bmu_soh_calc_base_value_null_test) {
    EXPECT_EQ(FAILURE, update_bmu_soh_calc_base_value(NULL));
}

TEST_F(bmu_soc, sync_soh_data_to_bmu_test) {
    // 准备测试数据
    int dev_sn = 1;
    bmu_save_t saved_data = {0};
    bmu_sox_deal_t current_data = {0};
    
    // 保存原始值
    int original_flag = s_replace_finish_flag;
    
    // 测试备件替换进行中的情况
    s_replace_finish_flag = enum_backup_doing;
    EXPECT_EQ(FAILURE, sync_soh_data_to_bmu(dev_sn, saved_data, current_data));
    
    // 测试BMU不存在的情况
    s_replace_finish_flag = enum_backup_none;
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(ABNORMAL));
    EXPECT_EQ(FAILURE, sync_soh_data_to_bmu(dev_sn, saved_data, current_data));
    GlobalMockObject::verify();
    
    // 测试BMU通信异常的情况
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(NORMAL));
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(ABNORMAL));
    EXPECT_EQ(FAILURE, sync_soh_data_to_bmu(dev_sn, saved_data, current_data));
    GlobalMockObject::verify();
    
    // 测试数据无变化的情况
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(NORMAL));
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));
    
    // 确保所有浮点比较都返回false（数据无变化）
    MOCKER(fabs)
        .stubs()
        .will(returnValue(0.0f));
    
    EXPECT_EQ(SUCCESS, sync_soh_data_to_bmu(dev_sn, saved_data, current_data));
    GlobalMockObject::verify();
    
    // 测试数据有变化的情况
    current_data.added_life_decline = 1.0f;
    
    // 模拟fabs返回大于FLOAT_EPSILON的值，表示数据有变化
    MOCKER(fabs)
        .stubs()
        .will(returnValue(0.1f));
    
    MOCKER(pdt_set_data)
        .stubs()
        .will(returnValue(SUCCESS));
    
    MOCKER(send_soh_data_to_bmu)
        .stubs()
        .will(returnValue(SUCCESS));
    
    EXPECT_EQ(SUCCESS, sync_soh_data_to_bmu(dev_sn, saved_data, current_data));
    GlobalMockObject::verify();
    
    // 测试备件替换完成的情况
    s_replace_finish_flag = enum_backup_done;
    
    MOCKER(is_bmu_exist)
        .stubs()
        .will(returnValue(NORMAL));
    
    MOCKER(is_bmu_comm_normal)
        .stubs()
        .will(returnValue(NORMAL));
    
    EXPECT_EQ(SUCCESS, sync_soh_data_to_bmu(dev_sn, saved_data, current_data));
    
    // 恢复原始值
    s_replace_finish_flag = original_flag;
    
    GlobalMockObject::verify();
}


/* Started by AICoder, pid:n019bc7d57b41ac149ea0a37908d8f58e9a47ff9 */
TEST_F(bmu_soc, SetModuleVoltageBelowThreshold) {
    float voltage = 19.5f;
    float mod_undervoltage = 20.0f;
    pdt_set_data(SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_VOLTAGE, &voltage, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_MODULE_GROUP_PARA_BMUS_VOLTAGE_LOW_END_THRESHOLD, &mod_undervoltage, sizeof(float), type_float);
    EXPECT_EQ(is_bmu_discharge_full(1), TRUE);
}

TEST_F(bmu_soc, SetAnyCellVoltageBelowThreshold) {
    float voltage = 21.0f;
    float mod_undervoltage = 20.0f;
    float cell_undervoltage = 2.5f;
    float cell_voltage = 2.4f;
    pdt_set_data(SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_VOLTAGE, &voltage, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_MODULE_GROUP_PARA_BMUS_VOLTAGE_LOW_END_THRESHOLD, &mod_undervoltage, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_MODULE_GROUP_PARA_BMUS_CELL_UNDERVOLTAGE_END_THRESHOLD, &cell_undervoltage, sizeof(float), type_float);
    float v = cell_voltage;
    pdt_set_data(SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE, &v, sizeof(float), type_float);

    EXPECT_EQ(is_bmu_discharge_full(1), TRUE);
}

TEST_F(bmu_soc, SetAllVoltagesNormal) {
    float voltage = 21.0f;
    float mod_undervoltage = 20.0f;
    float cell_undervoltage = 2.5f;
    pdt_set_data(SID_BATTERY_MODULE_ANA_BMU_BATTERY_MODULE_VOLTAGE, &voltage, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_MODULE_GROUP_PARA_BMUS_VOLTAGE_LOW_END_THRESHOLD, &mod_undervoltage, sizeof(float), type_float);
    pdt_set_data(SID_BATTERY_MODULE_GROUP_PARA_BMUS_CELL_UNDERVOLTAGE_END_THRESHOLD, &cell_undervoltage, sizeof(float), type_float);
    float v = 3.0f;
    pdt_set_data(SID_BATTERY_MODULE_ANA_BMU_CELL_VOLTAGE, &v, sizeof(float), type_float);

    EXPECT_EQ(is_bmu_discharge_full(1), FALSE);
}

TEST_F(bmu_soc, TestGetDataFetchFailure) {
    // Simulate pdt_get_data failure
     MOCKER(pdt_get_data)
     .stubs()
     .with(any(), any(), any())
     .will(returnValue(FAILURE));
    EXPECT_EQ(FALSE, is_bmu_discharge_full(1));

    // 恢复原始函数
    GlobalMockObject::verify();
}
/* Ended by AICoder, pid:n019bc7d57b41ac149ea0a37908d8f58e9a47ff9 */

/* Started by AICoder, pid:92591ad342lb9dc14a68086530051c26e678bb75 */
TEST_F(bmu_soc, NoBMUFull) {
    MOCKER(is_bmu_discharge_full)
        .stubs()
        .with(any())
        .will(returnValue(FALSE));
    EXPECT_EQ(judge_bmu_discharge_full_status(), FALSE);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, AllBMUFull) {
    MOCKER(is_bmu_discharge_full)
        .stubs()
        .with(any())
        .will(returnValue(TRUE));
    EXPECT_EQ(judge_bmu_discharge_full_status(), TRUE);
    GlobalMockObject::verify();
}

/* Ended by AICoder, pid:92591ad342lb9dc14a68086530051c26e678bb75 */

TEST_F(bmu_soc, check_and_save_charge_end_info_test)
{
    int bcmu_battery_status = BCMU_BAT_STATUS_CHARGE;
    int cluster_soc = 85;
    time_t current_time = 1234567890;

    // Mock pdt_get_data返回充电状态
    pdt_set_data((sid)SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, (void *)&bcmu_battery_status, sizeof(bcmu_battery_status), type_int);

    // 调用函数，此时内部状态应该记录为充电状态
    EXPECT_EQ(SUCCESS, check_and_save_charge_end_info());

    // 模拟第二次调用时电池状态为待机（非充电）
    bcmu_battery_status = BCMU_BAT_STATUS_STANDY;
    // Mock pdt_get_data返回充电状态
    pdt_set_data((sid)SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, (void *)&bcmu_battery_status, sizeof(bcmu_battery_status), type_int);

    // Mock pdt_get_data返回簇电流
    pdt_set_data((sid)SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_BATTERY_CLUSTER_TOTAL_CLUSTER_SOC, &cluster_soc, sizeof(cluster_soc), type_int);

    // 模拟保存充电结束信息成功
    MOCKER(save_charge_end_info)
        .stubs()
        .will(returnValue(SUCCESS));

    // 调用函数，此时应该检测到充电状态变化并保存信息
    EXPECT_EQ(SUCCESS, check_and_save_charge_end_info());

    GlobalMockObject::verify();
}

TEST_F(bmu_soc, save_charge_end_info_test)
{
    last_charge_info_t last_charge_info;
    // 准备测试数据
    time_t test_time = 1234567890;
    int test_soc = 85;

    // 模拟write_file函数行为
    MOCKER(write_file)
        .stubs()
        .with(any(), any(), eq((unsigned int)sizeof(last_charge_info_t)))
        .will(returnValue(SUCCESS));

    // 调用被测函数
    int result = save_charge_end_info(&last_charge_info);

    // 验证结果
    EXPECT_EQ(SUCCESS, result);

    // 验证write_file被正确调用
    GlobalMockObject::verify();

    // 测试写入文件失败的情况
    MOCKER(write_file)
        .stubs()
        .will(returnValue(FAILURE));

    result = save_charge_end_info(&last_charge_info);
    EXPECT_EQ(FAILURE, result);
    GlobalMockObject::verify();

    //模拟获取锁
    MOCKER(lock_file_write)
        .stubs()
        .will(returnValue(FAILURE));
    result = save_charge_end_info(&last_charge_info);
    EXPECT_EQ(FAILURE, result);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, update_sys_run_cnt_after_last_chg_test) {
    // 模拟文件不存在的情况
    MOCKER(is_file_exist)
        .stubs()
        .will(returnValue(FALSE));
    EXPECT_EQ(update_sys_run_cnt_after_last_chg(), FAILURE);
    GlobalMockObject::verify();

    // 模拟文件读取失败的情况
    MOCKER(read_file)
        .stubs()
        .will(returnValue(FAILURE));
    EXPECT_EQ(update_sys_run_cnt_after_last_chg(), FAILURE);
    GlobalMockObject::verify();

    // 模拟最后一次充电信息已被使用的情况
    last_charge_info_t info;
    info.last_charge_info_used = TRUE;
    MOCKER(read_file)
        .stubs()
        .with(any(),outBoundP((void *)&info, sizeof(info)), any())
        .will(returnValue(SUCCESS));
    EXPECT_EQ(update_sys_run_cnt_after_last_chg(), FAILURE);
    GlobalMockObject::verify();

    // 模拟上次充电结束后的运行次数为0的情况
    info.last_charge_info_used = FALSE;
    info.sys_run_cnt_after_last_charge_end = 0;
    MOCKER(read_file)
        .stubs()
        .with(any(),outBoundP((void *)&info, sizeof(info)), any())
        .will(returnValue(SUCCESS));
    MOCKER(save_charge_end_info)
        .stubs()
        .will(returnValue(SUCCESS));
    EXPECT_EQ(update_sys_run_cnt_after_last_chg(), SUCCESS);
    GlobalMockObject::verify();

    // 模拟计数递增但未达到保存条件的情况
    info.sys_run_cnt_after_last_charge_end = 1;
    MOCKER(read_file)
        .stubs()
        .with(any(),outBoundP((void *)&info, sizeof(info)), any())
        .will(returnValue(SUCCESS));
    EXPECT_EQ(update_sys_run_cnt_after_last_chg(), FAILURE);
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, update_sys_run_cnt_after_last_chg_period_save_test) {
    int i = 1;
    last_charge_info_t info;
    // 模拟上次充电结束后的运行次数为0的情况
    info.last_charge_info_used = FALSE;
    info.sys_run_cnt_after_last_charge_end = 0;
    MOCKER(read_file)
        .stubs()
        .with(any(),outBoundP((void *)&info, sizeof(info)), any())
        .will(returnValue(SUCCESS));
    MOCKER(save_charge_end_info)
        .stubs()
        .will(returnValue(SUCCESS));
    EXPECT_EQ(update_sys_run_cnt_after_last_chg(), SUCCESS);
    GlobalMockObject::verify();

    // 模拟达到保存条件的情况（需要修改静态变量，这里通过多次调用模拟）
    // 假设BCMUAPP_BCMU_TIMER为60，则需要24*60*60/60=1440次才会保存
    info.sys_run_cnt_after_last_charge_end = 1;
    MOCKER(read_file)
        .stubs()
        .with(any(),outBoundP((void *)&info, sizeof(info)), any())
        .will(returnValue(SUCCESS));
    // 这里我们通过mock save_charge_end_info来验证是否达到了保存条件
    MOCKER(save_charge_end_info)
        .stubs()
        .will(returnValue(SUCCESS));
    for (; (i < (24 * 60 * 60 / (BCMUAPP_BCMU_TIMER / 1000))); i++)
    {
        EXPECT_EQ(update_sys_run_cnt_after_last_chg(),FAILURE);
    }

    EXPECT_EQ(update_sys_run_cnt_after_last_chg(), SUCCESS);
    GlobalMockObject::verify();
}

/* Started by AICoder, pid:24cb2ffe64ue74c1400e0a47f0f3d739e8f54994 */
TEST_F(bmu_soc, SaveAdjustSocHistoryEventNoAdjustmentNeeded) {
    soc_adjust_record_t record = {enum_soc_adjust_none,  {FALSE, FALSE, FALSE, FALSE}};
    EXPECT_EQ(save_adjust_soc_history_event(record), FAILURE);
}

TEST_F(bmu_soc, SaveAdjustSocHistoryEventChargeEndAdjustment) {
    soc_adjust_record_t record = {enum_soc_adjust_charge_end,  {TRUE, FALSE, FALSE, FALSE}};
    EXPECT_EQ(save_adjust_soc_history_event(record), SUCCESS);
    record.soc_adjust_type = enum_soc_adjust_charge_end;
    record.soc_adjust_record_index[0] = TRUE;
    EXPECT_EQ(save_adjust_soc_history_event(record), SUCCESS); // 应该只记录一次
}

TEST_F(bmu_soc, SaveAdjustSocHistoryEventDischargeEndAdjustment) {
    soc_adjust_record_t record = {enum_soc_adjust_discharge_end, {FALSE, TRUE, FALSE, FALSE}};
    EXPECT_EQ(save_adjust_soc_history_event(record), SUCCESS);
}

TEST_F(bmu_soc, SaveAdjustSocHistoryEventNonPlatformStaticAdjustment) {
    soc_adjust_record_t record = {enum_soc_adjust_non_platform_static, {FALSE, FALSE, TRUE, FALSE}};
    EXPECT_EQ(save_adjust_soc_history_event(record), SUCCESS);
}

TEST_F(bmu_soc, SaveAdjustSocHistoryEventInvalidAdjustmentType) {
    soc_adjust_record_t record = {(soc_adjust_type_e)99,  {FALSE, FALSE, FALSE, TRUE}};
    EXPECT_EQ(save_adjust_soc_history_event(record), FAILURE);
}

TEST_F(bmu_soc, SaveAdjustSocHistoryEventBoundaryCondition) {
    soc_adjust_record_t record = {enum_soc_adjust_charge_end,  {TRUE, TRUE, TRUE, TRUE}};
    EXPECT_EQ(save_adjust_soc_history_event(record), SUCCESS);
    for (int i = 0; i < BMU_NUM; ++i) {
        EXPECT_TRUE(s_before_soc_adjust_record.soc_adjust_record_index[i]);
    }
}
/* Ended by AICoder, pid:24cb2ffe64ue74c1400e0a47f0f3d739e8f54994 */

/* Started by AICoder, pid:68b897c561xc915147d70a27203d2a510a4064dd */
TEST_F(bmu_soc, AdjustBMUSocChargeStatus) {
    int bcmu_battery_status = BCMU_BAT_STATUS_CHARGE;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(bcmu_battery_status), type_int);

    EXPECT_EQ(adjust_bmu_soc(), SUCCESS);
}

TEST_F(bmu_soc, AdjustBMUSocDischargeStatus) {
    int bcmu_battery_status = BCMU_BAT_STATUS_DISCHARGE;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(bcmu_battery_status), type_int);

    EXPECT_EQ(adjust_bmu_soc(), SUCCESS);
}

TEST_F(bmu_soc, AdjustBMUSocStandbyStatus) {
    int bcmu_battery_status = BCMU_BAT_STATUS_STANDY;
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_BATTERY_CLUSTER_BATTERY_STATUS, &bcmu_battery_status, sizeof(bcmu_battery_status), type_int);

    EXPECT_EQ(adjust_bmu_soc(), SUCCESS);
}

/* Ended by AICoder, pid:68b897c561xc915147d70a27203d2a510a4064dd */

TEST_F(bmu_soc, update_soc_correction_alarm_judge_data_NoFullEvent) {
    // 准备测试数据
    int soc_cal_alarm_status = FALSE;
    
    // 设置BMU的累计充放电容量
    s_bmu_cal_soc[0].accumulated_ah_since_correction = 10.0f;
    s_bmu_cal_soc[1].accumulated_ah_since_correction = 20.0f;
    s_bmu_cal_soc[2].accumulated_ah_since_correction = 5.0f;
    s_bmu_cal_soc[3].accumulated_ah_since_correction = 15.0f;
    
    // 设置SOC校正告警状态
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_CLUSTER_SOC_CORRECTION_ALARM, 
                &soc_cal_alarm_status, sizeof(soc_cal_alarm_status), type_int);
    
    // 模拟判断函数返回FALSE
    MOCKER(judge_bmu_charge_full_status)
        .stubs()
        .will(returnValue(FALSE));
    
    MOCKER(judge_bmu_discharge_full_status)
        .stubs()
        .will(returnValue(FALSE));
    
    // 执行测试
    EXPECT_EQ(SUCCESS, update_soc_correction_alarm_judge_data());
    
    // 验证结果
    float max_accumulated_ah = 0.0f;
    int is_full_event = FALSE;
    
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_MAX_CHG_DISCHG_AH_SINCE_LAST_SOC_CALIBRATION, 
                &max_accumulated_ah, sizeof(max_accumulated_ah));
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_FULL_CHG_OR_DISCHG_STATUS, 
                &is_full_event, sizeof(is_full_event));
    
    EXPECT_FLOAT_EQ(20.0f, max_accumulated_ah); // 最大值应为20.0f
    EXPECT_FALSE(is_full_event); // 没有满充满放事件
    
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, update_soc_correction_alarm_judge_data_ChargeFullEvent) {
    // 准备测试数据
    int soc_cal_alarm_status = TRUE;
    
    // 设置BMU的累计充放电容量
    s_bmu_cal_soc[0].accumulated_ah_since_correction = 10.0f;
    s_bmu_cal_soc[1].accumulated_ah_since_correction = 20.0f;
    s_bmu_cal_soc[2].accumulated_ah_since_correction = 5.0f;
    s_bmu_cal_soc[3].accumulated_ah_since_correction = 15.0f;
    
    // 设置SOC校正告警状态
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_CLUSTER_SOC_CORRECTION_ALARM, 
                &soc_cal_alarm_status, sizeof(soc_cal_alarm_status), type_int);
    
    // 模拟满充事件
    MOCKER(judge_bmu_charge_full_status)
        .stubs()
        .will(returnValue(TRUE));
    
    MOCKER(judge_bmu_discharge_full_status)
        .stubs()
        .will(returnValue(FALSE));
    
    // 执行测试
    EXPECT_EQ(SUCCESS, update_soc_correction_alarm_judge_data());
    
    // 验证结果
    float max_accumulated_ah = 0.0f;
    int is_full_event = FALSE;
    
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_MAX_CHG_DISCHG_AH_SINCE_LAST_SOC_CALIBRATION, 
                &max_accumulated_ah, sizeof(max_accumulated_ah));
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_FULL_CHG_OR_DISCHG_STATUS, 
                &is_full_event, sizeof(is_full_event));
    
    EXPECT_FLOAT_EQ(20.0f, max_accumulated_ah); // 最大值应为20.0f
    EXPECT_TRUE(is_full_event); // 有满充事件
    
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, update_soc_correction_alarm_judge_data_DischargeFullEvent) {
    // 准备测试数据
    int soc_cal_alarm_status = TRUE;
    
    // 设置BMU的累计充放电容量
    s_bmu_cal_soc[0].accumulated_ah_since_correction = 10.0f;
    s_bmu_cal_soc[1].accumulated_ah_since_correction = 20.0f;
    s_bmu_cal_soc[2].accumulated_ah_since_correction = 5.0f;
    s_bmu_cal_soc[3].accumulated_ah_since_correction = 15.0f;
    
    // 设置SOC校正告警状态
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_CLUSTER_SOC_CORRECTION_ALARM, 
                &soc_cal_alarm_status, sizeof(soc_cal_alarm_status), type_int);
    
    // 模拟满放事件
    MOCKER(judge_bmu_charge_full_status)
        .stubs()
        .will(returnValue(FALSE));
    
    MOCKER(judge_bmu_discharge_full_status)
        .stubs()
        .will(returnValue(TRUE));
    
    // 执行测试
    EXPECT_EQ(SUCCESS, update_soc_correction_alarm_judge_data());
    
    // 验证结果
    float max_accumulated_ah = 0.0f;
    int is_full_event = FALSE;
    
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_MAX_CHG_DISCHG_AH_SINCE_LAST_SOC_CALIBRATION, 
                &max_accumulated_ah, sizeof(max_accumulated_ah));
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_FULL_CHG_OR_DISCHG_STATUS, 
                &is_full_event, sizeof(is_full_event));
    
    EXPECT_FLOAT_EQ(20.0f, max_accumulated_ah); // 最大值应为20.0f
    EXPECT_TRUE(is_full_event); // 有满放事件
    
    GlobalMockObject::verify();
}

TEST_F(bmu_soc, update_soc_correction_alarm_judge_data_AlarmNotSet) {
    // 准备测试数据
    int soc_cal_alarm_status = FALSE;
    
    // 设置BMU的累计充放电容量
    s_bmu_cal_soc[0].accumulated_ah_since_correction = 10.0f;
    s_bmu_cal_soc[1].accumulated_ah_since_correction = 20.0f;
    s_bmu_cal_soc[2].accumulated_ah_since_correction = 5.0f;
    s_bmu_cal_soc[3].accumulated_ah_since_correction = 15.0f;
    
    // 设置SOC校正告警状态
    pdt_set_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ALM_BATTERY_CLUSTER_SOC_CORRECTION_ALARM, 
                &soc_cal_alarm_status, sizeof(soc_cal_alarm_status), type_int);
    
    // 模拟满充满放事件都发生
    MOCKER(judge_bmu_charge_full_status)
        .stubs()
        .will(returnValue(TRUE));
    
    MOCKER(judge_bmu_discharge_full_status)
        .stubs()
        .will(returnValue(TRUE));
    
    // 执行测试
    EXPECT_EQ(SUCCESS, update_soc_correction_alarm_judge_data());
    
    // 验证结果
    float max_accumulated_ah = 0.0f;
    int is_full_event = FALSE;
    
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_ANA_MAX_CHG_DISCHG_AH_SINCE_LAST_SOC_CALIBRATION, 
                &max_accumulated_ah, sizeof(max_accumulated_ah));
    pdt_get_data(SID_BATTERY_CLUSTER_MANAGEMENT_UNIT_DIG_FULL_CHG_OR_DISCHG_STATUS, 
                &is_full_event, sizeof(is_full_event));
    
    EXPECT_FLOAT_EQ(20.0f, max_accumulated_ah); // 最大值应为20.0f
    EXPECT_FALSE(is_full_event); // 告警未置位，不应设置满充满放事件
    
    GlobalMockObject::verify();
}

/**
 * @brief 测试序列1：模拟系统首次上电的完整流程
 * @details 这个测试模拟了系统从首次上电到检测参数变更的完整过程
 */
TEST_F(bmu_soc, CompleteWorkflowFromPowerOn)
{
    bmu_sox_deal_t test_bmu_sox_deal[BMU_NUM];

    // 步骤1：模拟首次上电，设置初始SOH值
    memset(test_bmu_sox_deal, 0, sizeof(test_bmu_sox_deal));
    set_all_mock_backup_replacement_soh(85);

    // 首次调用应该初始化静态变量并返回SUCCESS（如果是真正的首次运行）
    // 或者根据当前参数状态返回相应结果
    int result = check_backup_replacement_soh_change(0, test_bmu_sox_deal);
    EXPECT_TRUE(result == SUCCESS);

    // 步骤2：修改BMU1的SOH参数，模拟备件替换
    int bmu_index = 1;
    int new_soh = 75; // 从85降到75
    set_mock_backup_replacement_soh(bmu_index, new_soh);

    memset(test_bmu_sox_deal, 0, sizeof(test_bmu_sox_deal));
    result = check_backup_replacement_soh_change(bmu_index, test_bmu_sox_deal);

    // 如果检测到参数变更，应该返回SUCCESS并设置相关标志
    if (result == SUCCESS)
    {
        EXPECT_TRUE(test_bmu_sox_deal[bmu_index].save_to_file);
        EXPECT_FLOAT_EQ(test_bmu_sox_deal[bmu_index].calender_life_decline, 100.0f - new_soh);
        EXPECT_FLOAT_EQ(test_bmu_sox_deal[bmu_index].cycle_life_decline, 0.0f);
        EXPECT_FLOAT_EQ(test_bmu_sox_deal[bmu_index].added_life_decline, 0.0f);
    }

    // 步骤3：再次调用相同参数，应该不检测到变更
    memset(test_bmu_sox_deal, 0, sizeof(test_bmu_sox_deal));
    result = check_backup_replacement_soh_change(bmu_index, test_bmu_sox_deal);
    EXPECT_EQ(result, FAILURE); // 参数未变更
    EXPECT_FALSE(test_bmu_sox_deal[bmu_index].save_to_file);
}

/**
 * @brief 测试序列2：连续的参数变更检测
 * @details 测试连续多次参数变更的检测能力
 */
TEST_F(bmu_soc, ContinuousParameterChanges)
{
    bmu_sox_deal_t test_bmu_sox_deal[BMU_NUM];
    int bmu_index = 2;

    // 第一次变更：设置为70
    int soh_value_1 = 70;
    set_mock_backup_replacement_soh(bmu_index, soh_value_1);
    memset(test_bmu_sox_deal, 0, sizeof(test_bmu_sox_deal));

    int result = check_backup_replacement_soh_change(bmu_index, test_bmu_sox_deal);
    if (result == SUCCESS)
    {
        EXPECT_TRUE(test_bmu_sox_deal[bmu_index].save_to_file);
        EXPECT_FLOAT_EQ(test_bmu_sox_deal[bmu_index].calender_life_decline, 100.0f - soh_value_1);
    }

    // 第二次变更：设置为65
    int soh_value_2 = 65;
    set_mock_backup_replacement_soh(bmu_index, soh_value_2);
    memset(test_bmu_sox_deal, 0, sizeof(test_bmu_sox_deal));

    result = check_backup_replacement_soh_change(bmu_index, test_bmu_sox_deal);
    EXPECT_EQ(result, SUCCESS); // 应该检测到变更
    EXPECT_TRUE(test_bmu_sox_deal[bmu_index].save_to_file);
    EXPECT_FLOAT_EQ(test_bmu_sox_deal[bmu_index].calender_life_decline, 100.0f - soh_value_2);

    // 第三次变更：设置为80（提升）
    int soh_value_3 = 80;
    set_mock_backup_replacement_soh(bmu_index, soh_value_3);
    memset(test_bmu_sox_deal, 0, sizeof(test_bmu_sox_deal));

    result = check_backup_replacement_soh_change(bmu_index, test_bmu_sox_deal);
    EXPECT_EQ(result, SUCCESS); // 应该检测到变更
    EXPECT_TRUE(test_bmu_sox_deal[bmu_index].save_to_file);
    EXPECT_FLOAT_EQ(test_bmu_sox_deal[bmu_index].calender_life_decline, 100.0f - soh_value_3);
}

/**
 * @brief 测试序列3：边界值测试
 * @details 测试SOH参数的边界值处理
 */
TEST_F(bmu_soc, BoundaryValueTesting)
{
    bmu_sox_deal_t test_bmu_sox_deal[BMU_NUM];
    int bmu_index = 3;

    // 测试SOH = 0的情况
    set_mock_backup_replacement_soh(bmu_index, 0);
    memset(test_bmu_sox_deal, 0, sizeof(test_bmu_sox_deal));

    int result = check_backup_replacement_soh_change(bmu_index, test_bmu_sox_deal);
    if (result == SUCCESS)
    {
        EXPECT_TRUE(test_bmu_sox_deal[bmu_index].save_to_file);
        EXPECT_FLOAT_EQ(test_bmu_sox_deal[bmu_index].calender_life_decline, 100.0f); // 100 - 0 = 100
    }

    // 测试SOH = 100的情况
    set_mock_backup_replacement_soh(bmu_index, 100);
    memset(test_bmu_sox_deal, 0, sizeof(test_bmu_sox_deal));

    result = check_backup_replacement_soh_change(bmu_index, test_bmu_sox_deal);
    EXPECT_EQ(result, SUCCESS); // 应该检测到变更（从0到100）
    EXPECT_TRUE(test_bmu_sox_deal[bmu_index].save_to_file);
    EXPECT_FLOAT_EQ(test_bmu_sox_deal[bmu_index].calender_life_decline, 0.0f); // 100 - 100 = 0
}

/**
 * @brief 测试序列4：错误处理
 * @details 测试各种错误情况的处理
 */
TEST_F(bmu_soc, ErrorHandling)
{
    bmu_sox_deal_t test_bmu_sox_deal[BMU_NUM];

    // 测试pdt_get_data失败的情况
    MOCKER(pdt_get_data)
        .stubs()
        .with(any(), any(), any())
        .will(returnValue(FAILURE));

    int result = check_backup_replacement_soh_change(0, test_bmu_sox_deal);
    EXPECT_EQ(result, FAILURE);

    GlobalMockObject::reset();

    // 测试边界BMU索引
    result = check_backup_replacement_soh_change(BMU_NUM - 1, test_bmu_sox_deal);
    // 根据实际实现，这里可能成功也可能失败，取决于参数是否变更
    EXPECT_TRUE(result == SUCCESS || result == FAILURE);
}

int main(int argc, char *argv[]) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}


