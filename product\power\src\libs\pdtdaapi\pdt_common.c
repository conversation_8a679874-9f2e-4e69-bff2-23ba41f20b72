/*****************************************************************************
 (C) 2018 ZTE Corporation. 版权所有.

 模块名      :  产品公共模块
 文件名      :  pdt_common.c
 相关文件    :
 内容摘要    : 主要用于实现产品侧公用函数
 作者        : zhangdedi
 版本        : 3.0
 ------------------------------------------------------------------------------
 修改记录:
 日 期       版本号    修改人     修改内容
 2018-10-14  3.0      zhangdedi    创建

*****************************************************************************/
#include "pdt_common.h"
#include <string.h>
#include "plat_utils_safe_inf.h"

#ifdef __cplusplus
extern "C"{
#endif

extern str32 replace_string;

/**************************内部函数声明*******************/
//time_t time_struct_2_time_t(time_base_t time_base )

static double_link_list_struct *
list_jump_to_position(double_link_list_struct *list_start, int position);

/*********************  ��̬����ԭ�Ͷ���  **********************/
STATIC int get_valid_input_data(sid real_data_sid, number_data* alm_input, number_data_type_e *alm_input_type)
{
     int ret = FAILURE;

     switch ( *alm_input_type ) {
        case int_number:
            ret = pdt_get_data(real_data_sid, &alm_input->int_data, sizeof(alm_input->int_data));
            break;
        case float_number:
            ret = pdt_get_data(real_data_sid, &alm_input->float_data, sizeof(alm_input->float_data));
            break;
        case double_number:
            ret = pdt_get_data(real_data_sid, &alm_input->double_data, sizeof(alm_input->double_data));
            break;
        default:
            break;
    }
    return ret;
}


STATIC int get_valid_input_data_for_check(sid real_data_sid, number_data* alm_input, number_data_type_e *alm_input_type)
{
     int ret = FAILURE;

     switch ( *alm_input_type ) {
        case int_number:
            ret = get_data_from_check(real_data_sid, &alm_input->int_data, sizeof(alm_input->int_data));
            break;
        case float_number:
            ret = get_data_from_check(real_data_sid, &alm_input->float_data, sizeof(alm_input->float_data));
            break;
        case double_number:
            ret = get_data_from_check(real_data_sid, &alm_input->double_data, sizeof(alm_input->double_data));
            break;
        default:
            break;
    }
    return ret;
}
/*********************  静态函数原型定义  **********************/

double trans_data_type_to_double(number_data_type_e data_type, number_data data_value)
{
    double double_value;
    if(data_type == int_number)
    {
        double_value = (double)(data_value.int_data);
    }
    else if(data_type == float_number)
    {
        double_value = (double)(data_value.float_data);
    }
    else
    {
        double_value = data_value.double_data;
    }
    return double_value;
}

/*��ȡ��������γ��*/
STATIC int get_sid_dimension(sid sig_sid, dimensions sig_dims)
{
    int ret;

    plat_memset_s(sig_dims,sizeof(dimensions), 0, sizeof(dimensions));
    ret = get_attr_dimension(sig_sid, sig_dims);
    if ( ret != SUCCESS )
    {
    	return FAILURE;
    }
    return SUCCESS;
}


/********************* �ⲿ�������� ****************************/
time_t mktime_local(struct tm* pt_time)
{
    time_t tTimeReal;
    struct tm tTmTime;
    //struct tm* ptTmTime;

    tTimeReal = time( NULL );
    localtime_r(&tTimeReal,&tTmTime);

    pt_time->tm_yday = tTmTime.tm_yday;
    pt_time->tm_isdst = tTmTime.tm_isdst;
    pt_time->tm_gmtoff = tTmTime.tm_gmtoff;
    pt_time->tm_zone = tTmTime.tm_zone;

    return mktime(pt_time);
}
/*************************************************************************
* 函数名	:	if_leap_year
* 调    用  ：  无
* 被 调 用  ：
* 输入参数	:	year--年
* 返回值	:	TRUE--为闰年 FALSE--非闰年
* 功能描述	:	判断是否是闰年
* 作    者  ：
* 设计日期  ：
* 修改记录  ：
* 日    期		版	本		修改人		修改摘要
*************************************************************************/
int	if_leap_year( int year )
{
    return ( ( (year%4==0) && (year%100!=0)  )|| (year%400==0) );
}

/*************************************************************************
* 函数名	:	is_check_day_valid
* 调    用  ：  无
* 被 调 用  ：
* 输入参数	:	time_base_t t_time
* 返回值	:	TRUE--日期有效 FALSE--日期无效
* 功能描述	:	判断是日期是否有效
* 作    者  ：
* 设计日期  ：
* 修改记录  ：
* 日    期		版	本		修改人		修改摘要
*************************************************************************/

int  is_check_day_valid( time_base_t t_time )
{
    const int  months_Leap[12]   ={31,29,31,30,31,30,31,31,30,31,30,31};
    const int  months_no_leap[12] ={31,28,31,30,31,30,31,31,30,31,30,31};
    int index = 0;

    index =  t_time.month -1;
    if(index < 0){index = 0;}
    if(index > 11){index = 11;}

    if( if_leap_year( t_time.year ) )
    {
    	if( (t_time.day > months_Leap[index]) ||
            (t_time.day < 1) )
        {
        	return FALSE;
        }
    }
    else
    {
    	if( (t_time.day > months_no_leap[index]) ||
            (t_time.day < 1) )
        {
        	return FALSE;
        }
    }

    return TRUE;
}

int  is_check_hour_min_sec_valid( time_base_t t_time )
{
    return ((t_time.hour>23) || (t_time.minute>59) || (t_time.second>59))? FALSE:TRUE;
}
/*************************************************************************
* 函数名	：	check_time_valid
* 调    用  ：  无
* 被 调 用  ：
* 输入参数	： 	t_time: 时间结构体
* 返回值	：	TRUE-有效，FALSE-无效
* 功能描述	：	检查设置时间的有效性
* 作    者  ：
* 设计日期  ：
* 修改记录  ：
* 日    期		版	本		修改人		修改摘要
*************************************************************************/
int 	check_time_valid( time_base_t  t_time )
{
    if( (t_time.year>MAX_EDIT_YEAR) || (t_time.year<MIN_EDIT_YEAR) )
    {
    	return FALSE;
    }
    if( (t_time.month<1) || (t_time.month>12) )
    {
    	return FALSE;
    }

    if(FALSE == is_check_day_valid(t_time))
    {
        return FALSE;
    }

    return  is_check_hour_min_sec_valid(t_time);
}

/****************************************************************************
* 函数名称：GetTime()
* 调    用：无
* 被 调 用：
* 输入参数：pTime--指向T_TimeStruct结构体变量的指针
* 返 回 值：成功则返回0，否则返回-1
* 功能描述：获取系统当前时间
* 作    者：潘奇银
* 设计日期：2010-01-12
* 修改记录：
* 日    期		版	本		修改人		修改摘要
***************************************************************************/
int pdt_get_time( time_base_t *p_time )
{
    struct tm tm_time;

    time_t t_time;
    if(p_time == NULL)
    {
        return -1;
    }

    t_time = time( NULL );
    localtime_r(&t_time,&tm_time);

    p_time->year = tm_time.tm_year + 1900;
    p_time->month= tm_time.tm_mon + 1;
    p_time->day = tm_time.tm_mday;
    p_time->hour = tm_time.tm_hour;
    p_time->minute = tm_time.tm_min;
    p_time->second = tm_time.tm_sec;

    return 0;
}

/****************************************************************************
* 函数名称：TimeCompare()
* 调    用：无
* 被 调 用：
* 输入参数：ptTime1--时间1,ptTime2--时间2
* 返 回 值：ptTime1>ptTime2时返回1,ptTime1=ptTime2则返回0，ptTime1<ptTime2返回-1,比较失败返回-2
* 功能描述：比较时间大小
* 作    者：潘奇银
* 设计日期：2010-02-06
* 修改记录：
* 日    期		版	本		修改人		修改摘要
***************************************************************************/
int pdt_time_compare( time_base_t  time_first, time_base_t time_second )
{
    time_t t_time1,t_time2;

    if((FALSE == check_time_valid( time_first ))
        ||(FALSE == check_time_valid( time_second )) )
    {
        return -2;
    }
    t_time1= time_struct_2_time_t(time_first);
    t_time2 = time_struct_2_time_t(time_second);

    if( t_time1 > t_time2 )
    {
        return 1;
    }
    else if( t_time1 == t_time2 )
    {
        return 0;
    }

    return -1;
}

/****************************************************************************
* 函数名称：dateCompare()
* 调    用：无
* 被 调 用：
* 输入参数：ptTime1--时间1,ptTime2--时间2
* 返 回 值：ptTime1>ptTime2时返回1,ptTime1=ptTime2则返回0，ptTime1<ptTime2返回-1,比较失败返回-2
* 功能描述：比较时间大小
* 作    者：潘奇银
* 设计日期：2010-02-06
* 修改记录：
* 日    期		版	本		修改人		修改摘要
***************************************************************************/
int pdt_date_compare( time_base_t  time_first, time_base_t time_second )
{
    time_t t_time1,t_time2;

    if((FALSE == check_time_valid( time_first ))
        ||(FALSE == check_time_valid( time_second )) )
    {
        return -2;
    }
    t_time1= date_struct_2_time_t(time_first);
    t_time2 =date_struct_2_time_t(time_second);

    if( t_time1 > t_time2 )
    {
        return 1;
    }
    else if( t_time1 == t_time2 )
    {
        return 0;
    }

    return -1;
}



/****************************************************************************
* 函数名称：TimeStruct2Time_t()
* 输入参数：
            pTime--自定义类型的时间
            pFileName--原先的文件名，为"XXXTime1-"的形式
            pStrEndTime最后一条记录的时间
* 返 回 值：time_t类型的时间
* 功能描述：将自定义类型的时间转换成time_t类型时间
* 作    者：潘奇银
* 设计日期：2011-6-2
* 修改记录：
* 日    期		版	本		修改人		修改摘要
***************************************************************************/
time_t time_struct_2_time_t(time_base_t time_base )
{
    struct tm tm_time;

    plat_memset_s(&tm_time,sizeof(tm_time),0x00,sizeof(tm_time));
    tm_time.tm_year = time_base.year - 1900;
    tm_time.tm_mon = time_base.month -1;
    tm_time.tm_mday = time_base.day ;
    tm_time.tm_hour = time_base.hour ;
    tm_time.tm_min = time_base.minute;
    tm_time.tm_sec = time_base.second;
    tm_time.tm_isdst = -1;
    return mktime( &tm_time );
}

/****************************************************************************
* 函数名称：DateStruct2Time_t()
* 输入参数：
            pTime--自定义类型的时间
            pFileName--原先的文件名，为"XXXTime1-"的形式
            pStrEndTime最后一条记录的时间
* 返 回 值：time_t类型的时间
* 功能描述：将自定义类型的时间转换成time_t类型时间
* 作    者：潘奇银
* 设计日期：2011-6-2
* 修改记录：
* 日    期		版	本		修改人		修改摘要
***************************************************************************/
time_t date_struct_2_time_t(time_base_t time_base )
{
    struct tm tm_time;

    plat_memset_s(&tm_time,sizeof(tm_time),0x00,sizeof(tm_time));
    tm_time.tm_year = time_base.year - 1900;
    tm_time.tm_mon = time_base.month -1;
    tm_time.tm_mday = time_base.day ;
    tm_time.tm_hour = 0 ;
    tm_time.tm_min = 0;
    tm_time.tm_sec = 0;
    tm_time.tm_isdst = -1;
    return mktime( &tm_time );
}


/*****************************************************************************
* 函数名    if_auto_event
* 调    用  ：无
* 被 调 用  ：
* 输入参数  ：无
* 返回值    ：无
* 功能描述  ：判断自动事件，比如周期均充、周期测试、周期检测等
* 作    者  ：
* 设计日期  ：
* 修改记录  ：
* 日    期		版	本		修改人		修改摘要
*****************************************************************************/
int	if_auto_event( int	enable, time_base_t *hope_time, int hour )
{
	time_base_t	current_time, time_temp;

	if ( hope_time == NULL )
	{
		return FALSE;
	}

	if ( !enable )
	{
		return FALSE;
	}

	pdt_get_time(&current_time);
	time_temp = *hope_time;
	time_temp.hour = hour;
	time_temp.minute= 0;
	time_temp.second= 0;

	if ( pdt_time_compare( current_time, time_temp ) < 0 )
	{
		return FALSE;
	}

	return TRUE;
}


/****************************************************************************
* 函数名称：Fclose()
* 调    用：无
* 被 调 用：
* 输入参数：无
* 返 回 值：参数出错则返回FAILURE返回SUCCESS，参数出错则返回FAILURE
* 功能描述：关闭文件并清空文件描述符
* 作    者：潘奇银
* 设计日期：2010-05-13
* 修改记录：
* 日    期		版	本		修改人		修改摘要
***************************************************************************/
int file_close( FILE ** pFile )
{
    if( pFile && *pFile )
    {
        fclose( *pFile );
        *pFile = NULL;
        return 0;
    }

    return -1;
}


/****************************************************************************
* 函数名称  ：WriteFile()
* 调    用  ：无
* 被 调 用  ：
* 输入参数  ：acFileName--要写入的文件绝对路径,buff--要写入的数据缓冲区，ulSize--要写入的数据字节数量
* 返 回 值  ：成功则返回0，否则返回-1
* 功能描述  ：将数据写入二进制文件中
* 作    者  ：潘奇银
* 设计日期  ：2010-01-08
* 修改记录  ：
* 日    期		版	本		修改人		修改摘要
***************************************************************************/
int write_file( char *file_Name,void *buff, unsigned int ulSize )
{
    FILE * slParaFD = NULL;
    int slRetVal;

    if(file_Name == NULL)
    return -1;

    if(buff == NULL)
    return -1;

    slParaFD = fopen( file_Name, "w+");
    if ( slParaFD == NULL )
    {
        SYSLOG_NOTICE( "fopen error in function WriteFile:" );    // 写错误日志
        slRetVal = -1;
    }
    else
    {
        fwrite( buff, ulSize,1, slParaFD );
        slRetVal = 0;
        file_close( &slParaFD );
    }
    return slRetVal;
}


/****************************************************************************
* 函数名称：ReadFile()
* 调    用：无
* 被 调 用：
* 输入参数：acFileName:文件名指针
            buff:欲存放读取出来的数据空间指针
            ulSize:要读的字节数
* 返 回 值：读到的字节数；错误返回-1.
* 功能描述：
* 作    者：余颖
* 设计日期：2011-06-18
* 修改记录：
* 日    期		版	本		修改人		修改摘要
***************************************************************************/
int read_file( char *file_Name,void *buff, unsigned int ulSize )
{
    FILE * slParaFD = NULL;
    int slRetVal;

    if(file_Name == NULL)
    return -1;

    if(buff == NULL)
    return -1;

    slParaFD = fopen( file_Name, "r");
    if ( slParaFD == NULL )
    {
        SYSLOG_NOTICE("%s", file_Name);
        slRetVal = -1;
    }
    else
    {
        slRetVal=fread( buff, ulSize,1, slParaFD );
        file_close( &slParaFD );
    }
    return slRetVal;

}


// boolean invalid_int_data(data_type_e data_type, long long int_data) {
//     switch ( data_type ) {
//         case type_char:
//             return (char)int_data == INT8S_INVALID;

//         case type_unsigned_char:
//             return (unsigned char)int_data ==  INT8U_INVALID;

//         case type_short:
//             return (short)int_data == INT16S_INVALID;

//         case type_unsigned_short:
//             return (unsigned short)int_data == INT16U_INVALID;

//         case type_int:
//             return (int)int_data == INT32S_INVALID;

//         case type_unsigned_int:
//             return (unsigned int)int_data == INT32U_INVALID;

//         case type_longlong:
//             return int_data == 0x8000000000000000;

//         default:
//             break;
//     }

//     return FALSE;
// }

number_data_type_e get_number_data_type(data_type_e data_type)
{
    switch ( data_type ) {
        case type_char:
        case type_unsigned_char:
        case type_short:
        case type_unsigned_short:
        case type_int:
        case type_unsigned_int:
        case type_longlong:
            return int_number;

        case type_float:
            return float_number;

        case type_double:
            return double_number;

        default:
            return max_number_type;
    }

    return max_number_type;
}

int get_sid_type_and_value(sid real_data_sid, number_data* alm_value, number_data_type_e *alm_value_type)
{

    int ret;
    data_type_e    data_type;

	if(is_data_valid(real_data_sid, TYPE_FLAG) != DATA_VALID)
    {
       return INVALID_RET;
    }


    ret = get_attr_data_type(real_data_sid, &data_type);
    if(ret != SUCCESS)
    {
        return FAILURE;
    }
    *alm_value_type = get_number_data_type(data_type);
    ret = get_valid_input_data(real_data_sid, alm_value, alm_value_type);
    return ret;

}

int get_sid_type_and_value_for_check(sid real_data_sid, number_data* alm_value, number_data_type_e *alm_value_type)
{

    int ret;
    data_type_e    data_type;

	if(is_data_valid_on_check(real_data_sid, TYPE_FLAG) == DATA_INVALID)
    {
        return INVALID_RET;
    }

    ret = get_attr_data_type(real_data_sid, &data_type);
    if(ret != SUCCESS)
    {
        return FAILURE;
    }
    *alm_value_type = get_number_data_type(data_type);
    ret = get_valid_input_data_for_check(real_data_sid, alm_value, alm_value_type);
    return ret;

}


int relation_operate(number_data_type_e data1_type,
					 number_data data1,
                     number_data_type_e data2_type,
                     number_data data2,
                     relation_op_e op,
                     int* op_rst)
{
    if ( op_rst == NULL )
        return FAILURE;

    double op_left, op_right;

    //�������ת��Ϊdouble
    op_left = trans_data_type_to_double(data1_type, data1);

    //�Ҳ�����ת��Ϊdouble
    op_right = trans_data_type_to_double(data2_type, data2);

    //���������������Ƚϴ�С
    do_relation_operate(op_left, op_right, op, op_rst);

}


/*��ȡsid�����豸�ĸ���*/
int get_sid_belong_dev_num(sid sig_sid)
{
    int dev_num;
    devices_e dev;

    dev = SID_GET_DEV_TYPE(sig_sid);
    dev_num = get_device_inst_count(dev);

    return dev_num;
}


int cal_sig_num_in_dimenson(sid sig_sid)
{
    int dim_idx, sig_num = 1, ret;
    dimensions    alm_sid_dims;

    ret = get_sid_dimension(sig_sid, alm_sid_dims);
    if(ret == FAILURE)
    {
        return 0;
    }
    for ( dim_idx = 0; dim_idx < DIMENSION_MAX; dim_idx++ )
    {
        if(alm_sid_dims[dim_idx] > 0)
        {
            sig_num *= alm_sid_dims[dim_idx];
        }

    }
    return sig_num;

}


/**********************************************************************
* 函数名称： list_append
* 功能描述： 增加元素到链表尾部
* 访问的表： 无
* 修改的表： 无
* 输入参数： list链表首地址，
			 data_in输入元素数据首地址
			 data_size数据长度
* 输出参数： 无
* 返 回 值： 链表首地址
* 其它说明： list为NULL时，将创建新链表
* 修改日期		  版本号 	修改人		  修改内容
* -----------------------------------------------
*
***********************************************************************/
double_link_list_struct *list_append( double_link_list_struct *list, void *data_in, int data_size )
{
	double_link_list_struct *p_list_new, *p_pre, *p_ret;
	if( data_size <= 0 )
	{
		return NULL;
	}
	if( list == NULL )
	{
		p_list_new = (double_link_list_struct *)malloc(sizeof(double_link_list_struct));
		//ASSERT_LOG( pListNew != NULL );
		if( p_list_new == NULL )
			return list;
		p_ret = p_list_new;
		p_list_new->next_ptr = NULL;
		p_list_new->pre_ptr = NULL;

	}
	else
	{
		p_list_new = list;
		p_ret = list;
		while( p_list_new->next_ptr  != NULL )
		{
			p_list_new = p_list_new->next_ptr;
		}
		p_list_new->next_ptr = (double_link_list_struct *)malloc(sizeof(double_link_list_struct));
		//ASSERT_LOG(pListNew->pNext!=NULL);
		if( p_list_new->next_ptr == NULL )
		{
			return p_ret;
		}
		p_pre = p_list_new;
		p_list_new = p_list_new->next_ptr;
		p_list_new->next_ptr = NULL;
		p_list_new->pre_ptr =p_pre;//
	}
	p_list_new->data = (void *)malloc(data_size);
	//ASSERT_LOG( pListNew->data != NULL );
	if( p_list_new->data == NULL )
	{

		return p_ret;
	}
	p_list_new->data_bytes = data_size;
	plat_memset_s(p_list_new->data, data_size,0x00, data_size);
	//return g_list_append(pList, (gpointer)pDataIn);
	memcpy_s( p_list_new->data, data_size,data_in, data_size);
	return p_ret;
}

/**********************************************************************
* 函数名称： get_list_length
* 功能描述： 获取指定的链表的元素个数
* 访问的表： 无
* 修改的表： 无
* 输入参数： list_start——链表首地址，
* 输出参数： 无
* 返 回 值： 链表内元素个数
* 其它说明： 无
* 修改日期		  版本号 	修改人		  修改内容
* -----------------------------------------------
*
***********************************************************************/
int get_list_length(double_link_list_struct *list_start)
{
	int i;
	double_link_list_struct *p_list = NULL;
	if( list_start == NULL )
	{
		return 0;
	}
	p_list = list_start;
	i = 0;
	while( p_list != NULL )
	{
		p_list = p_list->next_ptr;
		i++;
	}
	return i;
}

/**********************************************************************
* 函数名称： get_list_by_pos
* 功能描述： 从指定的链表中指定的位置获取元素的数据
* 访问的表： 无
* 修改的表： 无
* 输入参数： pListStart——链表首地址，
			 uPosition——元素位置
* 输出参数： pDataOut——输出数据首地址
* 返 回 值： 0——获取成功
			 -1——获取失败
* 其它说明： 无
* 修改日期		  版本号 	修改人		  修改内容
* -----------------------------------------------
*
***********************************************************************/
void *get_list_by_pos(double_link_list_struct *list_start, int position, void *data_out)
{
	double_link_list_struct *p_list;
	if( list_start	== NULL )
	{
		return NULL ;
	}
	else
	{
		p_list = list_jump_to_position(list_start, position);
		//ASSERT_LOG( pList != NULL );
		if( p_list == NULL )
		{
			return NULL;
		}
		if( data_out != NULL )
		{
		    memcpy_s(data_out, p_list->data_bytes, p_list->data, p_list->data_bytes);
		}
	}
	return p_list->data;
}

/**********************************************************************
* 函数名称： ListJumpToPosition
* 功能描述： 跳转到指定的链表的某个元素
* 访问的表： 无
* 修改的表： 无
* 输入参数： pListStart——链表首地址，
			 uPosition——元素位置
* 输出参数： 无
* 返 回 值： 指定的位号的元素地址
* 其它说明： 无
* 修改日期		  版本号 	修改人		  修改内容
* -----------------------------------------------
*
***********************************************************************/
static double_link_list_struct *
list_jump_to_position(double_link_list_struct *list_start, int position)
{
	double_link_list_struct *p_list, *p_ret;
	int i;
	if( list_start == NULL )
	{
		return NULL;
	}
	p_list = list_start;
	i = 0;
	while( p_list != NULL )
	{
		if( i >= position )
		{
			break;
		}
		p_list = p_list->next_ptr;
		i++;
	}
	p_ret = p_list;
	return p_ret;
}

int pdt_logo_replace(char *str, char *str_new)
{
    char oldstr[64] = {0};
    char newstr[64] = {0};
    char tempstr[64] = {0};
    int str_len = 0;
    int len = 0;

    if(str == NULL || str_new == NULL) {
        return FAILURE;
    }

    len = plat_strlen_s(replace_string, STR_LEN_32);
    if(len != 0){
        if(g_str_has_prefix(str, "ZXDT22 SF01")) {
            plat_strcpy_s(oldstr, sizeof(oldstr), "ZXDT22 SF01", sizeof("ZXDT22 SF01"));
        }
        else if(g_str_has_prefix(str, "ZXDT02")) {
            plat_strcpy_s(oldstr, sizeof(oldstr), "ZXDT02", sizeof("ZXDT02"));
        }
        else if(g_str_has_prefix(str, "ZXD3000-ZXEPS")) {
            plat_strcpy_s(oldstr, sizeof(oldstr), "ZXD3000-ZXRPS", sizeof("ZXD3000-ZXRPS"));
        }
        else if(g_str_has_prefix(str, "ZXD4000-ZXEPS")) {
            plat_strcpy_s(oldstr, sizeof(oldstr), "ZXD4000-ZXRPS", sizeof("ZXD4000-ZXRPS"));
        }
        else if(g_str_has_prefix(str, "ZXEPS")) {
            plat_strcpy_s(oldstr, sizeof(oldstr), "ZXEPS", sizeof("ZXEPS"));
        }
        else if(g_str_has_prefix(str, "ZTE")) {
            plat_strcpy_s(oldstr, sizeof(oldstr), "ZTE", sizeof("ZTE"));
        }
        else if(g_str_has_prefix(str, "ZX")) {
            plat_strcpy_s(oldstr, sizeof(oldstr), "ZX", sizeof("ZX"));
        }
        else {
            return FAILURE;
        }

        plat_memcpy_s(tempstr, sizeof(tempstr), str_new, strnlen_s(str_new, STR_LEN_64));
        str_len = plat_strlen_s(oldstr, sizeof(oldstr));
        plat_memcpy_s(newstr, sizeof(newstr), str + str_len, strnlen_s(str + str_len, STR_LEN_64));
        plat_strcat_s(tempstr, sizeof(tempstr), newstr, strnlen_s(newstr, sizeof(newstr)));
        plat_memset_s(str, plat_strlen_s(str, STR_LEN_64),0, plat_strlen_s(str, STR_LEN_64));
        str_len = plat_strlen_s(tempstr, sizeof(tempstr));
        plat_memcpy_s(str, str_len, tempstr, str_len);
    }

    return SUCCESS;
}

bin_file_object_t * load_device_option_data(const char * path)
{
    bin_file_object_t * obj = bin_file_object(path, sizeof(device_option_t));
    RETURN_VAL_IF_FAIL(obj, NULL);
    RETURN_VAL_IF_FAIL(obj->is_content_valid(obj) == SUCCESS, NULL);
    return obj;
}

boolean is_device_included(const int device_type, bin_file_object_t * obj)
{
    if (obj == NULL || obj->content == NULL) {
        return FAILURE;
    }
    device_option_t * option_data = NULL;
    int i = 0;

    option_data = (device_option_t *)(obj->content);
    g_rw_lock_reader_lock(&obj->rwlock);
    for(i = 0; i < obj->element_num; i++) {
        if (device_type == (option_data + i)->dev_type) {
            g_rw_lock_reader_unlock(&obj->rwlock);
            return ((option_data + i)->dev_included == TRUE) ? TRUE : FALSE;
       }

    }
    g_rw_lock_reader_unlock(&obj->rwlock);
    /* 设备剪裁上没有记录的设备，默认不裁剪 */
    return TRUE;
}

int calc_alm_dev_reg_count(bin_file_object_t * obj)
{
    if (obj == NULL || obj->content == NULL) {
        return FAILURE;
    }
    device_option_t * option_data = NULL;
    int count = 0, i;

    option_data = (device_option_t *)(obj->content);
    g_rw_lock_reader_lock(&obj->rwlock);
    for(i = 0; i < obj->element_num; i++) {
        if (((option_data + i)->dev_included == TRUE)
            && ((option_data + i)->ala_included == TRUE)){
            count++;
        }
    }
    g_rw_lock_reader_unlock(&obj->rwlock);
    return count;
}

int is_pdt_dev_included(boolean *dev_include_ptr, int dev_type_max, int dev_type)
{
    if(dev_include_ptr == NULL || dev_type_max <= 0)
    {
        return FALSE;
    }

    if(dev_type > 0 && dev_type < dev_type_max)
    {
        return dev_include_ptr[dev_type];
    }
    else
    {
        return FALSE;
    }
}

int compare_two_str_equal(char *str_1, char *str_2, unsigned int max_len)
{
    int length_1 = 0;
    int length_2 = 0;

    if(str_1 == NULL || str_2 == NULL)
    {
        return FALSE;
    }

    length_1 = plat_strlen_s(str_1, max_len);
    length_2 = plat_strlen_s(str_2, max_len);
    if((length_1 == length_2) && (memcmp(str_1, str_2, length_1) == 0))
    {
        return TRUE;
    }
    return FALSE;
}

STATIC float cmp_max_rate_curr_from_two_branchs(float first_curr, float second_curr)
{
    if(fabs(first_curr) >= ZERO_VALUE)
    {
        if(fabs(second_curr) >= ZERO_VALUE)
        {
            return first_curr > second_curr ? first_curr : second_curr;
        }
        else
        {
            return first_curr;
        }
    }
    else
    {
        return second_curr;
    }
}

STATIC float calc_smr_max_rate_curr_in_large_ps(acin_branch_data_t *acin_branch_data, int branch_count, int index)
{
    int i;
    int count = 0, mark = 0;

    for(i = 0; i < branch_count; i++)
    {
        if(!acin_branch_data[i].power_off_flag)
        {
            count++;
            mark = i;
        }
    }

    if(count == branch_count)
    {
        return cmp_max_rate_curr_from_two_branchs(acin_branch_data[0].max_rate_curr_array[index], acin_branch_data[1].max_rate_curr_array[index]);
    }
    else
    {
        if(count == 1)
        {
            return acin_branch_data[mark].max_rate_curr_array[index];
        }
        else
        {
            return 0.0f;
        }
    }
}

float get_smr_max_rate_curr_per_phase(int large_ps_flag, 
                                    acin_branch_data_t *acin_branch_data, 
                                    int branch_count, 
                                    int index, 
                                    float mar_rate_curr)
{
    if(large_ps_flag)
    {
        return calc_smr_max_rate_curr_in_large_ps(acin_branch_data, branch_count, index);
    }
    else
    {
        return mar_rate_curr;
    }
}

float pdt_get_valid_data(float data, float max_val, float min_val)
{
    if (data > max_val)
    {
        return max_val;
    }
    else if (data < min_val)
    {
        return min_val;
    }
    else
    {
        return data;
    }
}


/* Started by AICoder, pid:pe094o000dh62fe145d8082ea036742738149169 */
/**
 * @brief 获取两个时间点之间的天数差
 * 
 * @param p_time 输入的时间点指针
 * @return int 天数差，如果输入无效则返回FAILURE
 */
int get_diff_days(time_base_t *p_time)
{
    // 检查输入是否有效
    if (p_time == NULL || check_time_valid(*p_time) == FALSE) {
        return FAILURE;
    }

    // 获取当前时间
    time_base_t current_time;
    pdt_get_time(&current_time);

    // 使用 long long  避免整数溢出
    long long current_time_t = (long long )time_struct_2_time_t(current_time);
    long long  p_time_t = (long long )time_struct_2_time_t(*p_time);

    // 计算时间差并转换为天数
    long long  diff_seconds = current_time_t > p_time_t ? current_time_t - p_time_t : p_time_t - current_time_t;
    long long days = (diff_seconds / (24 * 3600));
    if(days > INT_MAX)
    {
        return INT_MAX;
    }

    // 将秒转换为天，返回结果
    return (int)days;
}
/* Ended by AICoder, pid:pe094o000dh62fe145d8082ea036742738149169 */


/* Started by AICoder, pid:5b816y0f2459aef1466809ebc00b9a21eb395bf3 */
/**
 * @brief 计算线性插值点。
 *
 * @param x1 第一个点的 x 坐标。
 * @param y1 第一个点的 y 坐标。
 * @param x2 第二个点的 x 坐标。
 * @param y2 第二个点的 y 坐标。
 * @param x 需要插值的 x 坐标。
 * @param py 存储插值结果的指针。
 * @return 返回 0 表示成功，返回 -1 表示 x1 和 x2 相等（无法进行插值）。
 */
int get_linear_point(float x1, float y1, float x2, float y2, float x, float* py)
{
    float m, b;

    // 检查 x1 和 x2 是否相等，避免除以零
    if (fabs(x1-x2) < FLOAT_EPSILON)
    {
        return FAILURE;
    }

    // 检查 py 是否为空指针
    if (NULL == py)
    {
        return FAILURE;
    }

    // 计算斜率 m
    m = (y2 - y1) / (x2 - x1);

    // 计算截距 b
    b = y1 - m * x1;

    // 计算插值点的 y 值
    *py = m * x + b;

    return SUCCESS;
}
/* Ended by AICoder, pid:5b816y0f2459aef1466809ebc00b9a21eb395bf3 */

/* Started by AICoder, pid:oad3cf0a215ca4a146490bde00764b1e2a878fee */
// 冒泡排序函数，按从大到小排序
int bubble_sorts(float_type_t *data, int num) {
    int i = 0;
    int j = 0;
    if (data == NULL || num <= 0) {
        return FAILURE; // 参数无效
    }

    for (i = 0; i < num - 1; i++) {
        for (j = 0; j < num - 1 - i; j++) {
            if (data[j].f_value < data[j + 1].f_value) {
                float_type_t temp = data[j];
                data[j] = data[j + 1];
                data[j + 1] = temp;
            }
        }
    }
    return SUCCESS; // 排序成功
}
/* Ended by AICoder, pid:oad3cf0a215ca4a146490bde00764b1e2a878fee */

#ifdef __cplusplus
}
#endif

